<?php
/**
 * Verification script for English quotation mark fixes
 */

require_once 'config/config.php';

echo "=== VERIFICATION OF ENGLISH QUOTATION MARK FIXES ===\n";
echo "Date: " . date('Y-m-d H:i:s') . "\n\n";

try {
    $db = Database::getInstance();
    
    // Get current translated content
    $chapter = $db->fetchOne(
        "SELECT translated_content FROM chapters WHERE novel_id = 3 AND chapter_number = 24",
        []
    );
    
    if (!$chapter) {
        echo "ERROR: Chapter not found\n";
        exit(1);
    }
    
    $content = $chapter['translated_content'];
    
    echo "1. QUOTATION MARK STATISTICS:\n";
    echo "=" . str_repeat("=", 50) . "\n";
    
    $japaneseQuotes = mb_substr_count($content, '「') + mb_substr_count($content, '」') + 
                     mb_substr_count($content, '『') + mb_substr_count($content, '』');
    $englishQuotes = substr_count($content, '"');
    
    echo "Japanese quotation marks: {$japaneseQuotes} - " . 
         ($japaneseQuotes === 0 ? "NONE FOUND ✓" : "STILL PRESENT ✗") . "\n";
    echo "English double quotes: {$englishQuotes}\n";
    echo "Content length: " . strlen($content) . " characters\n";
    
    echo "\n2. CHECKING FOR REMAINING ISSUES:\n";
    echo "=" . str_repeat("=", 50) . "\n";
    
    $issues = [];
    
    // Check for Japanese quotes
    if (mb_substr_count($content, '「') > 0) $issues[] = "Japanese opening single quotes (「) found";
    if (mb_substr_count($content, '」') > 0) $issues[] = "Japanese closing single quotes (」) found";
    if (mb_substr_count($content, '『') > 0) $issues[] = "Japanese opening double quotes (『) found";
    if (mb_substr_count($content, '』') > 0) $issues[] = "Japanese closing double quotes (』) found";
    
    // Check for consecutive quotes
    if (preg_match('/"{3,}/', $content)) $issues[] = "Multiple consecutive quotes found";
    
    // Check for reversed patterns
    if (preg_match('/」[^」「]*「/u', $content)) $issues[] = "Reversed single quote patterns still present";
    if (preg_match('/』[^』『]*『/u', $content)) $issues[] = "Reversed double quote patterns still present";
    
    if (empty($issues)) {
        echo "No issues found ✓\n";
    } else {
        echo "Issues found:\n";
        foreach ($issues as $issue) {
            echo "  - {$issue}\n";
        }
    }
    
    echo "\n3. SAMPLE OF CORRECTED DIALOGUE:\n";
    echo "=" . str_repeat("=", 50) . "\n";
    
    // Find and display dialogue examples
    preg_match_all('/"[^"]{20,100}"/', $content, $dialogueMatches);
    
    echo "Examples of corrected dialogue:\n";
    for ($i = 0; $i < min(5, count($dialogueMatches[0])); $i++) {
        echo sprintf("%d. %s\n", $i+1, $dialogueMatches[0][$i]);
    }
    
    echo "\n4. BEFORE/AFTER COMPARISON:\n";
    echo "=" . str_repeat("=", 50) . "\n";
    
    // Show examples of what was fixed
    $beforeAfterExamples = [
        "」Stop—「" => "\"Stop—\"",
        "」Aaahhhhhaaaaahhh!『" => "\"Aaahhhhhaaaaahhh!\"",
        "』Next, you'll be violated by them...「" => "\"Next, you'll be violated by them...\"",
        "『Hehehe, can't move?』" => "\"Hehehe, can't move?\"",
        "「GOB...!」" => "\"GOB...!\""
    ];
    
    echo "Examples of corrections made:\n";
    foreach ($beforeAfterExamples as $before => $after) {
        echo "  BEFORE: {$before}\n";
        echo "  AFTER:  {$after}\n";
        echo "\n";
    }
    
    echo "5. CONTENT PREVIEW (first 800 characters):\n";
    echo "=" . str_repeat("=", 50) . "\n";
    echo substr($content, 0, 800) . "...\n";
    
    echo "\n6. SUMMARY:\n";
    echo "=" . str_repeat("=", 50) . "\n";
    
    if ($japaneseQuotes === 0 && empty($issues)) {
        echo "✓ All Japanese quotation marks successfully converted to English quotes\n";
        echo "✓ No formatting issues detected\n";
        echo "✓ Content is properly formatted for English readers\n";
        echo "\nSTATUS: ENGLISH QUOTATION MARKS SUCCESSFULLY CORRECTED\n";
    } else {
        echo "✗ Some issues may still remain\n";
        if ($japaneseQuotes > 0) echo "  - Japanese quotation marks still present\n";
        if (!empty($issues)) {
            foreach ($issues as $issue) {
                echo "  - {$issue}\n";
            }
        }
    }
    
} catch (Exception $e) {
    echo "ERROR: " . $e->getMessage() . "\n";
    exit(1);
}
?>
