<?php
/**
 * API Endpoint: Single Chapter Access
 * GET /api/chapter.php?novel_id=<id>&chapter=<number> - Get specific chapter
 * This is optimized for chapter-view.php to load individual chapters efficiently
 */

require_once '../config/config.php';

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    jsonResponse(['error' => 'Method not allowed'], 405);
}

// Validate required parameters
if (!isset($_GET['novel_id']) || !is_numeric($_GET['novel_id'])) {
    jsonResponse(['error' => 'Valid novel_id parameter is required'], 400);
}

if (!isset($_GET['chapter']) || !is_numeric($_GET['chapter'])) {
    jsonResponse(['error' => 'Valid chapter parameter is required'], 400);
}

$novelId = (int)$_GET['novel_id'];
$chapterNumber = (int)$_GET['chapter'];

try {
    $db = Database::getInstance();
    
    // Get novel information
    $novel = $db->fetchOne(
        "SELECT * FROM novels WHERE id = ?",
        [$novelId]
    );
    
    if (!$novel) {
        jsonResponse(['error' => 'Novel not found'], 404);
    }
    
    // Get specific chapter
    $chapter = $db->fetchOne(
        "SELECT c.*,
                CASE WHEN cc.chapter_id IS NOT NULL THEN 1 ELSE 0 END as has_chunks,
                COUNT(cc.id) as chunk_count
         FROM chapters c
         LEFT JOIN chapter_chunks cc ON c.id = cc.chapter_id
         WHERE c.novel_id = ? AND c.chapter_number = ?
         GROUP BY c.id",
        [$novelId, $chapterNumber]
    );
    
    if (!$chapter) {
        jsonResponse(['error' => "Chapter {$chapterNumber} not found"], 404);
    }
    
    // Get name dictionary for this novel (for translation consistency)
    $nameDictionary = $db->fetchAll(
        "SELECT * FROM name_dictionary WHERE novel_id = ? ORDER BY frequency DESC",
        [$novelId]
    );
    
    // Apply formatting fix to both original and translated content if needed
    if (!empty($chapter['original_content'])) {
        $chapter['original_content'] = applyFormattingFix($chapter['original_content']);
    }
    if (!empty($chapter['translated_content'])) {
        $chapter['translated_content'] = applyFormattingFix($chapter['translated_content']);
    }

    // Return the data
    jsonResponse([
        'success' => true,
        'data' => [
            'novel' => $novel,
            'chapter' => $chapter,
            'name_dictionary' => $nameDictionary
        ]
    ]);
    
} catch (Exception $e) {
    logError('Chapter API Error: ' . $e->getMessage(), [
        'novel_id' => $novelId,
        'chapter' => $chapterNumber,
        'trace' => $e->getTraceAsString()
    ]);

    jsonResponse([
        'success' => false,
        'error' => 'Failed to load chapter: ' . $e->getMessage()
    ], 500);
}

/**
 * Apply formatting fix to translated content on-the-fly
 */
function applyFormattingFix($content) {
    if (empty($content)) {
        return $content;
    }

    $contentLength = strlen($content);
    $newlineCount = substr_count($content, "\n");
    $paragraphCount = substr_count($content, "\n\n");

    // Enhanced detection: check multiple indicators of formatting issues
    $needsFixing = false;

    // Case 1: No newlines at all in substantial content
    if ($contentLength > 300 && $newlineCount === 0) {
        $needsFixing = true;
    }
    // Case 2: Very few newlines relative to content length
    elseif ($contentLength > 800 && $newlineCount < 5) {
        $needsFixing = true;
    }
    // Case 3: No paragraph breaks in substantial content
    elseif ($contentLength > 500 && $paragraphCount === 0) {
        $needsFixing = true;
    }
    // Case 4: Too few paragraphs for the content length
    elseif ($contentLength > 1500 && $paragraphCount < 2) {
        $needsFixing = true;
    }

    if (!$needsFixing) {
        return $content;
    }

    // Apply comprehensive paragraph reconstruction
    return reconstructParagraphs($content);
}

/**
 * Comprehensive paragraph reconstruction algorithm
 */
function reconstructParagraphs($content) {
    // Normalize whitespace first
    $content = str_replace(["\r\n", "\r"], "\n", $content);
    $content = preg_replace('/[ \t]+/', ' ', $content);

    // If content has some paragraph structure, enhance it
    if (substr_count($content, "\n\n") > 0) {
        // Clean up existing paragraph breaks
        $content = preg_replace('/\n{3,}/', "\n\n", $content);
        return trim($content);
    }

    // Check if this is Chinese text with indentation patterns
    if (preg_match('/　　/', $content)) {
        // Handle Chinese text with full-width space indentation
        // Convert single line breaks before indented lines to paragraph breaks
        $content = preg_replace('/\n\s*　　/', "\n\n　　", $content);

        // Clean up multiple consecutive line breaks
        $content = preg_replace('/\n{3,}/', "\n\n", $content);

        return trim($content);
    }

    // For non-Chinese text or text without clear indentation, use the original logic
    // Remove all newlines to start fresh
    $content = str_replace("\n", " ", $content);
    $content = preg_replace('/\s+/', ' ', $content);

    // Enhanced sentence splitting with multiple patterns
    $patterns = [
        // Chinese sentence endings
        '/([。！？])\s*([「『""]|[一-龯])/u',
        // Standard sentence endings followed by capital letters
        '/([.!?])\s+([A-Z][a-z])/',
        // Dialogue endings
        '/(["\'""][\.\!\?])\s+([A-Z][a-z])/',
        // After quoted speech
        '/(["\'""])\s+([A-Z][a-z])/',
        // Time/scene transitions
        '/([.!?])\s+(Later|Meanwhile|Then|Now|After|Before|Suddenly|However|But|The next|That night|The following|Eventually|Finally|Immediately|Soon|Moments later|Minutes later|Hours later)\s+/',
        // Character actions/descriptions
        '/([.!?])\s+(He|She|It|They|The|A|An|I|We|You)\s+/',
    ];

    // Apply sentence break patterns
    foreach ($patterns as $pattern) {
        $content = preg_replace($pattern, '$1' . "\n\n" . '$2', $content);
    }

    // If we still don't have enough breaks, use length-based splitting
    if (substr_count($content, "\n\n") < 2) {
        // Split by sentence endings and reconstruct
        $sentences = preg_split('/([.!?])\s*/', $content, -1, PREG_SPLIT_DELIM_CAPTURE | PREG_SPLIT_NO_EMPTY);

        $paragraphs = [];
        $currentParagraph = '';
        $sentenceCount = 0;

        for ($i = 0; $i < count($sentences); $i += 2) {
            $sentence = trim($sentences[$i] ?? '');
            $punctuation = $sentences[$i + 1] ?? '';

            if ($sentence) {
                $fullSentence = $sentence . $punctuation;
                $currentParagraph .= ($currentParagraph ? ' ' : '') . $fullSentence;
                $sentenceCount++;

                // Create paragraph break based on multiple criteria
                $shouldBreak = false;

                // Break after 2-4 sentences
                if ($sentenceCount >= 2 && $sentenceCount <= 4) {
                    $shouldBreak = true;
                }
                // Break if current paragraph is getting long
                elseif (strlen($currentParagraph) > 400) {
                    $shouldBreak = true;
                }
                // Break if sentence is very long (likely a paragraph by itself)
                elseif (strlen($fullSentence) > 200) {
                    $shouldBreak = true;
                }

                if ($shouldBreak) {
                    $paragraphs[] = trim($currentParagraph);
                    $currentParagraph = '';
                    $sentenceCount = 0;
                }
            }
        }

        // Add any remaining content
        if (trim($currentParagraph)) {
            $paragraphs[] = trim($currentParagraph);
        }

        $content = implode("\n\n", array_filter($paragraphs));
    }

    // Final cleanup
    $content = preg_replace('/\n{3,}/', "\n\n", $content);
    $content = preg_replace('/[ \t]+/', ' ', $content);

    return trim($content);
}
?>
