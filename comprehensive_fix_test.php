<?php
require_once 'config/config.php';

echo "=== COMPREHENSIVE TRANSLATION FIXES TEST ===\n";
echo "Date: " . date('Y-m-d H:i:s') . "\n\n";

// Test data that represents the actual issues found in Chapter 163
$testData = [
    'suffix_issues' => [
        'She seemed thoroughly satisfied with her reflection (-san9).',
        'But <PERSON><PERSON> and <PERSON><PERSON> (-san8) weren\'t fooled by appearances.',
        'The girl (-chan7) was cute and innocent.',
        'He called the boy (-kun6) over to help.',
        'The master (-sama5) was very pleased.',
        'The teacher (-sensei4) explained the lesson.',
        'His senior (-senpai3) gave him advice.'
    ],
    'punctuation_issues' => [
        '」I.. just imagining her in this dress makes me.. huff huff..「',
        '」..Zeros (-san3). Is <PERSON><PERSON> (-san2) okay? He seems.. really dangerous right now..「',
        '」What was that, Master and Servant Number Two? Have you something to say to me?「',
        '」N-no, nothing really..「',
        '」Oh ho♡ This is quite splendid indeed!「'
    ]
];

echo "1. TESTING SUFFIX FIXES:\n";
echo "=" . str_repeat("=", 50) . "\n";

$honorificService = new HonorificService();

foreach ($testData['suffix_issues'] as $i => $testText) {
    echo "Test " . ($i + 1) . ":\n";
    echo "Original: {$testText}\n";
    
    // Create mock honorifics for this test
    preg_match_all('/-(\w+)(\d+)/', $testText, $matches);
    $mockHonorifics = [];
    
    for ($j = 0; $j < count($matches[0]); $j++) {
        $honorificType = $matches[1][$j];
        $number = $matches[2][$j];
        $marker = "HONORIFIC_MARKER_{$number}";
        $mockHonorifics[$marker] = [
            'original' => $matches[0][$j],
            'romanized' => '-' . $honorificType
        ];
    }
    
    $fixedText = $honorificService->postprocessTranslatedText($testText, $mockHonorifics);
    echo "Fixed:    {$fixedText}\n";
    
    // Check if fix worked
    $remainingIssues = preg_match_all('/-\w+\d+/', $fixedText);
    echo "Status:   " . ($remainingIssues === 0 ? "✓ FIXED" : "✗ STILL HAS ISSUES") . "\n\n";
}

echo "2. TESTING PUNCTUATION FIXES:\n";
echo "=" . str_repeat("=", 50) . "\n";

$translationService = new TranslationService();

// Use reflection to access private method
$reflection = new ReflectionClass($translationService);
$fixPunctuationMethod = $reflection->getMethod('fixPunctuationIssues');
$fixPunctuationMethod->setAccessible(true);

foreach ($testData['punctuation_issues'] as $i => $testText) {
    echo "Test " . ($i + 1) . ":\n";
    echo "Original: {$testText}\n";
    
    $fixedText = $fixPunctuationMethod->invoke($translationService, $testText, $testText);
    echo "Fixed:    {$fixedText}\n";
    
    // Check if fix worked
    $remainingIssues = preg_match_all('/」[^「]*「/', $fixedText);
    echo "Status:   " . ($remainingIssues === 0 ? "✓ FIXED" : "✗ STILL HAS ISSUES") . "\n\n";
}

echo "3. TESTING COMBINED FIXES ON COMPLEX TEXT:\n";
echo "=" . str_repeat("=", 50) . "\n";

$complexText = '」Oh ho♡ This is quite splendid indeed!「

Spinning gleefully before the mirror in a black gothic lolita dress with princess sleeves was none other than Alphiea Magus.

She seemed thoroughly satisfied with her reflection - the golden horns protruding from her head and the wings sprouting from her back gave her the distinct appearance of a fallen angel or demon, making her look oddly.. （-san9）.

Though she」d been a little girl in the cultivation fluid, she now stood about as tall as a middle schooler, creating an utterly heartwarming sight.

」...(Loli granny)「

」What was that, Master and Servant Number Two? Have you something to say to me?「

」N-no, nothing really..「

But Zeros and Ado (-san8), who knew her true nature, weren」t fooled by appearances and muttered their honest opinions under their breaths.';

echo "Original complex text:\n";
echo $complexText . "\n\n";

// Count original issues
$originalSuffixIssues = preg_match_all('/-\w+\d+/', $complexText);
$originalPunctuationIssues = preg_match_all('/」[^「]*「/', $complexText);

echo "Original issues:\n";
echo "- Suffix issues: {$originalSuffixIssues}\n";
echo "- Punctuation issues: {$originalPunctuationIssues}\n\n";

// Apply suffix fixes
$mockHonorifics = [
    'HONORIFIC_MARKER_9' => ['original' => '-san9', 'romanized' => '-san'],
    'HONORIFIC_MARKER_8' => ['original' => '-san8', 'romanized' => '-san']
];

$fixedText = $honorificService->postprocessTranslatedText($complexText, $mockHonorifics);

// Apply punctuation fixes
$fixedText = $fixPunctuationMethod->invoke($translationService, $complexText, $fixedText);

echo "Fixed complex text:\n";
echo $fixedText . "\n\n";

// Count remaining issues
$remainingSuffixIssues = preg_match_all('/-\w+\d+/', $fixedText);
$remainingPunctuationIssues = preg_match_all('/」[^「]*「/', $fixedText);

echo "Remaining issues:\n";
echo "- Suffix issues: {$remainingSuffixIssues}\n";
echo "- Punctuation issues: {$remainingPunctuationIssues}\n\n";

$suffixImprovement = $originalSuffixIssues - $remainingSuffixIssues;
$punctuationImprovement = $originalPunctuationIssues - $remainingPunctuationIssues;

echo "SUMMARY:\n";
echo "=" . str_repeat("=", 50) . "\n";
echo "Suffix issues fixed: {$suffixImprovement} out of {$originalSuffixIssues}\n";
echo "Punctuation issues fixed: {$punctuationImprovement} out of {$originalPunctuationIssues}\n";

if ($suffixImprovement > 0 && $punctuationImprovement > 0) {
    echo "\n✓ BOTH FIXES ARE WORKING CORRECTLY!\n";
    echo "The translation system has been successfully improved.\n";
} elseif ($suffixImprovement > 0) {
    echo "\n✓ SUFFIX FIXES ARE WORKING!\n";
    echo "⚠ Punctuation fixes may need further refinement.\n";
} elseif ($punctuationImprovement > 0) {
    echo "\n✓ PUNCTUATION FIXES ARE WORKING!\n";
    echo "⚠ Suffix fixes may need further refinement.\n";
} else {
    echo "\n✗ FIXES NEED ADJUSTMENT\n";
}

echo "\n=== TEST COMPLETE ===\n";
?>
