<?php
/**
 * Examine translated content for Japanese quotation mark issues
 */

require_once 'config/config.php';

echo "=== EXAMINING TRANSLATED CONTENT - CHAPTER 24 ===\n";
echo "Date: " . date('Y-m-d H:i:s') . "\n\n";

try {
    $db = Database::getInstance();
    
    // Get chapter 24 translated content
    $chapter = $db->fetchOne(
        "SELECT id, translated_content FROM chapters WHERE novel_id = 3 AND chapter_number = 24",
        []
    );
    
    if (!$chapter) {
        echo "ERROR: Chapter not found\n";
        exit(1);
    }
    
    $translatedContent = $chapter['translated_content'];
    
    echo "1. TRANSLATED CONTENT ANALYSIS:\n";
    echo "=" . str_repeat("=", 50) . "\n";
    echo "Content length: " . strlen($translatedContent) . " characters\n";
    
    // Count Japanese quotation marks in English text
    $singleOpen = mb_substr_count($translatedContent, '「');
    $singleClose = mb_substr_count($translatedContent, '」');
    $doubleOpen = mb_substr_count($translatedContent, '『');
    $doubleClose = mb_substr_count($translatedContent, '』');
    $englishQuotes = substr_count($translatedContent, '"');
    
    echo "Japanese single quotes 「」: {$singleOpen} open, {$singleClose} close\n";
    echo "Japanese double quotes 『』: {$doubleOpen} open, {$doubleClose} close\n";
    echo "English double quotes \": {$englishQuotes}\n";
    
    if ($singleOpen > 0 || $singleClose > 0 || $doubleOpen > 0 || $doubleClose > 0) {
        echo "WARNING: Japanese quotation marks found in English translation!\n";
    }
    
    echo "\n2. FINDING PROBLEMATIC PATTERNS:\n";
    echo "=" . str_repeat("=", 50) . "\n";
    
    // Find all Japanese quotation marks with context
    $issues = [];
    $offset = 0;
    
    foreach (['「', '」', '『', '』'] as $mark) {
        $offset = 0;
        while (($pos = strpos($translatedContent, $mark, $offset)) !== false) {
            $start = max(0, $pos - 30);
            $length = 60;
            $context = substr($translatedContent, $start, $length);
            $context = str_replace($mark, "**{$mark}**", $context);
            $issues[] = [
                'mark' => $mark,
                'pos' => $pos,
                'context' => $context
            ];
            $offset = $pos + 1;
        }
    }
    
    // Sort by position
    usort($issues, function($a, $b) { return $a['pos'] - $b['pos']; });
    
    echo "Found " . count($issues) . " Japanese quotation marks in English text:\n";
    foreach ($issues as $i => $issue) {
        echo sprintf("%2d. %s at pos %d: ...%s...\n", $i+1, $issue['mark'], $issue['pos'], trim($issue['context']));
    }
    
    echo "\n3. SAMPLE OF TRANSLATED CONTENT (first 1000 characters):\n";
    echo "=" . str_repeat("=", 50) . "\n";
    echo substr($translatedContent, 0, 1000) . "\n";
    if (strlen($translatedContent) > 1000) {
        echo "... (truncated)\n";
    }
    
    echo "\n4. LOOKING FOR SPECIFIC CORRUPTION PATTERNS:\n";
    echo "=" . str_repeat("=", 50) . "\n";
    
    // Look for specific patterns mentioned in the request
    $corruptionPatterns = [
        '/」[^」]*「/' => 'Reversed quotes pattern (」...「)',
        '/』[^』]*『/' => 'Reversed double quotes pattern (』...『)',
        '/[」』][A-Za-z]/' => 'Japanese closing quote followed by English text',
        '/[A-Za-z][「『]/' => 'English text followed by Japanese opening quote',
        '/[」』][A-Z]/' => 'Japanese closing quote followed by capitalized English',
        '/[!?][「『]/' => 'Punctuation followed by Japanese opening quote'
    ];
    
    foreach ($corruptionPatterns as $pattern => $description) {
        if (preg_match_all($pattern, $translatedContent, $matches, PREG_OFFSET_CAPTURE)) {
            echo "FOUND: {$description} - " . count($matches[0]) . " instances\n";
            foreach (array_slice($matches[0], 0, 3) as $match) { // Show first 3 examples
                $pos = $match[1];
                $start = max(0, $pos - 20);
                $length = 40;
                $context = substr($translatedContent, $start, $length);
                echo "  Example: ...{$context}...\n";
            }
        }
    }
    
} catch (Exception $e) {
    echo "ERROR: " . $e->getMessage() . "\n";
    exit(1);
}
?>
