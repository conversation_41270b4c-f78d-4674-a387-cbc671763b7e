<?php
/**
 * Test Enhanced Logging
 * Simple test to verify our enhanced logging and conflict resolution is working
 */

require_once 'config/config.php';
require_once 'config/database.php';
require_once 'classes/NameSubstitutionService.php';

echo "=== Enhanced Logging Test ===\n\n";

try {
    $nameService = new NameSubstitutionService();
    
    // Test with names that will actually trigger processing
    echo "Test: Name substitution with matching original text\n";
    
    $testNames = [
        [
            'original_name' => '<PERSON>',
            'translation' => 'John-kun',
            'name_type' => 'character',
            'frequency' => 10,
            'is_verified' => true
        ],
        [
            'original_name' => '<PERSON>',
            'translation' => 'Mary-chan',
            'name_type' => 'character',
            'frequency' => 8,
            'is_verified' => true
        ]
    ];
    
    // Test text where AI might have translated names differently
    $testText = "<PERSON> spoke to <PERSON> about the plan. <PERSON> was also there with <PERSON>.";
    $originalText = "<PERSON> spoke to <PERSON> about the plan. <PERSON> was also there with <PERSON>.";
    
    echo "Original Japanese: {$originalText}\n";
    echo "AI Translation: {$testText}\n";
    
    $result = $nameService->applyNameSubstitutions($testText, $testNames, $originalText);
    
    echo "Final Result: {$result['processed_text']}\n";
    echo "Success: " . ($result['success'] ? 'Yes' : 'No') . "\n";
    echo "Substitutions made: " . count($result['substitutions']) . "\n";
    
    if (!empty($result['substitutions'])) {
        echo "Substitution details:\n";
        foreach ($result['substitutions'] as $sub) {
            echo "  - {$sub['original_name']} → {$sub['target_name']} ";
            echo "(variations: " . implode(', ', $sub['found_variations']) . ", ";
            echo "count: {$sub['replacement_count']})\n";
        }
    }
    
    echo "\n";
    
    // Test 2: Conflicting names
    echo "Test 2: Conflicting names with priority resolution\n";
    
    $conflictNames = [
        [
            'original_name' => 'Alex',
            'translation' => 'Alex-kun',
            'name_type' => 'character',
            'frequency' => 20,
            'is_verified' => true
        ],
        [
            'original_name' => 'Alexander',
            'translation' => 'Alexander-sama',
            'name_type' => 'character',
            'frequency' => 15,
            'is_verified' => true
        ]
    ];
    
    $testText2 = "Alexandre spoke to Alex about Alexander's plan.";
    $originalText2 = "Alex spoke to Alex about Alexander's plan.";
    
    echo "Original: {$originalText2}\n";
    echo "AI Translation: {$testText2}\n";
    
    $result2 = $nameService->applyNameSubstitutions($testText2, $conflictNames, $originalText2);
    
    echo "Final Result: {$result2['processed_text']}\n";
    echo "Substitutions made: " . count($result2['substitutions']) . "\n";
    
    if (!empty($result2['substitutions'])) {
        echo "Substitution details:\n";
        foreach ($result2['substitutions'] as $sub) {
            echo "  - {$sub['original_name']} → {$sub['target_name']} ";
            echo "(variations: " . implode(', ', $sub['found_variations']) . ", ";
            echo "count: {$sub['replacement_count']})\n";
        }
    }
    
    echo "\n";
    
    // Test 3: Fuzzy matching
    echo "Test 3: Fuzzy matching with improved thresholds\n";
    
    $fuzzyNames = [
        [
            'original_name' => 'Sarah',
            'translation' => 'Sarah-chan',
            'name_type' => 'character',
            'frequency' => 12
        ]
    ];
    
    $fuzzyText = "Sara spoke to Sera about Sarah's plan. The girl named Sasha was also there.";
    
    echo "Text for fuzzy matching: {$fuzzyText}\n";
    
    $fuzzyMatches = $nameService->findFuzzyMatches($fuzzyText, $fuzzyNames);
    
    echo "Fuzzy matches found: " . count($fuzzyMatches) . "\n";
    foreach ($fuzzyMatches as $match) {
        echo "  - Found: '{$match['found_text']}' → '{$match['target_name']}' ";
        echo "(similarity: " . number_format($match['similarity'], 3) . ", ";
        echo "confidence: " . number_format($match['confidence'], 3) . ")\n";
    }
    
    echo "\n=== Enhanced Logging Test Complete ===\n";
    echo "Check debug.log for detailed processing information.\n";
    
} catch (Exception $e) {
    echo "❌ Error during testing: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
?>
