<?php
/**
 * Re-translate chapter 24 to test the 君 fix
 */

require_once 'config/config.php';

echo "=== RE-TRANSLATING CHAPTER 24 TO TEST 君 FIX ===\n";
echo "Date: " . date('Y-m-d H:i:s') . "\n\n";

try {
    $novelManager = new NovelManager();
    $db = Database::getInstance();
    
    // Get current chapter 24 info
    $chapter = $db->fetchOne(
        "SELECT * FROM chapters WHERE novel_id = 3 AND chapter_number = 24",
        []
    );
    
    if (!$chapter) {
        echo "ERROR: Chapter 24 not found\n";
        exit(1);
    }
    
    echo "BEFORE RETRANSLATION:\n";
    echo "=" . str_repeat("=", 50) . "\n";
    echo "Chapter ID: {$chapter['id']}\n";
    echo "Original Title: {$chapter['original_title']}\n";
    echo "Translated Title: {$chapter['translated_title']}\n";
    echo "Translation Status: {$chapter['translation_status']}\n";
    
    // Check for standalone -kun in current translation
    if (!empty($chapter['translated_content'])) {
        $standaloneKunCount = preg_match_all('/(?<![a-zA-Z])-kun(?![a-zA-Z])/', $chapter['translated_content']);
        echo "Current standalone '-kun' count: {$standaloneKunCount}\n";
        
        if ($standaloneKunCount > 0) {
            echo "❌ Current translation has standalone '-kun' issues\n";
        } else {
            echo "✅ Current translation has no standalone '-kun' issues\n";
        }
    }
    
    echo "\nRE-TRANSLATING CHAPTER...\n";
    echo "=" . str_repeat("=", 50) . "\n";
    
    // Re-translate the chapter
    $result = $novelManager->translateChapter(3, 24, 'en');
    
    if (!$result['success']) {
        echo "ERROR: Translation failed - {$result['error']}\n";
        exit(1);
    }
    
    echo "✅ Translation completed successfully!\n\n";
    
    // Get the updated chapter
    $updatedChapter = $db->fetchOne(
        "SELECT * FROM chapters WHERE novel_id = 3 AND chapter_number = 24",
        []
    );
    
    echo "AFTER RETRANSLATION:\n";
    echo "=" . str_repeat("=", 50) . "\n";
    echo "Translation Status: {$updatedChapter['translation_status']}\n";
    echo "Translated Content Length: " . strlen($updatedChapter['translated_content']) . " characters\n";
    
    // Check for standalone -kun in new translation
    $newStandaloneKunCount = preg_match_all('/(?<![a-zA-Z])-kun(?![a-zA-Z])/', $updatedChapter['translated_content']);
    echo "New standalone '-kun' count: {$newStandaloneKunCount}\n";
    
    // Check for proper name-kun combinations
    $properNameKunCount = preg_match_all('/[A-Za-z]+-kun/', $updatedChapter['translated_content']);
    echo "Proper 'Name-kun' combinations: {$properNameKunCount}\n";
    
    // Look for "you" translations (should have increased)
    $youCount = preg_match_all('/\byou\b/i', $updatedChapter['translated_content']);
    echo "Occurrences of 'you': {$youCount}\n";
    
    if ($newStandaloneKunCount === 0) {
        echo "\n🎉 SUCCESS! No more standalone '-kun' issues!\n";
    } else {
        echo "\n❌ ISSUE: Still has {$newStandaloneKunCount} standalone '-kun' occurrences\n";
        
        // Show contexts of remaining issues
        echo "\nRemaining standalone '-kun' contexts:\n";
        $positions = [];
        $offset = 0;
        while (($pos = strpos($updatedChapter['translated_content'], '-kun', $offset)) !== false) {
            $positions[] = $pos;
            $offset = $pos + 1;
        }
        
        foreach (array_slice($positions, 0, 3) as $i => $pos) { // Show first 3
            $start = max(0, $pos - 50);
            $length = 100;
            $context = substr($updatedChapter['translated_content'], $start, $length);
            echo "  " . ($i + 1) . ". ...{$context}...\n";
        }
    }
    
    // Compare specific contexts that were problematic before
    echo "\nCOMPARING SPECIFIC CONTEXTS:\n";
    echo "=" . str_repeat("=", 50) . "\n";
    
    $originalContent = $chapter['original_content'];
    $translatedContent = $updatedChapter['translated_content'];
    
    // Find the specific 君 contexts from chapter 24
    $kunContexts = [
        'これから君は' => 'From now on, you will',
        '君は材料なんだ' => 'You are material',
        'ゲームオーバーだよ、君' => 'Game over, you'
    ];
    
    foreach ($kunContexts as $originalPhrase => $expectedTranslation) {
        if (mb_strpos($originalContent, $originalPhrase) !== false) {
            echo "Original: {$originalPhrase}\n";
            echo "Expected: {$expectedTranslation}\n";
            
            // Try to find the corresponding translation
            $found = false;
            if (stripos($translatedContent, 'from now on') !== false && stripos($translatedContent, 'you will') !== false) {
                echo "✅ Found 'From now on, you will' pattern\n";
                $found = true;
            } elseif (stripos($translatedContent, 'you are material') !== false) {
                echo "✅ Found 'You are material' pattern\n";
                $found = true;
            } elseif (stripos($translatedContent, 'game over') !== false && stripos($translatedContent, 'you') !== false) {
                echo "✅ Found 'Game over, you' pattern\n";
                $found = true;
            }
            
            if (!$found) {
                echo "❓ Could not verify this specific translation\n";
            }
            echo "\n";
        }
    }
    
} catch (Exception $e) {
    echo "ERROR: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

echo "=== RETRANSLATION TEST COMPLETE ===\n";
?>
