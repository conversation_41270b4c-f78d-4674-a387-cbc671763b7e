<?php
/**
 * Simple integration test for name substitution
 */

require_once 'config/config.php';

echo "=== SIMPLE NAME SUBSTITUTION INTEGRATION TEST - " . date('Y-m-d H:i:s') . " ===\n";

try {
    $translationService = new TranslationService();
    
    // Test with a simple sentence that should work quickly
    $testText = '死王の蟲とエルマが話した。';
    echo "Test text: {$testText}\n";
    
    // Get name dictionary for novel 3
    $nameDictionary = $translationService->getNameDictionary(3);
    echo "Found " . count($nameDictionary) . " names in dictionary\n";
    
    // Create context with name dictionary
    $context = [
        'type' => 'chapter',
        'names' => $nameDictionary
    ];
    
    echo "\n=== Testing with Name Dictionary ===\n";
    $result = $translationService->translateText($testText, 'en', 'auto', $context);
    
    if ($result['success']) {
        echo "Translation successful!\n";
        echo "Original: {$testText}\n";
        echo "Translated: {$result['translated_text']}\n";
        echo "API used: " . ($result['api_used'] ?? 'unknown') . "\n";
        
        // Check for expected names
        $expectedNames = ['Insect(s) of the Death King', 'Elma'];
        $foundNames = [];
        
        foreach ($expectedNames as $expectedName) {
            if (stripos($result['translated_text'], $expectedName) !== false) {
                $foundNames[] = $expectedName;
                echo "✓ Found: {$expectedName}\n";
            } else {
                echo "✗ Missing: {$expectedName}\n";
            }
        }
        
        if (count($foundNames) === count($expectedNames)) {
            echo "✅ SUCCESS: All expected names found!\n";
        } else {
            echo "⚠️  PARTIAL: " . count($foundNames) . "/" . count($expectedNames) . " names found\n";
        }
        
    } else {
        echo "Translation failed: {$result['error']}\n";
    }
    
    echo "\n=== Testing without Name Dictionary ===\n";
    $contextWithoutNames = ['type' => 'chapter'];
    $resultWithoutNames = $translationService->translateText($testText, 'en', 'auto', $contextWithoutNames);
    
    if ($resultWithoutNames['success']) {
        echo "Translation without names: {$resultWithoutNames['translated_text']}\n";
        echo "API used: " . ($resultWithoutNames['api_used'] ?? 'unknown') . "\n";
        
        if ($result['success'] && $result['translated_text'] !== $resultWithoutNames['translated_text']) {
            echo "✅ SUCCESS: Translations are different, name dictionary is working!\n";
        } else {
            echo "⚠️  WARNING: Translations are similar, name dictionary effect unclear\n";
        }
    } else {
        echo "Translation without names failed: {$resultWithoutNames['error']}\n";
    }
    
    // Test the name substitution service directly on a known problematic case
    echo "\n=== Testing Direct Name Substitution ===\n";
    
    $nameSubstitutionService = new NameSubstitutionService();
    $problematicText = "The Insects of the Death King and Elma talked.";
    echo "Testing substitution on: {$problematicText}\n";
    
    $substitutionResult = $nameSubstitutionService->applyNameSubstitutions(
        $problematicText,
        $nameDictionary,
        $testText
    );
    
    if ($substitutionResult['success']) {
        echo "Before substitution: {$substitutionResult['original_text']}\n";
        echo "After substitution:  {$substitutionResult['processed_text']}\n";
        echo "Substitutions made:  {$substitutionResult['substitution_count']}\n";
        
        if ($substitutionResult['substitution_count'] > 0) {
            echo "✅ SUCCESS: Name substitution is working!\n";
            foreach ($substitutionResult['substitutions'] as $sub) {
                echo "  - {$sub['original_name']} → {$sub['target_name']}\n";
            }
        } else {
            echo "ℹ️  INFO: No substitutions needed (names already correct)\n";
        }
    } else {
        echo "Name substitution failed: {$substitutionResult['error']}\n";
    }
    
} catch (Exception $e) {
    echo "Test failed with exception: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}

echo "\n=== Simple Integration Test Complete ===\n";
?>
