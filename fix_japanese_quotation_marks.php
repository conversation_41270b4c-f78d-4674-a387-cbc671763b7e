<?php
/**
 * Japanese Quotation Mark Correction Script
 * Fixes positioning errors and inappropriate usage of Japanese quotation marks
 * 「」(single quotes) and 『』(double quotes) in novel content
 */

require_once 'config/config.php';

class JapaneseQuotationMarkFixer {
    
    /**
     * Fix Japanese quotation marks in text
     * @param string $text The text to fix
     * @return array ['fixed_text' => string, 'changes' => array]
     */
    public function fixQuotationMarks($text) {
        $changes = [];
        $originalText = $text;
        
        // Step 1: Fix consecutive quotation marks (e.g., 「「「「「GOB」」」」」)
        $text = $this->fixConsecutiveQuotes($text, $changes);
        
        // Step 2: Balance unmatched quotation marks
        $text = $this->balanceQuotes($text, $changes);
        
        // Step 3: Fix improper nesting
        $text = $this->fixNesting($text, $changes);
        
        // Step 4: Apply Japanese typography rules
        $text = $this->applyTypographyRules($text, $changes);
        
        return [
            'fixed_text' => $text,
            'changes' => $changes,
            'original_length' => mb_strlen($originalText),
            'fixed_length' => mb_strlen($text)
        ];
    }
    
    /**
     * Fix consecutive quotation marks that appear to be formatting errors
     */
    private function fixConsecutiveQuotes($text, &$changes) {
        // Pattern: Multiple consecutive opening quotes followed by content and multiple closing quotes
        // Example: 「「「「「GOB」」」」」 should become 「GOB」
        
        $patterns = [
            // Multiple single quotes
            '/「{2,}([^」]+)」{2,}/u' => '「$1」',
            // Multiple double quotes  
            '/『{2,}([^』]+)』{2,}/u' => '『$1』',
            // Mixed consecutive quotes - prioritize the content type
            '/[「『]{2,}([^」』]+)[」』]{2,}/u' => function($matches) {
                $content = $matches[1];
                // Use single quotes for short exclamations, double quotes for longer speech
                if (mb_strlen($content) <= 10 && preg_match('/^[A-Z]+[!?]*$/u', $content)) {
                    return '「' . $content . '」';
                } else {
                    return '『' . $content . '』';
                }
            }
        ];
        
        foreach ($patterns as $pattern => $replacement) {
            if (is_callable($replacement)) {
                $newText = preg_replace_callback($pattern, $replacement, $text);
            } else {
                $newText = preg_replace($pattern, $replacement, $text);
            }
            
            if ($newText !== $text) {
                $changes[] = [
                    'type' => 'consecutive_quotes',
                    'pattern' => $pattern,
                    'count' => preg_match_all($pattern, $text)
                ];
                $text = $newText;
            }
        }
        
        return $text;
    }
    
    /**
     * Balance unmatched quotation marks
     */
    private function balanceQuotes($text, &$changes) {
        // Count quotes
        $singleOpen = mb_substr_count($text, '「');
        $singleClose = mb_substr_count($text, '」');
        $doubleOpen = mb_substr_count($text, '『');
        $doubleClose = mb_substr_count($text, '』');
        
        // Fix unbalanced single quotes
        if ($singleOpen !== $singleClose) {
            $diff = $singleOpen - $singleClose;
            if ($diff > 0) {
                // More opening than closing - add closing quotes at appropriate positions
                $text = $this->addMissingClosingQuotes($text, '「', '」', $diff);
                $changes[] = [
                    'type' => 'balance_single_quotes',
                    'added_closing' => $diff
                ];
            } else {
                // More closing than opening - add opening quotes at appropriate positions
                $text = $this->addMissingOpeningQuotes($text, '「', '」', abs($diff));
                $changes[] = [
                    'type' => 'balance_single_quotes',
                    'added_opening' => abs($diff)
                ];
            }
        }
        
        // Fix unbalanced double quotes
        if ($doubleOpen !== $doubleClose) {
            $diff = $doubleOpen - $doubleClose;
            if ($diff > 0) {
                $text = $this->addMissingClosingQuotes($text, '『', '』', $diff);
                $changes[] = [
                    'type' => 'balance_double_quotes',
                    'added_closing' => $diff
                ];
            } else {
                $text = $this->addMissingOpeningQuotes($text, '『', '』', abs($diff));
                $changes[] = [
                    'type' => 'balance_double_quotes',
                    'added_opening' => abs($diff)
                ];
            }
        }
        
        return $text;
    }
    
    /**
     * Add missing closing quotes at appropriate positions
     */
    private function addMissingClosingQuotes($text, $openQuote, $closeQuote, $count) {
        $lines = explode("\n", $text);
        $addedCount = 0;
        
        for ($i = count($lines) - 1; $i >= 0 && $addedCount < $count; $i--) {
            $line = $lines[$i];
            
            // Look for lines that have opening quotes but no closing quotes
            $openCount = mb_substr_count($line, $openQuote);
            $closeCount = mb_substr_count($line, $closeQuote);
            
            if ($openCount > $closeCount) {
                // Add closing quote at the end of the line (before punctuation if present)
                if (preg_match('/([。！？])$/u', $line, $matches)) {
                    $lines[$i] = mb_substr($line, 0, -1) . $closeQuote . $matches[1];
                } else {
                    $lines[$i] = $line . $closeQuote;
                }
                $addedCount++;
            }
        }
        
        return implode("\n", $lines);
    }
    
    /**
     * Add missing opening quotes at appropriate positions
     */
    private function addMissingOpeningQuotes($text, $openQuote, $closeQuote, $count) {
        $lines = explode("\n", $text);
        $addedCount = 0;
        
        for ($i = 0; $i < count($lines) && $addedCount < $count; $i++) {
            $line = $lines[$i];
            
            // Look for lines that have closing quotes but no opening quotes
            $openCount = mb_substr_count($line, $openQuote);
            $closeCount = mb_substr_count($line, $closeQuote);
            
            if ($closeCount > $openCount) {
                // Add opening quote at the beginning of dialogue
                if (preg_match('/^(\s*)([^「『])/u', $line, $matches)) {
                    $lines[$i] = $matches[1] . $openQuote . $matches[2] . mb_substr($line, mb_strlen($matches[0]));
                    $addedCount++;
                }
            }
        }
        
        return implode("\n", $lines);
    }
    
    /**
     * Fix improper nesting of quotes
     */
    private function fixNesting($text, &$changes) {
        // Japanese typography rule: Use 『』 for outer quotes and 「」 for inner quotes
        // Find nested quotes and ensure proper hierarchy
        
        $nestingFixed = 0;
        
        // Pattern: 「...『...』...」 should become 『...「...」...』
        $text = preg_replace_callback('/「([^」]*?)『([^』]*?)』([^」]*?)」/u', function($matches) use (&$nestingFixed) {
            $nestingFixed++;
            return '『' . $matches[1] . '「' . $matches[2] . '」' . $matches[3] . '』';
        }, $text);
        
        if ($nestingFixed > 0) {
            $changes[] = [
                'type' => 'fix_nesting',
                'count' => $nestingFixed
            ];
        }
        
        return $text;
    }
    
    /**
     * Apply Japanese typography rules
     */
    private function applyTypographyRules($text, &$changes) {
        $rulesApplied = 0;
        
        // Rule 1: Short exclamations and sound effects should use single quotes
        $text = preg_replace_callback('/『([A-Z]{1,10}[!?]*)』/u', function($matches) use (&$rulesApplied) {
            $rulesApplied++;
            return '「' . $matches[1] . '」';
        }, $text);
        
        // Rule 2: Longer dialogue should use double quotes for main speech
        // This is more complex and context-dependent, so we'll be conservative
        
        if ($rulesApplied > 0) {
            $changes[] = [
                'type' => 'typography_rules',
                'count' => $rulesApplied
            ];
        }
        
        return $text;
    }
    
    /**
     * Analyze quotation marks in text
     */
    public function analyzeQuotationMarks($text) {
        $analysis = [
            'single_open' => mb_substr_count($text, '「'),
            'single_close' => mb_substr_count($text, '」'),
            'double_open' => mb_substr_count($text, '『'),
            'double_close' => mb_substr_count($text, '』'),
            'total_quotes' => 0,
            'balanced' => true,
            'issues' => []
        ];
        
        $analysis['total_quotes'] = $analysis['single_open'] + $analysis['single_close'] + 
                                   $analysis['double_open'] + $analysis['double_close'];
        
        // Check balance
        if ($analysis['single_open'] !== $analysis['single_close']) {
            $analysis['balanced'] = false;
            $analysis['issues'][] = 'Unbalanced single quotes: ' . $analysis['single_open'] . ' open, ' . $analysis['single_close'] . ' close';
        }
        
        if ($analysis['double_open'] !== $analysis['double_close']) {
            $analysis['balanced'] = false;
            $analysis['issues'][] = 'Unbalanced double quotes: ' . $analysis['double_open'] . ' open, ' . $analysis['double_close'] . ' close';
        }
        
        // Check for consecutive quotes
        if (preg_match('/[「『]{2,}/u', $text) || preg_match('/[」』]{2,}/u', $text)) {
            $analysis['issues'][] = 'Consecutive quotation marks found';
        }
        
        return $analysis;
    }
}

// Main execution
if (basename(__FILE__) === basename($_SERVER['SCRIPT_NAME'])) {
    echo "=== JAPANESE QUOTATION MARK FIXER ===\n";
    echo "Date: " . date('Y-m-d H:i:s') . "\n\n";
    
    try {
        $db = Database::getInstance();
        $fixer = new JapaneseQuotationMarkFixer();
        
        // Get chapter 24 content
        $chapter = $db->fetchOne(
            "SELECT id, original_content FROM chapters WHERE novel_id = 3 AND chapter_number = 24",
            []
        );
        
        if (!$chapter) {
            echo "ERROR: Chapter 24 not found\n";
            exit(1);
        }
        
        echo "1. ANALYZING CURRENT QUOTATION MARKS:\n";
        echo "=" . str_repeat("=", 50) . "\n";
        
        $analysis = $fixer->analyzeQuotationMarks($chapter['original_content']);
        foreach ($analysis as $key => $value) {
            if ($key === 'issues') {
                echo "Issues found:\n";
                foreach ($value as $issue) {
                    echo "  - {$issue}\n";
                }
            } else {
                echo "{$key}: {$value}\n";
            }
        }
        
        if ($analysis['balanced'] && empty($analysis['issues'])) {
            echo "\nNo issues found. Content appears to be correctly formatted.\n";
            exit(0);
        }
        
        echo "\n2. APPLYING FIXES:\n";
        echo "=" . str_repeat("=", 50) . "\n";
        
        $result = $fixer->fixQuotationMarks($chapter['original_content']);
        
        echo "Changes made:\n";
        foreach ($result['changes'] as $change) {
            echo "  - {$change['type']}";
            if (isset($change['count'])) echo " (count: {$change['count']})";
            if (isset($change['added_closing'])) echo " (added {$change['added_closing']} closing quotes)";
            if (isset($change['added_opening'])) echo " (added {$change['added_opening']} opening quotes)";
            echo "\n";
        }
        
        echo "\nText length: {$result['original_length']} -> {$result['fixed_length']}\n";
        
        // Verify the fixes
        echo "\n3. VERIFYING FIXES:\n";
        echo "=" . str_repeat("=", 50) . "\n";
        
        $newAnalysis = $fixer->analyzeQuotationMarks($result['fixed_text']);
        foreach ($newAnalysis as $key => $value) {
            if ($key === 'issues') {
                if (empty($value)) {
                    echo "Issues: None found\n";
                } else {
                    echo "Remaining issues:\n";
                    foreach ($value as $issue) {
                        echo "  - {$issue}\n";
                    }
                }
            } else {
                echo "{$key}: {$value}\n";
            }
        }
        
        // Ask for confirmation before updating
        echo "\n4. UPDATE DATABASE?\n";
        echo "=" . str_repeat("=", 50) . "\n";
        echo "Do you want to update the database with the fixed content? (y/N): ";
        $handle = fopen("php://stdin", "r");
        $line = fgets($handle);
        fclose($handle);
        
        if (trim(strtolower($line)) === 'y') {
            $db->update(
                'chapters',
                ['original_content' => $result['fixed_text']],
                'id = ?',
                [$chapter['id']]
            );
            echo "Database updated successfully!\n";
        } else {
            echo "Database not updated. Fixed content available in result.\n";
        }
        
    } catch (Exception $e) {
        echo "ERROR: " . $e->getMessage() . "\n";
        exit(1);
    }
}
