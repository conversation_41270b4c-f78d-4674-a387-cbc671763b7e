<?php
require_once 'config/config.php';

echo "=== TESTING TRANSLATION FIXES ===\n";
echo "Date: " . date('Y-m-d H:i:s') . "\n\n";

// Test the honorific fix
echo "1. TESTING HONORIFIC FIX:\n";
echo "=" . str_repeat("=", 40) . "\n";

$honorificService = new HonorificService();

// Simulate corrupted honorific markers
$testText = "She seemed thoroughly satisfied with her reflection (-san9). But <PERSON><PERSON> and <PERSON><PERSON> (-san8) weren't fooled. The girl (-chan7) was cute. He called him (-kun6).";

echo "Original text with corrupted markers:\n";
echo $testText . "\n\n";

// Create mock honorifics array
$mockHonorifics = [
    'HONORIFIC_MARKER_9' => ['original' => 'さん', 'romanized' => '-san'],
    'HONORIFIC_MARKER_8' => ['original' => 'さん', 'romanized' => '-san'],
    'HONORIFIC_MARKER_7' => ['original' => 'ちゃん', 'romanized' => '-chan'],
    'HONOR<PERSON>IC_MARKER_6' => ['original' => 'くん', 'romanized' => '-kun']
];

$fixedText = $honorificService->postprocessTranslatedText($testText, $mockHonorifics);

echo "Fixed text:\n";
echo $fixedText . "\n\n";

// Test punctuation fix
echo "2. TESTING PUNCTUATION FIX:\n";
echo "=" . str_repeat("=", 40) . "\n";

$translationService = new TranslationService();

// Simulate reversed dialogue punctuation
$testDialogue = "」I.. just imagining her in this dress makes me.. huff huff..「\n」..Zeros (-san3). Is Creston (-san2) okay? He seems.. really dangerous right now..「";

echo "Original text with reversed punctuation:\n";
echo $testDialogue . "\n\n";

// Use reflection to access private method for testing
$reflection = new ReflectionClass($translationService);
$fixPunctuationMethod = $reflection->getMethod('fixPunctuationIssues');
$fixPunctuationMethod->setAccessible(true);

$fixedDialogue = $fixPunctuationMethod->invoke($translationService, $testDialogue, $testDialogue);

echo "Fixed dialogue:\n";
echo $fixedDialogue . "\n\n";

// Test combined fix on actual chapter content
echo "3. TESTING ON ACTUAL CHAPTER 163:\n";
echo "=" . str_repeat("=", 40) . "\n";

try {
    $db = Database::getInstance();
    $chapter = $db->fetchOne('SELECT * FROM chapters WHERE novel_id = 1 AND chapter_number = 163');
    
    if ($chapter && !empty($chapter['translated_content'])) {
        $originalContent = $chapter['translated_content'];
        
        // Count issues before fix
        preg_match_all('/-san\d+|-kun\d+|-chan\d+|-sama\d+/', $originalContent, $suffixMatches);
        preg_match_all('/」[^「]*「/', $originalContent, $punctuationMatches);
        
        echo "Issues found before fix:\n";
        echo "- Suffix issues: " . count($suffixMatches[0]) . "\n";
        echo "- Punctuation issues: " . count($punctuationMatches[0]) . "\n\n";
        
        // Apply fixes (simulate the translation process)
        $fixedContent = $originalContent;
        
        // Fix suffix issues (simulate with common patterns)
        $commonSuffixFixes = [
            '/-san\d+/' => '-san',
            '/-kun\d+/' => '-kun', 
            '/-chan\d+/' => '-chan',
            '/-sama\d+/' => '-sama',
            '/\(-san\d+\)/' => ''  // Remove parenthetical ones
        ];
        
        foreach ($commonSuffixFixes as $pattern => $replacement) {
            $fixedContent = preg_replace($pattern, $replacement, $fixedContent);
        }
        
        // Fix punctuation issues
        $fixedContent = $fixPunctuationMethod->invoke($translationService, $originalContent, $fixedContent);
        
        // Count issues after fix
        preg_match_all('/-san\d+|-kun\d+|-chan\d+|-sama\d+/', $fixedContent, $suffixMatchesAfter);
        preg_match_all('/」[^「]*「/', $fixedContent, $punctuationMatchesAfter);
        
        echo "Issues found after fix:\n";
        echo "- Suffix issues: " . count($suffixMatchesAfter[0]) . "\n";
        echo "- Punctuation issues: " . count($punctuationMatchesAfter[0]) . "\n\n";
        
        echo "Sample of fixed content (first 500 chars):\n";
        echo substr($fixedContent, 0, 500) . "\n\n";
        
        if (count($suffixMatches[0]) > count($suffixMatchesAfter[0]) || 
            count($punctuationMatches[0]) > count($punctuationMatchesAfter[0])) {
            echo "✓ Fixes are working! Issues reduced.\n";
        } else {
            echo "⚠ Fixes may need adjustment.\n";
        }
        
    } else {
        echo "Chapter 163 not found or has no translated content.\n";
    }
    
} catch (Exception $e) {
    echo "Error testing on chapter: " . $e->getMessage() . "\n";
}

echo "\n=== TEST COMPLETE ===\n";
?>
