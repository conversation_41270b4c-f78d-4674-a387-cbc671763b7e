<?php
/**
 * 69书吧 (69shuba.cx) Crawler
 * Novel Translation Application
 */

class Shuba69Crawler extends BaseCrawler {

    private const BASE_URL = 'https://69shuba.cx';
    private const ALTERNATIVE_URLS = [
        'https://69shu.com',
        'https://69shuba.com',
        'https://www.69shu.com',
        'https://www.69shuba.com',
        'https://69shuba.cx',
        'https://www.69shuba.cx'
    ];
    
    /**
     * Validate 69书吧 URL
     */
    protected function validateUrl(string $url): bool {
        // Support multiple URL patterns for 69shuba including www subdomain
        return preg_match('/((www\.)?69shuba\.(cx|com)|(www\.)?69shu\.com)\/(book|txt)\/\d+/', $url) === 1;
    }
    
    /**
     * Get novel information from 69书吧
     */
    public function getNovelInfo(string $url): array {
        if (!$this->validateUrl($url)) {
            throw new Exception("Invalid 69书吧 URL: {$url}");
        }

        $this->log("Fetching novel info from: {$url}");

        // Try multiple domains if the first one fails
        $urlsToTry = $this->generateAlternativeUrls($url);
        $lastException = null;

        foreach ($urlsToTry as $urlToTry) {
            try {
                $this->log("Attempting to fetch novel info from: {$urlToTry}");
                $html = $this->makeRequest($urlToTry);

                // Check for Cloudflare protection
                if ($this->isCloudflareChallenge($html)) {
                    $this->log("Cloudflare challenge detected, waiting and retrying...");
                    sleep(10);
                    $html = $this->makeRequest($urlToTry);

                    if ($this->isCloudflareChallenge($html)) {
                        $this->log("Still blocked after retry, trying next URL...");
                        continue;
                    }
                }

                $dom = $this->parseHtml($html);

                // Extract novel information
                $title = $this->extractTitle($dom);
                $author = $this->extractAuthor($dom);
                $synopsis = $this->extractSynopsis($dom);
                $publishDate = $this->extractPublishDate($dom);
                $totalChapters = $this->extractTotalChapters($dom);

                $this->log("Successfully extracted novel info: {$title}");

                return [
                    'platform' => 'shuba69',
                    'url' => $url,
                    'original_title' => $title,
                    'author' => $author,
                    'original_synopsis' => $synopsis,
                    'publication_date' => $publishDate,
                    'total_chapters' => $totalChapters,
                    'language' => 'zh'
                ];

            } catch (Exception $e) {
                $this->log("Error fetching novel info from {$urlToTry}: " . $e->getMessage(), 'warning');
                $lastException = $e;
                continue;
            }
        }

        // If all URLs failed, throw the last exception
        throw new Exception("Failed to extract novel info from all attempted URLs. Last error: " . $lastException->getMessage());
    }
    
    /**
     * Get chapter list from 69书吧
     */
    public function getChapterList(string $url): array {
        if (!$this->validateUrl($url)) {
            throw new Exception("Invalid 69书吧 URL: {$url}");
        }

        // Convert book info URL to chapter list URL
        $chapterListUrl = $this->getChapterListUrl($url);
        $this->log("Fetching chapter list from: {$chapterListUrl}");

        // Try multiple domains if the first one fails
        $urlsToTry = $this->generateAlternativeUrls($chapterListUrl);
        $lastException = null;

        foreach ($urlsToTry as $urlToTry) {
            try {
                $this->log("Attempting to fetch chapter list from: {$urlToTry}");
                $html = $this->makeRequest($urlToTry);

                // Check for Cloudflare protection
                if ($this->isCloudflareChallenge($html)) {
                    $this->log("Cloudflare challenge detected, waiting and retrying...");
                    sleep(10);
                    $html = $this->makeRequest($urlToTry);

                    if ($this->isCloudflareChallenge($html)) {
                        $this->log("Still blocked after retry, trying next URL...");
                        continue;
                    }
                }

                $dom = $this->parseHtml($html);

            $chapters = [];

            // Use the correct selector for 69shuba chapter list
            $chapterElements = $this->querySelectorAll($dom, '.catalog a');

            if ($chapterElements->length === 0) {
                // Try alternative selectors as fallback
                $selectors = [
                    '.chapter-list a',
                    '.list-chapter a',
                    '.mulu a',
                    '.book-chapter a',
                    '.chapter a',
                    '#list a',
                    '.listmain a',
                    'dd a'
                ];

                foreach ($selectors as $selector) {
                    $chapterElements = $this->querySelectorAll($dom, $selector);
                    if ($chapterElements->length > 0) {
                        $this->log("Found " . $chapterElements->length . " chapter elements using fallback selector: {$selector}");
                        break;
                    }
                }
            } else {
                $this->log("Found " . $chapterElements->length . " chapter elements using primary selector: .catalog a");
            }

            if ($chapterElements->length === 0) {
                $this->log("No chapter elements found with any selector", 'warning');
                return $chapters;
            }

            $chapterNumber = 1;
            foreach ($chapterElements as $element) {
                $chapterUrl = $element->getAttribute('href');
                $chapterTitle = $this->cleanText($element->textContent);

                // Filter out non-chapter links (navigation, book info, etc.)
                if ($this->isValidChapterLink($chapterUrl, $chapterTitle)) {
                    $fullChapterUrl = $this->normalizeUrl($chapterUrl, $this->getBaseUrlFromOriginal($chapterListUrl));

                    $chapters[] = [
                        'chapter_number' => $chapterNumber,
                        'chapter_url' => $fullChapterUrl,
                        'original_title' => $chapterTitle
                    ];

                    $chapterNumber++;
                }
            }

            // Check if chapters are in reverse order (newest first) and reverse if needed
            if (count($chapters) > 1) {
                $firstChapterTitle = $chapters[0]['original_title'];
                $lastChapterTitle = $chapters[count($chapters) - 1]['original_title'];

                // If first chapter has higher number than last, reverse the order
                if (preg_match('/第(\d+)章/', $firstChapterTitle, $firstMatch) &&
                    preg_match('/第(\d+)章/', $lastChapterTitle, $lastMatch)) {
                    if ((int)$firstMatch[1] > (int)$lastMatch[1]) {
                        $chapters = array_reverse($chapters);
                        $this->log("Reversed chapter order (was newest first)");
                    }
                }
            }

            // Re-number chapters in correct order
            foreach ($chapters as $index => &$chapter) {
                $chapter['chapter_number'] = $index + 1;
            }

                $this->log("Found " . count($chapters) . " chapters");

                return $chapters;

            } catch (Exception $e) {
                $this->log("Error fetching chapter list from {$urlToTry}: " . $e->getMessage(), 'warning');
                $lastException = $e;
                continue;
            }
        }

        // If all URLs failed, throw the last exception
        throw new Exception("Failed to extract chapter list from all attempted URLs. Last error: " . $lastException->getMessage());
    }

    /**
     * Convert book info URL to chapter list URL
     */
    private function getChapterListUrl(string $url): string {
        // If URL ends with .htm, remove it to get chapter list URL
        if (preg_match('/(.+)\.htm$/', $url, $matches)) {
            return $matches[1] . '/';
        }

        // If URL already ends with /, it's probably the chapter list URL
        if (substr($url, -1) === '/') {
            return $url;
        }

        // Add trailing slash
        return $url . '/';
    }

    /**
     * Validate if a link is a valid chapter link
     */
    private function isValidChapterLink(string $chapterUrl, string $chapterTitle): bool {
        if (!$chapterUrl || !$chapterTitle) {
            return false;
        }

        // Skip empty or anchor links
        if ($chapterUrl === '#' || strpos($chapterUrl, 'javascript:') === 0) {
            return false;
        }

        // Skip navigation links back to book info
        if (preg_match('/book\/\d+\.htm$/', $chapterUrl)) {
            return false;
        }

        // Must be chapter format - 69shuba uses /txt/bookid/chapterid pattern
        $validPatterns = [
            '/\/txt\/\d+\/\d+/',      // /txt/12345/67890 (69shuba format)
            '/\/\d+\/\d+\.html/',     // /12345/67890.html
            '/\/chapter\/\d+/',       // /chapter/123
            '/\/read\/\d+\/\d+/',     // /read/12345/67890
            '/\/book\/\d+\/\d+/'      // /book/12345/67890
        ];

        $isValidUrl = false;
        foreach ($validPatterns as $pattern) {
            if (preg_match($pattern, $chapterUrl)) {
                $isValidUrl = true;
                break;
            }
        }

        if (!$isValidUrl) {
            return false;
        }

        // Skip author notes and non-chapter content
        $skipPatterns = [
            '/(新书|完本感|感言|作者|序言|后记|番外|公告)/',
            '/^(目录|返回|上一页|下一页|首页|尾页)$/',
            '/^第?\s*[零一二三四五六七八九十百千万]+\s*卷$/',  // Volume titles
            '/^(从零开始缔造游戏帝国|69书吧)/',  // Site-specific navigation
            '/无弹窗.*阅读/'  // Navigation text
        ];

        foreach ($skipPatterns as $pattern) {
            if (preg_match($pattern, $chapterTitle)) {
                return false;
            }
        }

        // Must have meaningful title and look like a chapter
        $title = trim($chapterTitle);
        if (strlen($title) === 0) {
            return false;
        }

        // Should contain chapter indicators
        if (preg_match('/(第\d+章|章节|完结感言)/', $title)) {
            return true;
        }

        // If it doesn't have chapter indicators but has valid URL pattern, it might still be valid
        // but be more strict about the title length
        return strlen($title) > 3 && strlen($title) < 200;
    }
    
    /**
     * Get chapter content from 69书吧
     */
    public function getChapterContent(string $chapterUrl): array {
        $this->log("Fetching chapter content from: {$chapterUrl}");

        // Try multiple domains if the first one fails
        $urlsToTry = $this->generateAlternativeUrls($chapterUrl);

        $lastException = null;

        foreach ($urlsToTry as $urlToTry) {
            try {
                $this->log("Attempting to fetch from: {$urlToTry}");

                // Extract book ID from chapter URL for referer
                $bookId = '';
                if (preg_match('/\/txt\/(\d+)\//', $urlToTry, $matches)) {
                    $bookId = $matches[1];
                }

                // Determine the correct referer domain
                $domain = $this->extractDomainFromUrl($urlToTry);
                $referer = $bookId ? "{$domain}/book/{$bookId}.htm" : $domain;

                $options = [
                    CURLOPT_HTTPHEADER => [
                        "Referer: {$referer}",
                        'Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
                        'Accept-Language: zh-CN,zh;q=0.9,en;q=0.8,en-US;q=0.7',
                        'Accept-Encoding: gzip, deflate',
                        'Cache-Control: max-age=0',
                        'Sec-Fetch-Dest: document',
                        'Sec-Fetch-Mode: navigate',
                        'Sec-Fetch-Site: same-origin',
                        'Sec-Fetch-User: ?1',
                        'Upgrade-Insecure-Requests: 1',
                        'Connection: keep-alive',
                        'DNT: 1',
                        'Sec-GPC: 1'
                    ],
                    CURLOPT_ENCODING => '', // Let curl handle all supported encodings automatically
                    CURLOPT_FOLLOWLOCATION => true,
                    CURLOPT_MAXREDIRS => 5,
                    CURLOPT_TIMEOUT => 45,
                    CURLOPT_CONNECTTIMEOUT => 30,
                    CURLOPT_COOKIEJAR => sys_get_temp_dir() . '/69shuba_cookies.txt',
                    CURLOPT_COOKIEFILE => sys_get_temp_dir() . '/69shuba_cookies.txt',
                    CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_2_0,
                    CURLOPT_IPRESOLVE => CURL_IPRESOLVE_V4,
                    CURLOPT_USERAGENT => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                    CURLOPT_SSL_VERIFYPEER => false,
                    CURLOPT_SSL_VERIFYHOST => false
                ];

                // Add delay to avoid being detected as bot
                if (isset($GLOBALS['last_69shuba_request'])) {
                    $timeSinceLastRequest = time() - $GLOBALS['last_69shuba_request'];
                    if ($timeSinceLastRequest < 5) {
                        sleep(5 - $timeSinceLastRequest);
                    }
                }

                // Warm up session by visiting the novel page first
                $this->warmUpSession($domain, $bookId);

                $html = $this->makeRequest($urlToTry, $options);

                // Check for Cloudflare protection
                if ($this->isCloudflareChallenge($html)) {
                    $this->log("Cloudflare challenge detected, waiting and retrying...");
                    sleep(10); // Wait for potential challenge to complete
                    $html = $this->makeRequest($urlToTry, $options);

                    // If still blocked, try alternative domain
                    if ($this->isCloudflareChallenge($html)) {
                        $alternativeUrl = $this->getAlternativeDomainUrl($urlToTry);
                        if ($alternativeUrl !== $urlToTry) {
                            $this->log("Trying alternative domain: $alternativeUrl");
                            $html = $this->makeRequest($alternativeUrl, $options);
                        }
                    }
                }

                $GLOBALS['last_69shuba_request'] = time();

                // Debug: Log HTML length and check if it looks like a chapter page
                $this->log("HTML response length: " . strlen($html));

                // Check if we got redirected to a listing page or blocked
                // Be more specific about anti-bot detection to avoid false positives
                $hasChapterContent = strpos($html, '第') !== false &&
                                   (strpos($html, '章') !== false || strpos($html, '怒气') !== false);
                $isListingPage = strpos($html, 'class="newlistbox"') !== false && !$hasChapterContent;
                $isBlocked = strpos($html, 'Just a moment') !== false ||
                            strpos($html, 'Checking your browser') !== false ||
                            strpos($html, 'cloudflare') !== false ||
                            strpos($html, 'Access denied') !== false;

                if ($isListingPage || $isBlocked) {
                    $this->log("Detected redirect or blocking on {$urlToTry}", 'warning');
                    throw new Exception("Site protection active on {$urlToTry}");
                }

                $dom = $this->parseHtml($html);

                // Extract chapter title - for 69shuba it's in h1 or title
                $title = $this->extractChapterTitle($dom);

                // For 69shuba, content is directly in the page body, not in a specific container
                $content = $this->extractChapterContentDirect($dom);

                if (empty($content)) {
                    throw new Exception("No content extracted from chapter on {$urlToTry}");
                }

                // Enhanced content validation
                $contentLength = strlen($content);
                $wordCount = mb_strlen($content, 'UTF-8');
                $chineseCharCount = preg_match_all('/[\x{4E00}-\x{9FFF}]/u', $content);

                if ($contentLength < 100) {
                    throw new Exception("Chapter content too short: {$contentLength} characters on {$urlToTry}");
                }

                if ($chineseCharCount < 50) {
                    throw new Exception("Insufficient Chinese content: {$chineseCharCount} characters on {$urlToTry} - likely blocked");
                }

                // Check for content completeness indicators
                $hasBeginning = $this->validateContentBeginning($content);
                $hasEnding = $this->validateContentEnding($content);

                if (!$hasBeginning) {
                    $this->log("Warning: Content may be missing beginning", 'warning');
                }

                if (!$hasEnding) {
                    $this->log("Warning: Content may be missing ending", 'warning');
                }

                // Log content quality metrics
                $this->log("Content validation - Length: {$contentLength} chars, Beginning: " .
                          ($hasBeginning ? 'OK' : 'MISSING') . ", Ending: " .
                          ($hasEnding ? 'OK' : 'MISSING'));

                $this->log("Successfully extracted chapter from {$urlToTry}: {$title} ({$contentLength} characters)");

                return [
                    'original_title' => $title,
                    'original_content' => $content,
                    'word_count' => $wordCount
                ];

            } catch (Exception $e) {
                $this->log("Failed to extract from {$urlToTry}: " . $e->getMessage(), 'warning');
                $lastException = $e;
                continue; // Try next URL
            }
        }

        // If we get here, all URLs failed
        throw new Exception("Failed to extract chapter content from all attempted URLs. Last error: " .
                          ($lastException ? $lastException->getMessage() : 'Unknown error'));
    }

    /**
     * Check if response contains Cloudflare challenge
     */
    private function isCloudflareChallenge(string $html): bool {
        $challengeIndicators = [
            'Just a moment...',
            'Checking your browser',
            'Please wait while we check your browser',
            'DDoS protection by Cloudflare',
            'cf-browser-verification',
            'cf-challenge-running',
            'cloudflare',
            'Ray ID:'
        ];

        foreach ($challengeIndicators as $indicator) {
            if (stripos($html, $indicator) !== false) {
                return true;
            }
        }

        return false;
    }

    /**
     * Generate multiple alternative URLs to try
     */
    private function generateAlternativeUrls(string $url): array {
        $urls = [$url];

        // Extract the path part
        $parsed = parse_url($url);
        $path = $parsed['path'] ?? '';
        $query = isset($parsed['query']) ? '?' . $parsed['query'] : '';

        // Try all alternative domains
        $domains = [
            '69shuba.com',
            'www.69shuba.com',
            '69shuba.cx',
            'www.69shuba.cx',
            '69shu.com',
            'www.69shu.com'
        ];

        foreach ($domains as $domain) {
            $newUrl = "https://{$domain}{$path}{$query}";
            if (!in_array($newUrl, $urls)) {
                $urls[] = $newUrl;
            }
        }

        return $urls;
    }

    /**
     * Get alternative domain URL (legacy method for backward compatibility)
     */
    private function getAlternativeDomainUrl(string $url): string {
        $alternatives = $this->generateAlternativeUrls($url);
        return count($alternatives) > 1 ? $alternatives[1] : $url;
    }

    /**
     * Extract domain from URL
     */
    private function extractDomainFromUrl(string $url): string {
        $parsed = parse_url($url);
        return $parsed['scheme'] . '://' . $parsed['host'];
    }

    /**
     * Warm up session by visiting the novel page first to avoid anti-bot detection
     */
    private function warmUpSession(string $domain, string $bookId): void {
        if (empty($bookId)) {
            return;
        }

        $novelUrl = "{$domain}/book/{$bookId}.htm";

        // Check if we've already warmed up this session recently
        $cacheKey = 'warmup_' . md5($novelUrl);
        $lastWarmup = $GLOBALS[$cacheKey] ?? 0;

        if (time() - $lastWarmup < 300) { // 5 minutes cache
            return;
        }

        try {
            $this->log("Warming up session by visiting novel page: {$novelUrl}");

            $options = [
                CURLOPT_HTTPHEADER => [
                    'Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
                    'Accept-Language: zh-CN,zh;q=0.9,en;q=0.8,en-US;q=0.7',
                    'Accept-Encoding: gzip, deflate',
                    'Cache-Control: max-age=0',
                    'Sec-Fetch-Dest: document',
                    'Sec-Fetch-Mode: navigate',
                    'Sec-Fetch-Site: none',
                    'Sec-Fetch-User: ?1',
                    'Upgrade-Insecure-Requests: 1',
                    'Connection: keep-alive',
                    'DNT: 1',
                    'Sec-GPC: 1'
                ],
                CURLOPT_USERAGENT => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                CURLOPT_ENCODING => '', // Let curl handle all supported encodings automatically
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_MAXREDIRS => 3,
                CURLOPT_TIMEOUT => 30,
                CURLOPT_COOKIEJAR => sys_get_temp_dir() . '/69shuba_cookies.txt',
                CURLOPT_COOKIEFILE => sys_get_temp_dir() . '/69shuba_cookies.txt',
                CURLOPT_SSL_VERIFYPEER => false,
                CURLOPT_SSL_VERIFYHOST => false
            ];

            $html = $this->makeRequest($novelUrl, $options);

            // Check for Cloudflare challenge during warmup
            if ($this->isCloudflareChallenge($html)) {
                $this->log("Cloudflare challenge detected during warmup, waiting...");
                sleep(15); // Longer wait during warmup
                $html = $this->makeRequest($novelUrl, $options);
            }

            $GLOBALS[$cacheKey] = time();

            // Small delay after warmup
            sleep(2);

        } catch (Exception $e) {
            $this->log("Session warmup failed: " . $e->getMessage(), 'warning');
        }
    }

    /**
     * Validate if content has proper beginning
     */
    private function validateContentBeginning(string $content): bool {
        $firstPart = substr($content, 0, 200);

        // Check for typical story beginning patterns
        $beginningPatterns = [
            '/^["""]/',  // Starts with dialogue
            '/^[\x{4e00}-\x{9fff}]/u',  // Starts with Chinese character
            '/第\d+章/',  // Contains chapter marker
            '/^\s*[\x{4e00}-\x{9fff}].*[。！？]/u'  // Starts with text and has punctuation
        ];

        foreach ($beginningPatterns as $pattern) {
            if (preg_match($pattern, $firstPart)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Validate if content has proper ending
     */
    private function validateContentEnding(string $content): bool {
        $lastPart = substr($content, -200);

        // Check for typical story ending patterns
        $endingPatterns = [
            '/[。！？][""]?\s*$/',  // Ends with punctuation
            '/[。！？]\s*$/',  // Ends with Chinese punctuation
            '/完$/',  // Ends with "完" (complete)
            '/待续$/',  // Ends with "待续" (to be continued)
            '/[\x{4e00}-\x{9fff}][。！？]\s*$/u'  // Ends with character and punctuation
        ];

        foreach ($endingPatterns as $pattern) {
            if (preg_match($pattern, $lastPart)) {
                return true;
            }
        }

        return false;
    }
    
    /**
     * Extract novel title
     */
    private function extractTitle(DOMDocument $dom): string {
        // First try h1 which should have the actual title
        $h1Element = $this->querySelector($dom, 'h1');
        if ($h1Element) {
            $title = $this->cleanText($h1Element->textContent);
            // Make sure it's not a generic title like "作品简介"
            if (!empty($title) && !in_array($title, ['作品简介', '简介', '目录', '章节列表'])) {
                $this->log("Title extracted using h1: {$title}");
                return $title;
            }
        }

        // Try other selectors
        $titleSelectors = [
            '.book-title',
            '.bookname',
            '.book-info h1',
            '.info h1',
            '.book h1',
            '#info h1'
        ];

        foreach ($titleSelectors as $selector) {
            $titleElement = $this->querySelector($dom, $selector);
            if ($titleElement) {
                $title = $this->cleanText($titleElement->textContent);
                if (!empty($title) && !in_array($title, ['作品简介', '简介', '目录', '章节列表'])) {
                    $this->log("Title extracted using selector '{$selector}': {$title}");
                    return $title;
                }
            }
        }

        // Try to extract from page title
        $titleElement = $this->querySelector($dom, 'title');
        if ($titleElement) {
            $pageTitle = $this->cleanText($titleElement->textContent);
            // Remove common suffixes from page title
            $title = preg_replace('/[-_|].*?(69书吧|69shuba|小说|阅读|txt|下载).*$/i', '', $pageTitle);
            $title = trim($title);
            if (!empty($title)) {
                $this->log("Title extracted from page title: {$title}");
                return $title;
            }
        }

        $this->log("Title extraction failed - no matching elements found", 'warning');
        return '';
    }

    /**
     * Extract author name
     */
    private function extractAuthor(DOMDocument $dom): string {
        // Try to find author in paragraph elements (most reliable for this site)
        $paragraphs = $this->querySelectorAll($dom, 'p');
        foreach ($paragraphs as $p) {
            $text = $this->cleanText($p->textContent);

            // Check if this paragraph contains author info
            if (strpos($text, '作者') !== false) {
                // Pattern: 作者：AuthorName (capture everything after 作者：)
                if (preg_match('/作者[：:]\s*(.+)$/', $text, $matches)) {
                    $author = trim($matches[1]);
                    // Make sure it's not part of a longer navigation text
                    if (!empty($author) && strlen($author) < 50 && !preg_match('/(小说|阅读|下载|69书吧|首页|排行|分类)/', $author)) {
                        $this->log("Author extracted from paragraph: {$author}");
                        return $author;
                    }
                }
            }
        }

        // Try to find author in text content
        $xpath = new DOMXPath($dom);
        $textNodes = $xpath->query('//text()[contains(., "作者")]');
        foreach ($textNodes as $textNode) {
            $text = $textNode->textContent;

            // Pattern: 作者：AuthorName
            if (preg_match('/作者[：:]\s*([^分类\n\r]+?)(?:\s|$)/', $text, $matches)) {
                $author = trim($matches[1]);
                // Make sure it's not part of a longer navigation text
                if (!empty($author) && strlen($author) < 50 && !preg_match('/(小说|阅读|下载|69书吧)/', $author)) {
                    $this->log("Author extracted from text content: {$author}");
                    return $author;
                }
            }
        }

        // Try structured selectors
        $authorSelectors = [
            '.book-author',
            '.author',
            '.author-name',
            '.writer',
            '.writer-name',
            '.book-info .author',
            '.meta .author',
            '.info .author',
            '#info .author',
            '.book .author',
            '.bookinfo .author'
        ];

        foreach ($authorSelectors as $selector) {
            $authorElement = $this->querySelector($dom, $selector);
            if ($authorElement) {
                $authorText = $this->cleanText($authorElement->textContent);

                // Remove common prefixes and clean up
                $authorText = preg_replace('/^(作者[：:]?\s*|著者[：:]?\s*|Author[：:]?\s*)/u', '', $authorText);
                $authorText = preg_replace('/\s*(作者|著者|Author)\s*$/', '', $authorText);
                $authorText = trim($authorText);

                // Make sure it's not navigation text
                if (!empty($authorText) && strlen($authorText) > 1 && strlen($authorText) < 50 &&
                    !preg_match('/(小说|阅读|下载|69书吧|首页|排行|分类)/', $authorText)) {
                    $this->log("Author extracted using selector '{$selector}': {$authorText}");
                    return $authorText;
                }
            }
        }

        // Try to extract from page structure - look for spans or divs near title
        $h1Element = $this->querySelector($dom, 'h1');
        if ($h1Element && $h1Element->parentNode) {
            $parent = $h1Element->parentNode;
            $siblings = $parent->childNodes;

            foreach ($siblings as $sibling) {
                if ($sibling->nodeType === XML_ELEMENT_NODE) {
                    $text = $this->cleanText($sibling->textContent);
                    if (preg_match('/作者[：:]\s*([^分类\n\r]+?)(?:\s|$)/', $text, $matches)) {
                        $author = trim($matches[1]);
                        if (!empty($author) && strlen($author) < 50) {
                            $this->log("Author extracted from sibling element: {$author}");
                            return $author;
                        }
                    }
                }
            }
        }

        $this->log("Author extraction failed - no matching elements found", 'warning');
        return '';
    }

    /**
     * Extract synopsis
     */
    private function extractSynopsis(DOMDocument $dom): string {
        $synopsisSelectors = [
            '.book-summary',
            '.summary',
            '.intro',
            '.description',
            '.book-intro',
            '.book-description',
            '.content-summary',
            '.synopsis',
            '.book-info .intro',
            '.info .intro',
            '#info .intro',
            '.bookinfo .intro',
            '.book .intro',
            '.jieshao'
        ];

        foreach ($synopsisSelectors as $selector) {
            $synopsisElement = $this->querySelector($dom, $selector);
            if ($synopsisElement) {
                $synopsisText = $this->cleanText($synopsisElement->textContent);

                // Remove common prefixes
                $synopsisText = preg_replace('/^(内容简介[：:]?\s*|简介[：:]?\s*|介绍[：:]?\s*)/u', '', $synopsisText);
                $synopsisText = trim($synopsisText);

                if (!empty($synopsisText) && strlen($synopsisText) > 10) {
                    $this->log("Synopsis extracted using selector '{$selector}': " . substr($synopsisText, 0, 100) . "...");
                    return $synopsisText;
                }
            }
        }

        // Try to find synopsis in paragraphs
        $paragraphs = $this->querySelectorAll($dom, 'p');
        foreach ($paragraphs as $p) {
            $text = $this->cleanText($p->textContent);
            if (strlen($text) > 50 &&
                (strpos($text, '简介') !== false ||
                 strpos($text, '内容') !== false ||
                 strpos($text, '介绍') !== false)) {

                $text = preg_replace('/^(内容简介[：:]?\s*|简介[：:]?\s*|介绍[：:]?\s*)/u', '', $text);
                $text = trim($text);

                if (strlen($text) > 20) {
                    $this->log("Synopsis extracted from paragraph: " . substr($text, 0, 100) . "...");
                    return $text;
                }
            }
        }

        $this->log("Synopsis extraction failed - no matching elements found", 'warning');
        return '';
    }
    
    /**
     * Extract publication date
     */
    private function extractPublishDate(DOMDocument $dom): ?string {
        // Try to find date in paragraph elements (most reliable for this site)
        $paragraphs = $this->querySelectorAll($dom, 'p');
        foreach ($paragraphs as $p) {
            $text = $this->cleanText($p->textContent);

            // Look for "更新：" pattern
            if (preg_match('/更新[：:]\s*(\d{4}-\d{1,2}-\d{1,2})/', $text, $matches)) {
                $date = $this->extractDate($matches[1]);
                if ($date) {
                    $this->log("Publication date extracted from paragraph: {$date}");
                    return $date;
                }
            }
        }

        $dateSelectors = [
            '.book-meta',
            '.book-info',
            '.meta',
            '.publish-date',
            '.created-date',
            '.date',
            '.book-date',
            '.info',
            '#info',
            '.bookinfo',
            '.book .info'
        ];

        foreach ($dateSelectors as $selector) {
            $dateElements = $this->querySelectorAll($dom, $selector);

            foreach ($dateElements as $element) {
                $dateText = $this->cleanText($element->textContent);

                // Look for Chinese date keywords
                if (strpos($dateText, '更新时间') !== false ||
                    strpos($dateText, '更新') !== false ||
                    strpos($dateText, '发布时间') !== false ||
                    strpos($dateText, '上传时间') !== false ||
                    strpos($dateText, '创建时间') !== false ||
                    strpos($dateText, '时间') !== false ||
                    preg_match('/\d{4}/', $dateText)) {

                    $date = $this->extractDate($dateText);
                    if ($date) {
                        $this->log("Publication date extracted using selector '{$selector}': {$date}");
                        return $date;
                    }
                }
            }
        }

        // Try to find date in text content with specific patterns
        $xpath = new DOMXPath($dom);
        $textNodes = $xpath->query('//text()[contains(., "更新") or contains(., "时间") or contains(., "日期") or contains(., "年") or contains(., "月")]');

        foreach ($textNodes as $textNode) {
            $text = $textNode->textContent;

            // Look for date patterns with Chinese keywords
            $patterns = [
                '/(?:更新|更新时间|发布时间|上传时间|创建时间)[：:]\s*(\d{4}[-\/年]\d{1,2}[-\/月]\d{1,2}[日]?)/',
                '/(\d{4}[-\/年]\d{1,2}[-\/月]\d{1,2}[日]?)/',
                '/(\d{4}年\d{1,2}月\d{1,2}日)/',
                '/(\d{4}-\d{1,2}-\d{1,2})/',
                '/(\d{4}\/\d{1,2}\/\d{1,2})/'
            ];

            foreach ($patterns as $pattern) {
                if (preg_match($pattern, $text, $matches)) {
                    $date = $this->extractDate($matches[1]);
                    if ($date) {
                        $this->log("Publication date extracted from text content: {$date}");
                        return $date;
                    }
                }
            }
        }

        // Try to find date in meta tags
        $metaElements = $this->querySelectorAll($dom, 'meta[property*="date"], meta[name*="date"], meta[property*="time"], meta[name*="time"]');
        foreach ($metaElements as $element) {
            $content = $element->getAttribute('content');
            if ($content) {
                $date = $this->extractDate($content);
                if ($date) {
                    $this->log("Publication date extracted from meta tag: {$date}");
                    return $date;
                }
            }
        }

        // Try to extract from first chapter if available
        $firstChapterDate = $this->extractFirstChapterDate($dom);
        if ($firstChapterDate) {
            $this->log("Publication date extracted from first chapter: {$firstChapterDate}");
            return $firstChapterDate;
        }

        $this->log("Publication date extraction failed - no matching elements found", 'warning');
        return null;
    }

    /**
     * Extract publication date from the first chapter
     */
    private function extractFirstChapterDate(DOMDocument $dom): ?string {
        // Look for the first chapter link and try to extract date from its context
        $chapterSelectors = [
            '.catalog a:first-child',
            '.chapter-list a:first-child',
            '.list-chapter a:first-child',
            '.mulu a:first-child',
            '#list a:first-child'
        ];

        foreach ($chapterSelectors as $selector) {
            $firstChapter = $this->querySelector($dom, $selector);
            if ($firstChapter) {
                // Look for date in the parent element or siblings
                $parent = $firstChapter->parentNode;
                if ($parent) {
                    $parentText = $this->cleanText($parent->textContent);
                    $date = $this->extractDate($parentText);
                    if ($date) {
                        return $date;
                    }
                }

                // Check next sibling for date
                $nextSibling = $firstChapter->nextSibling;
                while ($nextSibling) {
                    if ($nextSibling->nodeType === XML_TEXT_NODE || $nextSibling->nodeType === XML_ELEMENT_NODE) {
                        $siblingText = $this->cleanText($nextSibling->textContent);
                        $date = $this->extractDate($siblingText);
                        if ($date) {
                            return $date;
                        }
                    }
                    $nextSibling = $nextSibling->nextSibling;

                    // Only check a few siblings
                    if (!$nextSibling || $nextSibling->nodeType === XML_ELEMENT_NODE) {
                        break;
                    }
                }
            }
        }

        return null;
    }
    
    /**
     * Extract total chapters
     */
    private function extractTotalChapters(DOMDocument $dom): int {
        // Count chapters in the catalog
        $chapterElements = $this->querySelectorAll($dom, '.catalog a');
        if ($chapterElements->length > 0) {
            return $chapterElements->length;
        }
        
        // Try alternative selectors
        $chapterElements = $this->querySelectorAll($dom, '.chapter-list a');
        if ($chapterElements->length > 0) {
            return $chapterElements->length;
        }
        
        // Look for chapter count in text
        $allText = $dom->textContent;
        if (preg_match('/共(\d+)章/', $allText, $matches)) {
            return (int) $matches[1];
        }
        
        if (preg_match('/(\d+)章/', $allText, $matches)) {
            return (int) $matches[1];
        }
        
        return 0;
    }
    
    /**
     * Extract chapter title with fallback selectors
     */
    private function extractChapterTitle(DOMDocument $dom): string {
        // For 69shuba, try h1 first
        $h1Element = $this->querySelector($dom, 'h1');
        if ($h1Element) {
            $title = $this->cleanText($h1Element->textContent);
            if (!empty($title) && !in_array($title, ['目录', '章节列表', '首页'])) {
                return $title;
            }
        }

        // Try to extract from page title
        $titleElement = $this->querySelector($dom, 'title');
        if ($titleElement) {
            $pageTitle = $this->cleanText($titleElement->textContent);
            // Extract chapter title from page title (format: "小说名-第X章 章节名")
            if (preg_match('/^(.+?)-(.+?)$/', $pageTitle, $matches)) {
                $chapterTitle = trim($matches[2]);
                // Remove site name suffix
                $chapterTitle = preg_replace('/[-_|].*?(69书吧|69shuba).*$/i', '', $chapterTitle);
                $chapterTitle = trim($chapterTitle);
                if (!empty($chapterTitle)) {
                    return $chapterTitle;
                }
            }
        }

        // Try other selectors as fallback
        $titleSelectors = [
            '.chapter-title',
            '.book-title',
            '.title',
            '.content-title'
        ];

        foreach ($titleSelectors as $selector) {
            $titleElement = $this->querySelector($dom, $selector);
            if ($titleElement) {
                $title = $this->cleanText($titleElement->textContent);
                if (!empty($title)) {
                    return $title;
                }
            }
        }

        return '';
    }

    /**
     * Find content element with fallback selectors
     */
    private function findContentElement(DOMDocument $dom): ?DOMElement {
        $contentSelectors = [
            '.chapter-content',
            '.content',
            '#content',
            '.book-content',
            '.text-content',
            '.novel-content',
            '.txt',
            '.main-text',
            '.chapter-text',
            '.read-content',
            '.article-content',
            '.story-content',
            '#main',
            '.main',
            '.readcontent',
            '.bookcontent'
        ];

        foreach ($contentSelectors as $selector) {
            $contentElement = $this->querySelector($dom, $selector);
            if ($contentElement) {
                // Check if element has meaningful content
                $text = trim($contentElement->textContent);
                if (!empty($text) && strlen($text) > 50) {
                    $this->log("Found content element using selector: {$selector}");
                    return $contentElement;
                }
            }
        }

        // If no specific content element found, try to find the main content area
        // Look for divs with substantial text content
        $xpath = new DOMXPath($dom);
        $divs = $xpath->query('//div[string-length(normalize-space(text())) > 100]');

        foreach ($divs as $div) {
            $text = trim($div->textContent);
            // Skip navigation and header content
            if (strlen($text) > 200 &&
                !preg_match('/(排行|导航|菜单|登录|注册|搜索|首页)/', $text) &&
                !preg_match('/class="(menu|nav|header|footer|sidebar)"/', $div->getAttribute('class'))) {
                $this->log("Found content element using text length heuristic");
                return $div;
            }
        }

        return null;
    }

    /**
     * Extract chapter content directly from 69shuba page structure
     * FIXED: Simplified approach based on working backup version to prevent content truncation
     */
    protected function extractChapterContentDirect(DOMDocument $dom): string {
        $content = '';

        // For 69shuba, content paragraphs are directly in the page after the navigation
        // Look for paragraphs that contain actual story content
        $xpath = new DOMXPath($dom);
        $paragraphs = $xpath->query('//p');

        $contentStarted = false;
        $contentParagraphs = [];

        foreach ($paragraphs as $paragraph) {
            $text = $this->cleanText($paragraph->textContent);

            // Skip empty paragraphs
            if (empty($text)) {
                continue;
            }

            // Filter out JavaScript code like "loadAdv(2, 0);" but be very conservative
            if (preg_match('/^loadAdv\s*\(\s*\d+\s*,\s*\d+\s*\)\s*;?\s*$/', $text)) {
                continue;
            }

            // Skip only obvious navigation paragraphs - be very conservative to prevent content loss
            if (preg_match('/^(收藏|推荐|投票|签约|更新|点击|评论)$/', $text) ||
                preg_match('/^(目录|上一章|下一章|首页|书签|设置|夜间|返回|收藏本站|Copyright|69书吧)$/', $text) ||
                strlen($text) < 2) {
                continue;
            }

            // Look for the start of actual content - be more inclusive
            if (!$contentStarted) {
                // Content typically starts with narrative text
                if (strlen($text) > 5 &&
                    !preg_match('/^(目录|上一章|下一章|首页|书签|设置|夜间)$/', $text)) {
                    $contentStarted = true;
                }
            }

            if ($contentStarted) {
                $contentParagraphs[] = $text;
            }
        }

        // Join paragraphs with double newlines
        $content = implode("\n\n", $contentParagraphs);

        // If no content found with the above method, try a more aggressive approach
        if (empty($content)) {
            // Look for the main content area between navigation elements
            $allText = $dom->textContent;

            // Try to extract content between navigation markers - be more conservative
            if (preg_match('/第\d+章[^<]*?(.+?)(?:上一章|下一章|目录|收藏)/s', $allText, $matches)) {
                $content = $this->cleanText($matches[1]);
            }
        }

        // Clean up the content - minimal cleaning to preserve content
        $content = preg_replace('/\n{3,}/', "\n\n", $content);
        $content = trim($content);

        return $content;
    }

    /**
     * REMOVED: Complex comprehensive strategy that was causing content truncation
     * Replaced with simpler, more reliable extraction methods
     */

    /**
     * Extract all text from an element while preserving structure and filtering navigation
     */
    private function extractAllTextFromElement(DOMElement $element): string {
        $xpath = new DOMXPath($element->ownerDocument);
        $textParts = [];

        // Method 1: Try to extract from paragraphs first
        $paragraphs = $xpath->query('.//p', $element);
        if ($paragraphs->length > 0) {
            foreach ($paragraphs as $p) {
                $text = trim($p->textContent);
                if ($this->isValidContentParagraph($text)) {
                    $textParts[] = $text;
                }
            }
        }

        // Method 2: If no paragraphs, extract from divs
        if (empty($textParts)) {
            $divs = $xpath->query('.//div', $element);
            foreach ($divs as $div) {
                $text = trim($div->textContent);
                if ($this->isValidContentParagraph($text)) {
                    // Check if this div contains other divs - if so, skip to avoid duplication
                    $childDivs = $xpath->query('.//div', $div);
                    if ($childDivs->length === 0) {
                        $textParts[] = $text;
                    }
                }
            }
        }

        // Method 3: If still no content, extract all text nodes
        if (empty($textParts)) {
            $textNodes = $xpath->query('.//text()[normalize-space(.) != ""]', $element);
            $currentParagraph = '';

            foreach ($textNodes as $textNode) {
                $text = trim($textNode->textContent);
                if (!empty($text)) {
                    $currentParagraph .= $text . ' ';

                    // Check if this text node is followed by a block element
                    $parent = $textNode->parentNode;
                    if ($parent && in_array(strtolower($parent->nodeName), ['p', 'div', 'br'])) {
                        if (!empty($currentParagraph)) {
                            $cleanParagraph = trim($currentParagraph);
                            if ($this->isValidContentParagraph($cleanParagraph)) {
                                $textParts[] = $cleanParagraph;
                            }
                            $currentParagraph = '';
                        }
                    }
                }
            }

            // Add any remaining paragraph
            if (!empty($currentParagraph)) {
                $cleanParagraph = trim($currentParagraph);
                if ($this->isValidContentParagraph($cleanParagraph)) {
                    $textParts[] = $cleanParagraph;
                }
            }
        }

        return implode("\n\n", $textParts);
    }

    /**
     * Check if a paragraph contains valid content (not navigation or JavaScript)
     */
    private function isValidContentParagraph(string $text): bool {
        // Skip empty or very short text
        if (empty($text) || mb_strlen($text, 'UTF-8') < 3) {
            return false;
        }

        // Skip JavaScript content and variables
        $jsPatterns = [
            '/var\s+\w+\s*=/',
            '/function\s*\(/',
            '/\{\s*pageType\s*:/',
            '/bookinfo\s*=/',
            '/articleid\s*:/',
            '/chapterid\s*:/',
            '/site\s*:/',
            '/https?:\/\//',
            '/loadAdv\s*\(/',
            '/document\./i',
            '/window\./i',
            '/console\./i',
            '/\$\s*\(/',
            '/jQuery/i'
        ];

        foreach ($jsPatterns as $pattern) {
            if (preg_match($pattern, $text)) {
                return false;
            }
        }

        // Skip obvious navigation patterns
        $navigationPatterns = [
            '/^(目录|上一章|下一章|首页|书签|设置|夜间|返回|收藏本站|Copyright|69书吧|TXT下载|全文阅读)$/u',
            '/^第\d+章\s*$/u', // Chapter title only
            '/^(点击|阅读|下载|收藏|推荐|投票|评论).*$/u',
            '/^(从零开始缔造游戏帝国)$/u', // Novel title
            '/^\d{4}-\d{1,2}-\d{1,2}$/', // Date only
            '/^(都市小说|小说分类)$/u'
        ];

        foreach ($navigationPatterns as $pattern) {
            if (preg_match($pattern, $text)) {
                return false;
            }
        }

        // Must contain some Chinese characters for story content
        $chineseCharCount = preg_match_all('/[\x{4E00}-\x{9FFF}]/u', $text);
        if ($chineseCharCount < 2) {
            return false;
        }

        return true;
    }

    /**
     * REMOVED: Complex 69Shuba strategy that was causing content truncation
     * The date-based extraction and complex HTML parsing was removing actual content
     */

    /**
     * Extract text content from HTML content with proper Chinese character handling
     */
    private function extractTextFromContentHtml(DOMDocument $dom): string {
        $xpath = new DOMXPath($dom);
        $contentParts = [];

        // Get all text nodes and paragraph elements
        $textNodes = $xpath->query('//text()[normalize-space(.) != ""]');
        $paragraphs = $xpath->query('//p');

        // Process paragraphs first with Chinese character-aware filtering
        foreach ($paragraphs as $p) {
            $text = $this->cleanText($p->textContent);

            // Filter out JavaScript code like "loadAdv(2, 0);"
            if (preg_match('/^loadAdv\s*\(\s*\d+\s*,\s*\d+\s*\)\s*;?\s*$/', $text)) {
                continue;
            }

            // Use mb_strlen for proper Chinese character counting
            if (!empty($text) && mb_strlen($text, 'UTF-8') > 1) {
                $contentParts[] = $text;
            }
        }

        // FIXED: If no paragraphs, process text nodes with more lenient filtering
        if (empty($contentParts)) {
            foreach ($textNodes as $textNode) {
                $text = trim($textNode->textContent);

                // Filter out JavaScript code like "loadAdv(2, 0);"
                if (preg_match('/^loadAdv\s*\(\s*\d+\s*,\s*\d+\s*\)\s*;?\s*$/', $text)) {
                    continue;
                }

                if (!empty($text) && strlen($text) > 3) { // FIXED: Reduced from 10 to 3
                    $contentParts[] = $text;
                }
            }
        }

        // Join paragraphs with double newlines to preserve paragraph structure
        return implode("\n\n", $contentParts);
    }

    /**
     * REMOVED: Complex date-based extraction that was causing beginning content truncation
     * This method was trying to skip "duplicate titles" but was removing actual content
     */

    /**
     * REMOVED: Complex date-based extraction methods that were causing content truncation
     * These methods were trying to skip "duplicate titles" and clean content too aggressively
     */

    /**
     * Get innerHTML of an element
     */
    private function getInnerHTML(DOMElement $element): string {
        $innerHTML = '';
        foreach ($element->childNodes as $child) {
            $innerHTML .= $element->ownerDocument->saveHTML($child);
        }
        return $innerHTML;
    }

    /**
     * SIMPLIFIED: Basic content cleaning to prevent content loss
     * Removed aggressive patterns that were truncating actual story content
     */
    private function cleanExtractedContent(string $content): string {
        // Only remove obvious JavaScript code
        $content = preg_replace('/loadAdv\s*\(\s*\d+\s*,\s*\d+\s*\)\s*;?\s*/s', '', $content);

        // Only remove obvious navigation at line endings
        $content = preg_replace('/\n(上一章|下一章|目录|Copyright|69书吧)$/m', '', $content);

        // Basic whitespace cleanup
        $content = preg_replace('/\n{3,}/', "\n\n", $content);
        $content = trim($content);

        return $content;
    }

    /**
     * REMOVED: Strategy 1 was still too complex and causing content loss
     * Replaced with simpler extraction in main method
     */

    /**
     * REMOVED: Complex strategies 2 and 3 that were causing content truncation
     * These were using aggressive pattern matching that removed actual content
     */

    /**
     * SIMPLIFIED: Basic text formatting to prevent content loss
     * Removed complex processing that was causing truncation
     */
    private function formatExtractedText(string $text): string {
        // Basic cleanup only
        $text = preg_replace('/\n{3,}/', "\n\n", $text);
        $text = trim($text);
        return $text;
    }

    /**
     * Extract chapter text content (legacy method for other content structures)
     */
    private function extractChapterText(DOMElement $contentElement): string {
        $content = '';

        // First try to find paragraphs within the content element
        $xpath = new DOMXPath($contentElement->ownerDocument);
        $paragraphs = $xpath->query('.//p', $contentElement);

        if ($paragraphs->length > 0) {
            foreach ($paragraphs as $paragraph) {
                $text = $this->cleanText($paragraph->textContent);
                if (!empty($text)) {
                    $content .= $text . "\n\n";
                }
            }
        } else {
            // Fallback: process text nodes and br tags within the content element
            foreach ($contentElement->childNodes as $node) {
                if ($node->nodeType === XML_TEXT_NODE) {
                    $text = trim($node->textContent);
                    if (!empty($text)) {
                        $content .= $text;
                    }
                } elseif ($node->nodeType === XML_ELEMENT_NODE) {
                    switch ($node->nodeName) {
                        case 'br':
                            $content .= "\n";
                            break;
                        case 'p':
                        case 'div':
                            $text = $this->cleanText($node->textContent);
                            if (!empty($text)) {
                                $content .= $text . "\n\n";
                            }
                            break;
                        default:
                            $text = $this->cleanText($node->textContent);
                            if (!empty($text)) {
                                $content .= $text;
                            }
                            break;
                    }
                }
            }
        }

        // If still no content, try getting all text from the element
        if (empty($content)) {
            $content = $this->cleanText($contentElement->textContent);
        }

        // Clean up the content
        $content = preg_replace('/\n{3,}/', "\n\n", $content);
        $content = trim($content);

        return $content;
    }

    /**
     * Get alternative chapter URL format for 69shuba
     */
    private function getAlternativeChapterUrl(string $url): string {
        // Try different URL formats that might work
        if (preg_match('/\/txt\/(\d+)\/(\d+)/', $url, $matches)) {
            $bookId = $matches[1];
            $chapterId = $matches[2];

            // Try with .htm extension
            if (!str_ends_with($url, '.htm')) {
                return "https://69shuba.cx/txt/{$bookId}/{$chapterId}.htm";
            }

            // Try without .htm extension
            if (str_ends_with($url, '.htm')) {
                return "https://69shuba.cx/txt/{$bookId}/{$chapterId}";
            }
        }

        return $url; // Return original if no alternative found
    }

    /**
     * Get the correct base URL from the original URL
     */
    private function getBaseUrlFromOriginal(string $url): string {
        $parsedUrl = parse_url($url);
        if ($parsedUrl && isset($parsedUrl['scheme']) && isset($parsedUrl['host'])) {
            return $parsedUrl['scheme'] . '://' . $parsedUrl['host'];
        }

        return self::BASE_URL;
    }

    /**
     * Debug method to test chapter extraction step by step
     */
    public function debugChapterExtraction(string $chapterUrl): array {
        $debug = [];

        try {
            $debug['url'] = $chapterUrl;
            $debug['step'] = 'Making HTTP request';

            // Make request with cookies
            $options = [
                CURLOPT_ENCODING => '', // Let curl handle all supported encodings automatically
                CURLOPT_TIMEOUT => 30,
                CURLOPT_COOKIEJAR => sys_get_temp_dir() . '/69shuba_cookies.txt',
                CURLOPT_COOKIEFILE => sys_get_temp_dir() . '/69shuba_cookies.txt'
            ];

            $html = $this->makeRequest($chapterUrl, $options);
            $debug['html_length'] = strlen($html);
            $debug['html_preview'] = substr($html, 0, 500);

            // Check for anti-bot protection
            $debug['anti_bot_detected'] = strpos($html, 'Just a moment') !== false;
            $debug['blocked_detected'] = strpos($html, 'class="newlistbox"') !== false;

            if ($debug['anti_bot_detected'] || $debug['blocked_detected']) {
                $debug['step'] = 'Anti-bot protection detected';
                return $debug;
            }

            $debug['step'] = 'Parsing HTML';
            $dom = $this->parseHtml($html);

            // Check page structure
            $xpath = new DOMXPath($dom);
            $debug['mybox_count'] = $xpath->query('//div[contains(@class, "mybox")]')->length;
            $debug['txtnav_count'] = $xpath->query('//div[contains(@class, "txtnav")]')->length;
            $debug['page1_count'] = $xpath->query('//div[contains(@class, "page1")]')->length;
            $debug['paragraph_count'] = $xpath->query('//p')->length;

            // Get page title
            $titleElements = $xpath->query('//title');
            $debug['page_title'] = $titleElements->length > 0 ? $titleElements->item(0)->textContent : 'No title';

            $debug['step'] = 'Extracting chapter title';
            $title = $this->extractChapterTitle($dom);
            $debug['chapter_title'] = $title;

            $debug['step'] = 'Extracting content using simplified method';
            $content = $this->extractChapterContentDirect($dom);
            $debug['content_length'] = strlen($content);
            $debug['content_preview'] = substr($content, 0, 200);

            $debug['final_content_length'] = strlen($content);
            $debug['final_content_preview'] = substr($content, 0, 300);
            $debug['final_content_ending'] = substr($content, -300);

            $debug['step'] = 'Complete';

        } catch (Exception $e) {
            $debug['error'] = $e->getMessage();
            $debug['step'] = 'Error occurred';
        }

        return $debug;
    }
}
