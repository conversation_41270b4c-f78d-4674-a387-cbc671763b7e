<?php
/**
 * English Translation Quotation Mark Fixer
 * Converts Japanese quotation marks in English translated content to proper English double quotes
 */

require_once 'config/config.php';

class EnglishQuotationMarkFixer {
    
    /**
     * Fix Japanese quotation marks in English translated text
     * @param string $text The English text with Japanese quotation marks
     * @return array ['fixed_text' => string, 'changes' => array]
     */
    public function fixEnglishQuotationMarks($text) {
        $changes = [];
        $originalText = $text;
        
        // Step 1: Fix reversed quotation patterns (」...「 and 』...『)
        $text = $this->fixReversedQuotes($text, $changes);
        
        // Step 2: Convert all Japanese quotation marks to English quotes
        $text = $this->convertToEnglishQuotes($text, $changes);
        
        // Step 3: Fix consecutive English quotes and clean up
        $text = $this->cleanupEnglishQuotes($text, $changes);
        
        // Step 4: Fix specific corruption patterns
        $text = $this->fixCorruptionPatterns($text, $changes);
        
        return [
            'fixed_text' => $text,
            'changes' => $changes,
            'original_length' => strlen($originalText),
            'fixed_length' => strlen($text)
        ];
    }
    
    /**
     * Fix reversed quotation patterns where closing comes before opening
     */
    private function fixReversedQuotes($text, &$changes) {
        $reversedCount = 0;
        
        // Pattern: 」...「 should become "..."
        $text = preg_replace_callback('/」([^」「]*?)「/u', function($matches) use (&$reversedCount) {
            $reversedCount++;
            return '"' . $matches[1] . '"';
        }, $text);
        
        // Pattern: 』...『 should become "..."
        $text = preg_replace_callback('/』([^』『]*?)『/u', function($matches) use (&$reversedCount) {
            $reversedCount++;
            return '"' . $matches[1] . '"';
        }, $text);
        
        if ($reversedCount > 0) {
            $changes[] = [
                'type' => 'fix_reversed_quotes',
                'count' => $reversedCount
            ];
        }
        
        return $text;
    }
    
    /**
     * Convert all remaining Japanese quotation marks to English quotes
     */
    private function convertToEnglishQuotes($text, &$changes) {
        $conversionCount = 0;
        
        // Count original Japanese quotes
        $originalJapaneseQuotes = mb_substr_count($text, '「') + mb_substr_count($text, '」') + 
                                 mb_substr_count($text, '『') + mb_substr_count($text, '』');
        
        // Convert all Japanese quotes to English quotes
        $patterns = [
            '「' => '"',  // Opening single quote to English quote
            '」' => '"',  // Closing single quote to English quote
            '『' => '"',  // Opening double quote to English quote
            '』' => '"'   // Closing double quote to English quote
        ];
        
        foreach ($patterns as $japanese => $english) {
            $count = mb_substr_count($text, $japanese);
            if ($count > 0) {
                $text = str_replace($japanese, $english, $text);
                $conversionCount += $count;
            }
        }
        
        if ($conversionCount > 0) {
            $changes[] = [
                'type' => 'convert_japanese_to_english',
                'count' => $conversionCount,
                'original_japanese_quotes' => $originalJapaneseQuotes
            ];
        }
        
        return $text;
    }
    
    /**
     * Clean up consecutive English quotes and fix spacing
     */
    private function cleanupEnglishQuotes($text, &$changes) {
        $cleanupCount = 0;
        
        // Fix multiple consecutive quotes (e.g., """" becomes ")
        $originalText = $text;
        $text = preg_replace('/"{2,}/', '"', $text);
        
        if ($text !== $originalText) {
            $cleanupCount++;
            $changes[] = [
                'type' => 'cleanup_consecutive_quotes',
                'count' => $cleanupCount
            ];
        }
        
        // Fix spacing around quotes
        // Remove space before opening quotes after punctuation
        $text = preg_replace('/([.!?])\s+"/', '$1 "', $text);
        
        // Ensure proper spacing after closing quotes
        $text = preg_replace('/"([A-Z])/', '" $1', $text);
        
        return $text;
    }
    
    /**
     * Fix specific corruption patterns identified in the analysis
     */
    private function fixCorruptionPatterns($text, &$changes) {
        $corruptionFixes = 0;
        
        // Fix patterns like: "Stop—" instead of 」Stop—「
        // This should already be handled by the previous steps, but let's add specific fixes
        
        // Fix dialogue attribution patterns
        $text = preg_replace('/"\s*\n\s*"/', '"\n\n"', $text);
        
        // Fix broken dialogue where quotes are split incorrectly
        // Pattern: "text" "more text" should sometimes be "text more text"
        // But we need to be careful not to merge separate dialogue
        
        // Fix specific patterns mentioned in the request
        $specificPatterns = [
            // Fix cases where punctuation is outside quotes incorrectly
            '/"\s*([.!?])\s*"/' => '"$1"',
            // Fix spacing issues
            '/"\s+([A-Za-z])/' => '"$1',
            '/([A-Za-z])\s+"/' => '$1"'
        ];
        
        foreach ($specificPatterns as $pattern => $replacement) {
            $newText = preg_replace($pattern, $replacement, $text);
            if ($newText !== $text) {
                $corruptionFixes++;
                $text = $newText;
            }
        }
        
        if ($corruptionFixes > 0) {
            $changes[] = [
                'type' => 'fix_corruption_patterns',
                'count' => $corruptionFixes
            ];
        }
        
        return $text;
    }
    
    /**
     * Analyze quotation marks in English text
     */
    public function analyzeEnglishQuotationMarks($text) {
        $analysis = [
            'japanese_single_open' => mb_substr_count($text, '「'),
            'japanese_single_close' => mb_substr_count($text, '」'),
            'japanese_double_open' => mb_substr_count($text, '『'),
            'japanese_double_close' => mb_substr_count($text, '』'),
            'english_quotes' => substr_count($text, '"'),
            'total_japanese_quotes' => 0,
            'issues' => []
        ];
        
        $analysis['total_japanese_quotes'] = $analysis['japanese_single_open'] + 
                                           $analysis['japanese_single_close'] + 
                                           $analysis['japanese_double_open'] + 
                                           $analysis['japanese_double_close'];
        
        // Check for issues
        if ($analysis['total_japanese_quotes'] > 0) {
            $analysis['issues'][] = 'Japanese quotation marks found in English text';
        }
        
        // Check for reversed patterns
        if (preg_match('/」[^」「]*「/u', $text)) {
            $analysis['issues'][] = 'Reversed single quote patterns found';
        }
        
        if (preg_match('/』[^』『]*『/u', $text)) {
            $analysis['issues'][] = 'Reversed double quote patterns found';
        }
        
        // Check for consecutive quotes
        if (preg_match('/"{2,}/', $text)) {
            $analysis['issues'][] = 'Consecutive English quotes found';
        }
        
        return $analysis;
    }
}

// Main execution
if (basename(__FILE__) === basename($_SERVER['SCRIPT_NAME'])) {
    echo "=== ENGLISH QUOTATION MARK FIXER ===\n";
    echo "Date: " . date('Y-m-d H:i:s') . "\n\n";
    
    try {
        $db = Database::getInstance();
        $fixer = new EnglishQuotationMarkFixer();
        
        // Get chapter 24 translated content
        $chapter = $db->fetchOne(
            "SELECT id, translated_content FROM chapters WHERE novel_id = 3 AND chapter_number = 24",
            []
        );
        
        if (!$chapter) {
            echo "ERROR: Chapter 24 not found\n";
            exit(1);
        }
        
        if (empty($chapter['translated_content'])) {
            echo "ERROR: No translated content found\n";
            exit(1);
        }
        
        echo "1. ANALYZING CURRENT ENGLISH QUOTATION MARKS:\n";
        echo "=" . str_repeat("=", 50) . "\n";
        
        $analysis = $fixer->analyzeEnglishQuotationMarks($chapter['translated_content']);
        foreach ($analysis as $key => $value) {
            if ($key === 'issues') {
                echo "Issues found:\n";
                foreach ($value as $issue) {
                    echo "  - {$issue}\n";
                }
            } else {
                echo "{$key}: {$value}\n";
            }
        }
        
        if (empty($analysis['issues'])) {
            echo "\nNo issues found. Content appears to be correctly formatted.\n";
            exit(0);
        }
        
        echo "\n2. APPLYING FIXES:\n";
        echo "=" . str_repeat("=", 50) . "\n";
        
        $result = $fixer->fixEnglishQuotationMarks($chapter['translated_content']);
        
        echo "Changes made:\n";
        foreach ($result['changes'] as $change) {
            echo "  - {$change['type']}";
            if (isset($change['count'])) echo " (count: {$change['count']})";
            if (isset($change['original_japanese_quotes'])) echo " (converted {$change['original_japanese_quotes']} Japanese quotes)";
            echo "\n";
        }
        
        echo "\nText length: {$result['original_length']} -> {$result['fixed_length']}\n";
        
        // Verify the fixes
        echo "\n3. VERIFYING FIXES:\n";
        echo "=" . str_repeat("=", 50) . "\n";
        
        $newAnalysis = $fixer->analyzeEnglishQuotationMarks($result['fixed_text']);
        foreach ($newAnalysis as $key => $value) {
            if ($key === 'issues') {
                if (empty($value)) {
                    echo "Issues: None found\n";
                } else {
                    echo "Remaining issues:\n";
                    foreach ($value as $issue) {
                        echo "  - {$issue}\n";
                    }
                }
            } else {
                echo "{$key}: {$value}\n";
            }
        }
        
        // Show sample of fixed content
        echo "\n4. SAMPLE OF FIXED CONTENT:\n";
        echo "=" . str_repeat("=", 50) . "\n";
        echo substr($result['fixed_text'], 0, 500) . "...\n";
        
        // Ask for confirmation before updating
        echo "\n5. UPDATE DATABASE?\n";
        echo "=" . str_repeat("=", 50) . "\n";
        echo "Do you want to update the database with the fixed content? (y/N): ";
        $handle = fopen("php://stdin", "r");
        $line = fgets($handle);
        fclose($handle);
        
        if (trim(strtolower($line)) === 'y') {
            $db->update(
                'chapters',
                ['translated_content' => $result['fixed_text']],
                'id = ?',
                [$chapter['id']]
            );
            echo "Database updated successfully!\n";
        } else {
            echo "Database not updated. Fixed content available in result.\n";
        }
        
    } catch (Exception $e) {
        echo "ERROR: " . $e->getMessage() . "\n";
        exit(1);
    }
}
