<?php
/**
 * API Endpoint: Fetch New Chapters
 * POST /api/fetch-new-chapters.php
 */

// Set error handling to prevent HTML output
ini_set('display_errors', 0);
error_reporting(0);

require_once '../config/config.php';

/**
 * Clean data for JSON encoding
 */
function cleanDataForJson($data) {
    if (is_array($data)) {
        return array_map('cleanDataForJson', $data);
    } elseif (is_string($data)) {
        // Remove null bytes and fix encoding
        $cleaned = str_replace("\0", '', $data);
        if (!mb_check_encoding($cleaned, 'UTF-8')) {
            $cleaned = mb_convert_encoding($cleaned, 'UTF-8', 'auto');
        }
        return $cleaned;
    }
    return $data;
}

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    jsonResponse(['error' => 'Method not allowed'], 405);
}

try {
    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);

    if (!$input) {
        jsonResponse(['error' => 'Invalid JSON input'], 400);
    }

    // Validate required parameters
    if (!isset($input['novel_id']) || !is_numeric($input['novel_id'])) {
        jsonResponse(['error' => 'Valid novel_id is required'], 400);
    }

    $novelId = (int)$input['novel_id'];
    $fetchAll = isset($input['fetch_all']) ? (bool)$input['fetch_all'] : false;
    $specificChapters = isset($input['chapter_numbers']) && is_array($input['chapter_numbers']) 
        ? array_map('intval', $input['chapter_numbers']) 
        : [];

    // Get novel information from database
    $db = Database::getInstance();
    $novel = $db->fetchOne(
        "SELECT * FROM novels WHERE id = ?",
        [$novelId]
    );

    if (!$novel) {
        jsonResponse(['error' => 'Novel not found'], 404);
    }

    // Validate platform support
    $supportedPlatforms = ['kakuyomu', 'syosetu', 'shuba69', 'dxmwx'];
    if (!in_array($novel['platform'], $supportedPlatforms)) {
        jsonResponse(['error' => 'Fetching new chapters is not supported for this platform'], 400);
    }

    // Get current chapters from database
    $currentChapters = $db->fetchAll(
        "SELECT chapter_number FROM chapters WHERE novel_id = ? ORDER BY chapter_number",
        [$novelId]
    );
    $currentChapterNumbers = array_column($currentChapters, 'chapter_number');

    // Create appropriate crawler for the platform
    $novelManager = new NovelManager();
    $crawler = $novelManager->getCrawler($novel['platform']);

    try {
        $latestChapters = $crawler->getChapterList($novel['url']);
    } catch (Exception $e) {
        logError('Fetch new chapters: Failed to fetch chapter list', [
            'error' => $e->getMessage(),
            'url' => $novel['url'],
            'platform' => $novel['platform']
        ]);
        jsonResponse([
            'success' => false,
            'error' => 'Failed to fetch chapter list from source website: ' . $e->getMessage()
        ], 500);
    }

    // Determine which chapters to fetch
    $chaptersToFetch = [];
    $maxChaptersPerRequest = 10; // Define this early to use in the loop

    if ($fetchAll) {
        // Fetch all new chapters (but limit the search to prevent timeouts)
        foreach ($latestChapters as $chapter) {
            if (!in_array($chapter['chapter_number'], $currentChapterNumbers)) {
                $chaptersToFetch[] = $chapter;

                // Early exit when we have enough chapters to process
                if (count($chaptersToFetch) >= $maxChaptersPerRequest) {
                    break;
                }
            }
        }
    } elseif (!empty($specificChapters)) {
        // Fetch specific chapters
        foreach ($latestChapters as $chapter) {
            if (in_array($chapter['chapter_number'], $specificChapters) &&
                !in_array($chapter['chapter_number'], $currentChapterNumbers)) {
                $chaptersToFetch[] = $chapter;

                // Early exit when we have enough chapters to process
                if (count($chaptersToFetch) >= $maxChaptersPerRequest) {
                    break;
                }
            }
        }
    } else {
        jsonResponse(['error' => 'Either fetch_all must be true or chapter_numbers must be provided'], 400);
    }

    if (empty($chaptersToFetch)) {
        jsonResponse([
            'success' => true,
            'message' => 'No new chapters to fetch',
            'chapters_added' => 0
        ]);
    }

    // Estimate total new chapters available (for response message)
    // Use a simple calculation to avoid iterating through all chapters again
    $latestChapterCount = count($latestChapters);
    $currentChapterCount = count($currentChapterNumbers);
    $totalNewChapters = max(0, $latestChapterCount - $currentChapterCount);

    $db->beginTransaction();
    $successCount = 0;
    $errorCount = 0;
    $errors = [];

    try {
        foreach ($chaptersToFetch as $index => $chapterInfo) {
            try {
                // Validate chapter data
                if (!isset($chapterInfo['chapter_number']) || !isset($chapterInfo['chapter_url']) || !isset($chapterInfo['original_title'])) {
                    $errorCount++;
                    $errors[] = "Chapter data incomplete for index {$index}";
                    continue;
                }

                // Check if chapter already exists (double-check)
                $existingChapter = $db->fetchOne(
                    "SELECT id FROM chapters WHERE novel_id = ? AND chapter_number = ?",
                    [$novelId, $chapterInfo['chapter_number']]
                );

                if ($existingChapter) {
                    continue; // Skip if already exists
                }

                // Insert chapter record
                $chapterId = $db->insert('chapters', [
                    'novel_id' => $novelId,
                    'chapter_number' => $chapterInfo['chapter_number'],
                    'chapter_url' => $chapterInfo['chapter_url'],
                    'original_title' => $chapterInfo['original_title'],
                    'translation_status' => 'pending',
                    'created_at' => date('Y-m-d H:i:s')
                ]);

                if ($chapterId) {
                    $successCount++;
                } else {
                    $errorCount++;
                    $errors[] = "Failed to insert chapter {$chapterInfo['chapter_number']}";
                }

            } catch (Exception $e) {
                $errorCount++;
                $errors[] = "Chapter {$chapterInfo['chapter_number']}: " . $e->getMessage();
                logError('Fetch new chapter error: ' . $e->getMessage(), [
                    'novel_id' => $novelId,
                    'chapter_number' => $chapterInfo['chapter_number'] ?? 'unknown'
                ]);
            }
        }

        // Update novel's total chapter count
        $totalChapters = $db->fetchOne(
            "SELECT COUNT(*) as count FROM chapters WHERE novel_id = ?",
            [$novelId]
        )['count'];

        $db->update('novels',
            ['total_chapters' => $totalChapters],
            'id = ?',
            [$novelId]
        );

        $db->commit();

        $message = "Successfully added {$successCount} new chapters";
        if ($errorCount > 0) {
            $message .= " ({$errorCount} errors)";
        }

        // Add information about remaining chapters if there are more to fetch
        $remainingChapters = $totalNewChapters - count($chaptersToFetch);
        if ($remainingChapters > 0) {
            $message .= ". {$remainingChapters} more chapters available - run again to fetch more.";
        }

        $responseData = cleanDataForJson([
            'success' => true,
            'message' => $message,
            'chapters_added' => $successCount,
            'chapters_failed' => $errorCount,
            'errors' => $errors,
            'total_chapters_now' => $totalChapters,
            'remaining_chapters' => $remainingChapters,
            'has_more_chapters' => $remainingChapters > 0
        ]);

        jsonResponse($responseData);

    } catch (Exception $e) {
        $db->rollback();
        throw $e;
    }

} catch (Exception $e) {
    logError('Fetch new chapters API error: ' . $e->getMessage(), [
        'novel_id' => $input['novel_id'] ?? 'not provided',
        'trace' => $e->getTraceAsString()
    ]);

    jsonResponse([
        'success' => false,
        'error' => 'An error occurred while fetching new chapters: ' . $e->getMessage()
    ], 500);
}
