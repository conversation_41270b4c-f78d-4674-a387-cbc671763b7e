<?php
/**
 * API Endpoint: Translation Quality Fix
 * POST /api/quality-fix.php - Apply automatic corrections to translated content
 */

require_once '../config/config.php';
require_once '../config/database.php';
require_once '../classes/TranslationQualityFixService.php';

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

$method = $_SERVER['REQUEST_METHOD'];

// Handle preflight requests
if ($method === 'OPTIONS') {
    http_response_code(200);
    exit;
}

try {
    switch ($method) {
        case 'POST':
            handleQualityFix();
            break;
        default:
            jsonResponse(['error' => 'Method not allowed'], 405);
    }
} catch (Exception $e) {
    logError('Quality Fix API Error: ' . $e->getMessage(), [
        'method' => $method,
        'trace' => $e->getTraceAsString()
    ]);

    jsonResponse([
        'success' => false,
        'error' => 'Quality fix failed: ' . $e->getMessage()
    ], 500);
}

/**
 * Handle quality fix request
 */
function handleQualityFix() {
    $rawInput = file_get_contents('php://input');
    $input = json_decode($rawInput, true);

    if (!$input) {
        jsonResponse(['error' => 'Invalid JSON input'], 400);
    }

    // Validate required parameters
    $novelId = (int)($input['novel_id'] ?? 0);
    $chapterNumber = (int)($input['chapter_number'] ?? 0);
    $fixTypes = $input['fix_types'] ?? [];
    $corrections = $input['corrections'] ?? [];

    if (!$novelId || !$chapterNumber) {
        jsonResponse(['error' => 'Novel ID and chapter number are required'], 400);
    }

    if (empty($fixTypes)) {
        jsonResponse(['error' => 'At least one fix type must be specified'], 400);
    }

    logError('Quality Fix API: Request received', [
        'novel_id' => $novelId,
        'chapter_number' => $chapterNumber,
        'fix_types' => $fixTypes,
        'corrections_count' => count($corrections)
    ]);

    try {
        // Initialize quality fix service
        $qualityFixService = new TranslationQualityFixService();

        // Apply corrections
        $result = $qualityFixService->applyCorrections($novelId, $chapterNumber, $fixTypes, $corrections);

        if ($result['success']) {
            jsonResponse([
                'success' => true,
                'fixes_applied' => $result['fixes_applied'],
                'updated_content' => $result['updated_content'],
                'summary' => $result['summary']
            ]);
        } else {
            jsonResponse([
                'success' => false,
                'error' => $result['error']
            ], 400);
        }

    } catch (Exception $e) {
        logError('Quality fix execution error: ' . $e->getMessage(), [
            'novel_id' => $novelId,
            'chapter_number' => $chapterNumber,
            'fix_types' => $fixTypes,
            'trace' => $e->getTraceAsString()
        ]);

        jsonResponse([
            'success' => false,
            'error' => 'Quality fix failed: ' . $e->getMessage()
        ], 500);
    }
}

/**
 * Handle preview corrections request
 */
function handlePreviewCorrections() {
    $rawInput = file_get_contents('php://input');
    $input = json_decode($rawInput, true);

    if (!$input) {
        jsonResponse(['error' => 'Invalid JSON input'], 400);
    }

    $novelId = (int)($input['novel_id'] ?? 0);
    $chapterNumber = (int)($input['chapter_number'] ?? 0);
    $corrections = $input['corrections'] ?? [];

    if (!$novelId || !$chapterNumber) {
        jsonResponse(['error' => 'Novel ID and chapter number are required'], 400);
    }

    try {
        $qualityFixService = new TranslationQualityFixService();
        $preview = $qualityFixService->previewCorrections($novelId, $chapterNumber, $corrections);

        jsonResponse([
            'success' => true,
            'preview' => $preview
        ]);

    } catch (Exception $e) {
        logError('Preview corrections error: ' . $e->getMessage(), [
            'novel_id' => $novelId,
            'chapter_number' => $chapterNumber,
            'trace' => $e->getTraceAsString()
        ]);

        jsonResponse([
            'success' => false,
            'error' => 'Preview failed: ' . $e->getMessage()
        ], 500);
    }
}
?>
