<?php
require_once 'config/config.php';

echo "=== RE-TRANSLATING CHAPTER 163 TO GET CLEAN CONTENT ===\n";
echo "Date: " . date('Y-m-d H:i:s') . "\n\n";

try {
    $novelManager = new NovelManager();
    $db = Database::getInstance();
    
    // Get current chapter 163 info
    $chapter = $db->fetchOne(
        "SELECT * FROM chapters WHERE novel_id = 1 AND chapter_number = 163",
        []
    );
    
    if (!$chapter) {
        echo "ERROR: Chapter 163 not found\n";
        exit(1);
    }
    
    echo "Chapter found: " . $chapter['original_title'] . "\n";
    echo "Current translation status: " . $chapter['translation_status'] . "\n";
    
    if (empty($chapter['original_content'])) {
        echo "ERROR: No original content to translate\n";
        exit(1);
    }
    
    echo "Original content length: " . strlen($chapter['original_content']) . " characters\n\n";
    
    // Clear the current translated content to force re-translation
    echo "Clearing current translated content...\n";
    $db->update('chapters', 
        [
            'translated_content' => null,
            'translation_status' => 'pending'
        ],
        'id = ?',
        [$chapter['id']]
    );
    
    echo "Starting re-translation...\n";
    
    // Translate the chapter
    $result = $novelManager->translateChapter(1, 163, 'en');
    
    if ($result['success']) {
        echo "✓ Chapter 163 re-translated successfully!\n";
        
        // Get the new translated content
        $updatedChapter = $db->fetchOne(
            "SELECT translated_content FROM chapters WHERE novel_id = 1 AND chapter_number = 163",
            []
        );
        
        if ($updatedChapter && !empty($updatedChapter['translated_content'])) {
            $newContent = $updatedChapter['translated_content'];
            
            // Check for issues in the new translation
            preg_match_all('/-san\d+|-kun\d+|-chan\d+|-sama\d+/', $newContent, $suffixMatches);
            preg_match_all('/」[^「]*「/', $newContent, $punctuationMatches);
            
            echo "\nIssues in new translation:\n";
            echo "- Suffix issues: " . count($suffixMatches[0]) . "\n";
            echo "- Punctuation issues: " . count($punctuationMatches[0]) . "\n";
            
            if (count($suffixMatches[0]) > 0) {
                echo "Suffix examples: " . implode(', ', array_slice($suffixMatches[0], 0, 5)) . "\n";
            }
            
            echo "\nSample of new content (first 800 characters):\n";
            echo "=" . str_repeat("=", 60) . "\n";
            echo substr($newContent, 0, 800) . "\n";
            echo "=" . str_repeat("=", 60) . "\n";
            
        } else {
            echo "⚠ Warning: No translated content found after translation\n";
        }
        
    } else {
        echo "✗ Translation failed: " . ($result['error'] ?? 'Unknown error') . "\n";
    }
    
} catch (Exception $e) {
    echo "ERROR: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

echo "\n=== RE-TRANSLATION COMPLETE ===\n";
?>
