<?php
/**
 * Detailed content examination for chapter 24
 */

require_once 'config/config.php';

echo "=== DETAILED CONTENT EXAMINATION - CHAPTER 24 ===\n";
echo "Date: " . date('Y-m-d H:i:s') . "\n\n";

try {
    $db = Database::getInstance();
    
    $chapter = $db->fetchOne(
        "SELECT * FROM chapters WHERE novel_id = 3 AND chapter_number = 24",
        []
    );
    
    if (!$chapter) {
        echo "ERROR: Chapter not found\n";
        exit(1);
    }
    
    echo "1. ORIGINAL JAPANESE CONTENT (first 1000 characters):\n";
    echo "=" . str_repeat("=", 60) . "\n";
    $originalContent = $chapter['original_content'];
    echo mb_substr($originalContent, 0, 1000) . "\n";
    if (mb_strlen($originalContent) > 1000) {
        echo "... (truncated, total length: " . mb_strlen($originalContent) . " characters)\n";
    }
    
    echo "\n2. FULL ORIGINAL JAPANESE CONTENT:\n";
    echo "=" . str_repeat("=", 60) . "\n";
    echo $originalContent . "\n";

    echo "\n3. ANALYZING JAPANESE QUOTATION MARKS:\n";
    echo "=" . str_repeat("=", 60) . "\n";

    // Count different types of quotation marks
    $singleOpen = mb_substr_count($originalContent, '「');
    $singleClose = mb_substr_count($originalContent, '」');
    $doubleOpen = mb_substr_count($originalContent, '『');
    $doubleClose = mb_substr_count($originalContent, '』');

    echo "Single quotes 「」: Open={$singleOpen}, Close={$singleClose}\n";
    echo "Double quotes 『』: Open={$doubleOpen}, Close={$doubleClose}\n";

    // Check for balance
    if ($singleOpen !== $singleClose) {
        echo "WARNING: Unbalanced single quotes!\n";
    }
    if ($doubleOpen !== $doubleClose) {
        echo "WARNING: Unbalanced double quotes!\n";
    }

    // Find all quotation mark positions and analyze context
    echo "\n4. QUOTATION MARK ANALYSIS:\n";
    echo "=" . str_repeat("=", 60) . "\n";

    $quotes = [];
    $offset = 0;

    // Find all quotation marks
    foreach (['「', '」', '『', '』'] as $mark) {
        $offset = 0;
        while (($pos = mb_strpos($originalContent, $mark, $offset)) !== false) {
            $quotes[] = ['mark' => $mark, 'pos' => $pos];
            $offset = $pos + 1;
        }
    }

    // Sort by position
    usort($quotes, function($a, $b) { return $a['pos'] - $b['pos']; });

    echo "Found " . count($quotes) . " quotation marks in order:\n";
    foreach ($quotes as $i => $quote) {
        $start = max(0, $quote['pos'] - 20);
        $length = 40;
        $context = mb_substr($originalContent, $start, $length);
        $context = str_replace($quote['mark'], "**{$quote['mark']}**", $context);
        echo sprintf("%2d. %s at pos %d: ...%s...\n", $i+1, $quote['mark'], $quote['pos'], $context);
    }

    echo "\n5. TRANSLATED ENGLISH CONTENT (first 1000 characters):\n";
    echo "=" . str_repeat("=", 60) . "\n";
    $translatedContent = $chapter['translated_content'];
    echo substr($translatedContent, 0, 1000) . "\n";
    if (strlen($translatedContent) > 1000) {
        echo "... (truncated, total length: " . strlen($translatedContent) . " characters)\n";
    }
    
    echo "\n3. SEARCHING FOR POTENTIAL CHARACTER REFERENCES IN ORIGINAL:\n";
    echo "=" . str_repeat("=", 60) . "\n";
    
    // Look for various patterns that might indicate character references
    $patterns = [
        'pronouns' => '/(?:彼|彼女|君|あなた|お前|俺|僕|私)/u',
        'dialogue_markers' => '/「[^」]*」/u',
        'potential_names' => '/[ァ-ヶ一-龯]{2,4}(?=[はがをにの、。！？])/u',
        'second_person' => '/(?:君|あなた|お前)/u'
    ];
    
    foreach ($patterns as $name => $pattern) {
        preg_match_all($pattern, $originalContent, $matches);
        echo "{$name}: " . count($matches[0]) . " matches\n";
        if ($name === 'second_person' && !empty($matches[0])) {
            $unique = array_unique($matches[0]);
            echo "  Found: " . implode(', ', $unique) . "\n";
        }
    }
    
    echo "\n4. ANALYZING STANDALONE -KUN CONTEXTS IN DETAIL:\n";
    echo "=" . str_repeat("=", 60) . "\n";
    
    // Find all -kun positions and show extended context
    $positions = [];
    $offset = 0;
    while (($pos = strpos($translatedContent, '-kun', $offset)) !== false) {
        $positions[] = $pos;
        $offset = $pos + 1;
    }
    
    foreach ($positions as $i => $pos) {
        echo "Occurrence " . ($i + 1) . " at position {$pos}:\n";
        $start = max(0, $pos - 150);
        $length = 300;
        $context = substr($translatedContent, $start, $length);
        
        // Highlight the -kun
        $context = str_replace('-kun', '**-kun**', $context);
        echo "Context: ...{$context}...\n\n";
    }
    
    echo "\n5. CHECKING FOR JAPANESE PRONOUNS THAT MIGHT BE TRANSLATED AS -KUN:\n";
    echo "=" . str_repeat("=", 60) . "\n";
    
    // Look for 君 (kimi/kun) in original text
    $kimiCount = mb_substr_count($originalContent, '君');
    echo "Occurrences of '君' (kimi/kun) in original: {$kimiCount}\n";
    
    if ($kimiCount > 0) {
        // Find contexts where 君 appears
        $kimiPositions = [];
        $offset = 0;
        while (($pos = mb_strpos($originalContent, '君', $offset)) !== false) {
            $kimiPositions[] = $pos;
            $offset = $pos + 1;
        }
        
        echo "Contexts where '君' appears:\n";
        foreach ($kimiPositions as $i => $pos) {
            $start = max(0, $pos - 20);
            $length = 40;
            $context = mb_substr($originalContent, $start, $length);
            $context = str_replace('君', '**君**', $context);
            echo "  " . ($i + 1) . ". ...{$context}...\n";
        }
    }
    
    echo "\n6. CHECKING TRANSLATION PROCESS LOGS:\n";
    echo "=" . str_repeat("=", 60) . "\n";
    
    // Check if there are any debug logs related to this chapter
    if (file_exists('debug.log')) {
        $debugContent = file_get_contents('debug.log');
        $lines = explode("\n", $debugContent);
        
        $relevantLines = [];
        foreach ($lines as $line) {
            if (strpos($line, 'Chapter ID: 391') !== false || 
                strpos($line, 'chapter_id: 391') !== false ||
                (strpos($line, 'Novel ID: 3') !== false && strpos($line, 'Chapter') !== false)) {
                $relevantLines[] = $line;
            }
        }
        
        if (!empty($relevantLines)) {
            echo "Found " . count($relevantLines) . " relevant debug log entries:\n";
            foreach (array_slice($relevantLines, -10) as $line) { // Show last 10
                echo "  {$line}\n";
            }
        } else {
            echo "No specific debug logs found for this chapter.\n";
        }
    }
    
} catch (Exception $e) {
    echo "ERROR: " . $e->getMessage() . "\n";
}

echo "\n=== EXAMINATION COMPLETE ===\n";
?>
