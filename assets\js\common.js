/**
 * Common JavaScript Utilities
 * Shared functions across all pages
 */

class CommonUtils {
    constructor() {
        this.initializeToasts();
    }

    initializeToasts() {
        // Initialize toast notifications
        this.toastElement = document.getElementById('toast');
        if (this.toastElement) {
            this.toast = new bootstrap.Toast(this.toastElement);
            console.log('CommonUtils: Toast notifications initialized successfully');
        } else {
            console.warn('CommonUtils: Toast element not found - notifications may not work');
        }

        // Add debugging for browser extension errors (content-all.js issue)
        this.setupExtensionErrorHandler();
    }

    setupExtensionErrorHandler() {
        // Listen for browser extension errors that don't affect our app
        window.addEventListener('error', (event) => {
            if (event.filename && (
                event.filename.includes('content-all.js') ||
                event.filename.includes('extension') ||
                event.filename.includes('chrome-extension') ||
                event.filename.includes('moz-extension')
            )) {
                console.log('CommonUtils: Detected browser extension error - this is harmless and does not affect the application');
                event.preventDefault(); // Prevent the error from appearing in console
                return false;
            }
        });

        // Listen for unhandled promise rejections from extensions
        window.addEventListener('unhandledrejection', (event) => {
            if (event.reason && event.reason.message && (
                event.reason.message.includes('Could not establish connection') ||
                event.reason.message.includes('Receiving end does not exist') ||
                event.reason.message.includes('Extension context invalidated') ||
                event.reason.message.includes('chrome.runtime') ||
                event.reason.message.includes('browser.runtime')
            )) {
                console.log('CommonUtils: Detected browser extension connection error - this is harmless and does not affect the application');
                event.preventDefault(); // Prevent the error from appearing in console
                return false;
            }
        });

        // Additional handler for extension-related console errors
        const originalConsoleError = console.error;
        console.error = function(...args) {
            const message = args.join(' ');
            if (message.includes('content-all.js') ||
                message.includes('Could not establish connection') ||
                message.includes('Receiving end does not exist')) {
                // Silently ignore extension errors
                return;
            }
            originalConsoleError.apply(console, args);
        };
    }

    // Show loading overlay
    showLoading(show = true) {
        const overlay = document.getElementById('loading-overlay');
        if (overlay) {
            overlay.style.display = show ? 'flex' : 'none';
        }
    }

    // Progress bar functionality
    createProgressBar(containerId, options = {}) {
        const container = document.getElementById(containerId);
        if (!container) {
            console.error(`Progress bar container '${containerId}' not found. Available elements:`,
                Array.from(document.querySelectorAll('[id]')).map(el => el.id).filter(id => id));
            return null;
        }

        const defaults = {
            title: 'Processing...',
            showPercentage: true,
            showStatus: true,
            animated: true,
            striped: true,
            color: 'primary'
        };

        const config = { ...defaults, ...options };

        // Create progress bar HTML
        const progressBarHtml = `
            <div class="progress-container mb-3" style="display: none;">
                <div class="progress-header d-flex justify-content-between align-items-center mb-2">
                    <span class="progress-title">${config.title}</span>
                    ${config.showPercentage ? '<span class="progress-percentage">0%</span>' : ''}
                </div>
                <div class="progress" style="height: 20px;">
                    <div class="progress-bar ${config.animated ? 'progress-bar-animated' : ''} ${config.striped ? 'progress-bar-striped' : ''} bg-${config.color}"
                         role="progressbar"
                         style="width: 0%"
                         aria-valuenow="0"
                         aria-valuemin="0"
                         aria-valuemax="100">
                    </div>
                </div>
                ${config.showStatus ? '<div class="progress-status mt-2 text-muted"></div>' : ''}
            </div>
        `;

        container.innerHTML = progressBarHtml;

        const progressContainer = container.querySelector('.progress-container');
        const progressBar = container.querySelector('.progress-bar');
        const progressPercentage = container.querySelector('.progress-percentage');
        const progressStatus = container.querySelector('.progress-status');
        const progressTitle = container.querySelector('.progress-title');

        return {
            containerId: containerId,
            progressBar: progressBar,
            show() {
                progressContainer.style.display = 'block';
                progressContainer.classList.add('show');
            },
            hide() {
                progressContainer.style.display = 'none';
                progressContainer.classList.remove('show');
            },
            setProgress(percentage, status = '') {
                const clampedPercentage = Math.max(0, Math.min(100, percentage));

                progressBar.style.width = `${clampedPercentage}%`;
                progressBar.setAttribute('aria-valuenow', clampedPercentage);

                if (progressPercentage) {
                    progressPercentage.textContent = `${Math.round(clampedPercentage)}%`;
                }

                if (progressStatus && status) {
                    progressStatus.textContent = status;
                }
            },
            setTitle(title) {
                if (progressTitle) {
                    progressTitle.textContent = title;
                }
            },
            setStatus(status) {
                if (progressStatus) {
                    progressStatus.textContent = status;
                }
            },
            setColor(color) {
                progressBar.className = progressBar.className.replace(/bg-\w+/, `bg-${color}`);
            },
            complete(message = 'Completed!') {
                this.setProgress(100, message);
                this.setColor('success');

                // Auto-hide after 2 seconds
                setTimeout(() => {
                    this.hide();
                }, 2000);
            },
            error(message = 'Error occurred') {
                this.setColor('danger');
                this.setStatus(message);

                // Auto-hide after 3 seconds
                setTimeout(() => {
                    this.hide();
                }, 3000);
            }
        };
    }

    // Show toast notification
    showToast(message, type = 'info') {
        if (!this.toastElement) {
            console.warn('CommonUtils: Cannot show toast - toast element not found');
            // Fallback to alert for critical messages
            if (type === 'error') {
                alert('Error: ' + message);
            }
            return;
        }

        const toastBody = this.toastElement.querySelector('.toast-body');

        // Set message and style
        toastBody.textContent = message;
        this.toastElement.className = `toast ${this.getToastClass(type)}`;

        // Show toast
        if (this.toast) {
            this.toast.show();
            console.log(`CommonUtils: Showing ${type} toast: ${message}`);
        } else {
            console.warn('CommonUtils: Toast instance not available');
            // Fallback to alert for critical messages
            if (type === 'error') {
                alert('Error: ' + message);
            }
        }
    }

    getToastClass(type) {
        const classes = {
            'error': 'bg-danger text-white',
            'success': 'bg-success text-white',
            'warning': 'bg-warning text-dark',
            'info': 'bg-info text-white'
        };
        return classes[type] || classes['info'];
    }

    // Escape HTML to prevent XSS
    escapeHtml(text) {
        if (!text) return '';
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    // Get platform display name
    getPlatformName(platform) {
        const platforms = {
            'kakuyomu': 'Kakuyomu',
            'syosetu': 'Syosetu',
            'shuba69': '69书吧',
            'dxmwx': '大熊猫文学',
            'manual': 'Manual Entry'
        };
        return platforms[platform] || platform;
    }

    // Get status display text
    getStatusText(status) {
        const statuses = {
            'pending': 'Pending',
            'saved': 'Saved',
            'translating': 'Translating...',
            'completed': 'Completed',
            'error': 'Error'
        };
        return statuses[status] || status;
    }

    // Format date for display
    formatDate(dateString) {
        if (!dateString) return 'Unknown';
        try {
            const date = new Date(dateString);
            return date.toLocaleDateString();
        } catch (e) {
            return dateString;
        }
    }

    // Calculate progress percentage
    calculateProgress(completed, total) {
        if (!total || total === 0) return 0;
        return Math.round((completed / total) * 100);
    }

    // Smooth scroll to element
    scrollToElement(elementId, offset = 0) {
        const element = document.getElementById(elementId);
        if (element) {
            const elementPosition = element.getBoundingClientRect().top + window.pageYOffset;
            window.scrollTo({
                top: elementPosition - offset,
                behavior: 'smooth'
            });
        }
    }

    // Debounce function for search inputs
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // Make API request with enhanced error handling and optional progress tracking
    async makeApiRequest(url, options = {}, isTranslationRequest = false) {
        // Use different timeouts for translation vs regular requests
        const timeoutDuration = isTranslationRequest ? 480000 : 120000; // 8 minutes for translations, 2 minutes for others
        const timeoutMinutes = Math.round(timeoutDuration / 60000);

        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), timeoutDuration);

        // Track request start time for better error reporting
        const startTime = Date.now();

        try {
            // Prepare headers - don't set Content-Type for FormData (let browser handle it)
            const headers = { ...options.headers };

            // Only set Content-Type to application/json if body is not FormData
            if (!(options.body instanceof FormData)) {
                headers['Content-Type'] = 'application/json';
            }

            const response = await fetch(url, {
                headers: headers,
                signal: controller.signal,
                ...options
            });

            clearTimeout(timeoutId);

            // Get response text first to handle non-JSON responses
            const responseText = await response.text();

            // Try to parse as JSON
            let result;
            try {
                result = JSON.parse(responseText);
            } catch (jsonError) {
                // If JSON parsing fails, log the response for debugging
                console.error('Invalid JSON response:', responseText.substring(0, 500));
                console.error('Response length:', responseText.length);
                console.error('Response headers:', response.headers);
                console.error('JSON parse error:', jsonError.message);

                // Check for common issues
                if (responseText.includes('<html>') || responseText.includes('<!DOCTYPE')) {
                    throw new Error(`Server returned HTML instead of JSON. This indicates a PHP error or server misconfiguration.`);
                } else if (responseText.trim() === '') {
                    throw new Error(`Server returned empty response. Check server logs for errors.`);
                } else if (!responseText.trim().startsWith('{') && !responseText.trim().startsWith('[')) {
                    throw new Error(`Server response doesn't start with JSON delimiter. First 100 chars: ${responseText.substring(0, 100)}`);
                } else {
                    throw new Error(`Server returned invalid JSON response. JSON Error: ${jsonError.message}. Check browser console for full response.`);
                }
            }

            if (!response.ok) {
                // Enhanced error handling for different HTTP status codes
                const errorMessage = this.getEnhancedErrorMessage(response.status, result);
                throw new Error(errorMessage);
            }

            return result;
        } catch (error) {
            clearTimeout(timeoutId);
            const duration = ((Date.now() - startTime) / 1000).toFixed(1);

            if (error.name === 'AbortError') {
                console.error(`API request timed out after ${duration}s (limit: ${timeoutMinutes}min)`);
                const timeoutMessage = isTranslationRequest
                    ? `Translation request timed out after ${duration} seconds (${timeoutMinutes} minute limit). This may happen with complex content or during high API usage. Please try again - the chunk may be processed faster on retry.`
                    : `Request timed out after ${duration} seconds (${timeoutMinutes} minute limit). The operation may be taking too long. Please try again.`;
                throw new Error(timeoutMessage);
            }

            console.error('API request failed:', error);
            throw error;
        }
    }

    // Make API request with progress tracking for translations
    async makeTranslationRequest(url, data, progressBar = null) {
        if (progressBar) {
            progressBar.show();
            progressBar.setProgress(10, 'Initializing translation...');
        }

        let progressInterval = null;
        const maxProgress = 85; // Don't go above 85% until we get a response

        try {
            // Enhanced progress simulation with different phases
            if (progressBar) {
                progressInterval = setInterval(() => {
                    const currentProgress = parseInt(progressBar.progressBar.getAttribute('aria-valuenow') || 0);

                    if (currentProgress < maxProgress) {
                        let increment;
                        let message;

                        // Enhanced progress phases with chunking awareness
                        if (currentProgress < 30) {
                            increment = Math.random() * 8 + 3; // Faster initial progress
                            message = 'Analyzing content structure...';
                        } else if (currentProgress < 60) {
                            increment = Math.random() * 5 + 2; // Medium progress
                            message = 'Processing content chunks...';
                        } else if (currentProgress < 80) {
                            increment = Math.random() * 3 + 1; // Slower progress
                            message = 'Translating with AI...';
                        } else {
                            increment = Math.random() * 1 + 0.5; // Very slow near the end
                            message = 'Finalizing translation...';
                        }

                        const newProgress = Math.min(currentProgress + increment, maxProgress);
                        progressBar.setProgress(newProgress, message);
                    }
                }, 1000); // Update every second for smoother progress
            }

            // Make the actual API request with translation timeout
            const result = await this.makeApiRequest(url, {
                method: 'PUT',
                body: JSON.stringify(data)
            }, true); // Mark as translation request for extended timeout

            // Clear the progress interval
            if (progressInterval) {
                clearInterval(progressInterval);
                progressInterval = null;
            }

            // Handle the result with enhanced feedback
            if (progressBar) {
                if (result.success) {
                    progressBar.setProgress(100, 'Processing response...');

                    // Show additional info for chunked translations
                    let completionMessage = 'Translation completed successfully!';
                    if (result.data && result.data.chunks_processed) {
                        completionMessage = `Translation completed! Processed ${result.data.chunks_processed} content chunks.`;
                    } else if (result.data && result.data.complexity_analysis) {
                        const complexity = result.data.complexity_analysis;
                        if (complexity.estimated_chunks > 1) {
                            completionMessage = `Translation completed! Content was optimally chunked for best results.`;
                        }
                    }

                    setTimeout(() => {
                        progressBar.complete(completionMessage);
                    }, 500);
                } else {
                    // Enhanced error messages for chunking scenarios
                    let errorMessage = result.error || 'Translation failed';
                    if (errorMessage.includes('too large')) {
                        errorMessage = 'Content is too large. The system will automatically split it into smaller chunks for translation.';
                    } else if (errorMessage.includes('timeout')) {
                        errorMessage = 'Translation timed out. This may happen with very long content. The system will try to process it in smaller chunks.';
                    } else if (errorMessage.includes('chunking')) {
                        errorMessage = 'Content requires intelligent chunking for optimal translation. Please try again.';
                    }
                    progressBar.error(errorMessage);
                }
            }

            return result;
        } catch (error) {
            // Clean up progress interval
            if (progressInterval) {
                clearInterval(progressInterval);
            }

            // Handle different types of errors with enhanced chunking-aware messages
            if (progressBar) {
                let errorMessage = error.message;

                if (error.message.includes('timed out')) {
                    errorMessage = 'Translation timed out. For large chapters, the system will automatically use intelligent chunking to prevent timeouts.';
                } else if (error.message.includes('Rate limit')) {
                    errorMessage = 'API rate limit reached. Please wait a moment and try again.';
                } else if (error.message.includes('too long') || error.message.includes('too large')) {
                    errorMessage = 'Content is too long for single translation. The system will automatically split it into manageable chunks.';
                } else if (error.message.includes('Invalid JSON')) {
                    errorMessage = 'Server response error. This may be due to content complexity. The system will try chunking for better results.';
                }

                progressBar.error(errorMessage);
            }

            throw error;
        }
    }

    // Get enhanced error message based on status code and response
    getEnhancedErrorMessage(status, result) {
        const baseError = result.error || `HTTP error! status: ${status}`;

        switch (status) {
            case 400:
                // Handle specific 400 error cases
                if (baseError.includes('Chapter content not saved')) {
                    return 'Chapter content not saved. Please save the chapter content first before attempting translation.';
                }
                if (baseError.includes('Invalid JSON input')) {
                    return 'Invalid request format. Please try again.';
                }
                if (baseError.includes('Valid novel_id is required')) {
                    return 'Novel ID is required for translation.';
                }
                if (baseError.includes('Valid chapter_number is required')) {
                    return 'Chapter number is required for translation.';
                }
                // Legacy handling for old 400 errors that should be 503
                if (baseError.includes('HTTP error: 503') || baseError.includes('overloaded')) {
                    return 'Translation service is temporarily overloaded. The system is automatically retrying with fallback models. Please wait a moment and try again.';
                }
                if (baseError.includes('Translation failed')) {
                    return 'Translation failed due to API issues. The system has automatic retry logic - please try again in a few moments.';
                }
                return baseError;

            case 408:
                return 'Translation request timed out. This may happen with very long content or during high API usage. Please try again or consider breaking the content into smaller sections.';

            case 413:
                return 'Chapter content is too large for translation. The system will automatically use intelligent chunking to handle large content. Please try again - the chapter will be processed in smaller, more manageable sections.';

            case 429:
                if (baseError.includes('quota exceeded')) {
                    return 'Translation service quota exceeded. The API has reached its usage limit. Please wait and try again later.';
                }
                return 'Rate limit exceeded. Please wait a moment before trying again.';

            case 500:
                return 'Server error occurred. Please try again in a few moments.';

            case 503:
                return 'Translation service is temporarily overloaded. The API is busy processing other requests. Please wait a few minutes and try again.';

            default:
                return baseError;
        }
    }

    // Confirm action with user
    confirmAction(message, callback) {
        if (confirm(message)) {
            callback();
        }
    }

    // Navigate to page
    navigateTo(url) {
        window.location.href = url;
    }

    // Get URL parameter
    getUrlParameter(name) {
        const urlParams = new URLSearchParams(window.location.search);
        return urlParams.get(name);
    }

    // Set URL parameter without page reload
    setUrlParameter(name, value) {
        const url = new URL(window.location);
        url.searchParams.set(name, value);
        window.history.pushState({}, '', url);
    }
}

// Initialize common utilities
const utils = new CommonUtils();

// Export for use in other modules
window.CommonUtils = CommonUtils;
window.utils = utils;
