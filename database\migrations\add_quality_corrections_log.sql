-- Migration: Add Quality Corrections Log Table
-- This table tracks all automatic corrections applied through the quality check system

CREATE TABLE IF NOT EXISTS quality_corrections_log (
    id INT AUTO_INCREMENT PRIMARY KEY,
    novel_id INT NOT NULL,
    chapter_number INT NOT NULL,
    fixes_applied JSON NOT NULL,
    applied_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_novel_chapter (novel_id, chapter_number),
    INDEX idx_applied_at (applied_at),
    
    FOREIGN KEY (novel_id) REFERENCES novels(id) ON DELETE CASCADE
);

-- Add comment to the table
ALTER TABLE quality_corrections_log COMMENT = 'Tracks automatic corrections applied through quality check system';
