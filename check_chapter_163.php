<?php
require_once 'config/config.php';

echo "=== CHECKING CHAPTER 163 FOR NOVEL ID 1 ===\n";
echo "Date: " . date('Y-m-d H:i:s') . "\n\n";

try {
    $db = Database::getInstance();
    
    $chapter = $db->fetchOne('SELECT * FROM chapters WHERE novel_id = 1 AND chapter_number = 163');
    
    if ($chapter) {
        echo "Chapter found: " . $chapter['original_title'] . "\n";
        echo "Translation status: " . $chapter['translation_status'] . "\n";
        echo "Has translated content: " . (!empty($chapter['translated_content']) ? 'Yes' : 'No') . "\n";
        
        if (!empty($chapter['translated_content'])) {
            echo "\n=== TRANSLATED CONTENT SAMPLE (first 1000 chars) ===\n";
            echo substr($chapter['translated_content'], 0, 1000) . "\n";
            
            echo "\n=== CHECKING FOR SUFFIX ISSUES ===\n";
            preg_match_all('/-san\d+|-kun\d+|-chan\d+|-sama\d+/', $chapter['translated_content'], $matches);
            if (!empty($matches[0])) {
                echo "Found suffix issues: " . implode(', ', array_unique($matches[0])) . "\n";
            } else {
                echo "No numbered suffix issues found\n";
            }
            
            echo "\n=== CHECKING FOR PUNCTUATION ISSUES ===\n";
            preg_match_all('/」[^「]*「/', $chapter['translated_content'], $dialogueMatches);
            if (!empty($dialogueMatches[0])) {
                echo "Found reversed dialogue punctuation: " . count($dialogueMatches[0]) . " instances\n";
                echo "Examples: " . implode(' | ', array_slice($dialogueMatches[0], 0, 3)) . "\n";
            } else {
                echo "No reversed dialogue punctuation found\n";
            }
        }
    } else {
        echo "Chapter 163 not found for Novel ID 1\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
