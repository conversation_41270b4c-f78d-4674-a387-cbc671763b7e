<?php
/**
 * Test script to verify the 君 (kun) pronoun vs honorific fix
 */

require_once 'config/config.php';

echo "=== TESTING 君 (KUN) PRONOUN VS HONORIFIC FIX ===\n";
echo "Date: " . date('Y-m-d H:i:s') . "\n\n";

try {
    $honorificService = new HonorificService();
    
    // Test cases
    $testCases = [
        // Standalone 君 (pronoun) - should NOT be processed as honorific
        [
            'text' => 'これから君は、彼らに犯される。',
            'description' => 'Standalone 君 as pronoun (should NOT be processed)',
            'expected_behavior' => 'No honorific processing'
        ],
        [
            'text' => '君は材料なんだ',
            'description' => 'Standalone 君 as pronoun (should NOT be processed)',
            'expected_behavior' => 'No honorific processing'
        ],
        [
            'text' => 'ゲームオーバーだよ、君',
            'description' => 'Standalone 君 as pronoun (should NOT be processed)',
            'expected_behavior' => 'No honorific processing'
        ],
        
        // Name+君 (honorific) - should be processed as honorific
        [
            'text' => '田中君はとても優秀です。',
            'description' => 'Name+君 as honorific (should be processed)',
            'expected_behavior' => 'Should be processed as honorific'
        ],
        [
            'text' => '山田君、おはよう！',
            'description' => 'Name+君 as honorific (should be processed)',
            'expected_behavior' => 'Should be processed as honorific'
        ],
        [
            'text' => 'エルマ君は魔法が得意だ。',
            'description' => 'Katakana name+君 as honorific (should be processed)',
            'expected_behavior' => 'Should be processed as honorific'
        ]
    ];
    
    echo "1. TESTING HONORIFIC PREPROCESSING:\n";
    echo "=" . str_repeat("=", 50) . "\n";
    
    foreach ($testCases as $i => $testCase) {
        echo "\nTest " . ($i + 1) . ": {$testCase['description']}\n";
        echo "Input: {$testCase['text']}\n";
        echo "Expected: {$testCase['expected_behavior']}\n";
        
        // Test the preprocessing
        $result = $honorificService->preprocessTextForTranslation($testCase['text'], 'japanese');
        
        echo "Processed text: {$result['text']}\n";
        echo "Honorifics found: " . count($result['honorifics']) . "\n";
        
        if (!empty($result['honorifics'])) {
            foreach ($result['honorifics'] as $marker => $honorific) {
                echo "  - {$marker}: {$honorific['original']} → {$honorific['romanized']}\n";
            }
        }
        
        // Verify the result
        $hasHonorifics = !empty($result['honorifics']);
        $shouldHaveHonorifics = strpos($testCase['expected_behavior'], 'should be processed') !== false;
        
        if ($hasHonorifics === $shouldHaveHonorifics) {
            echo "✓ PASS: Behavior matches expectation\n";
        } else {
            echo "✗ FAIL: Expected " . ($shouldHaveHonorifics ? 'honorifics' : 'no honorifics') . 
                 " but got " . ($hasHonorifics ? 'honorifics' : 'no honorifics') . "\n";
        }
    }
    
    echo "\n\n2. TESTING WITH CHAPTER 24 CONTENT:\n";
    echo "=" . str_repeat("=", 50) . "\n";
    
    $db = Database::getInstance();
    $chapter = $db->fetchOne(
        "SELECT original_content FROM chapters WHERE novel_id = 3 AND chapter_number = 24",
        []
    );
    
    if ($chapter && !empty($chapter['original_content'])) {
        echo "Testing with actual chapter 24 content...\n";
        
        // Extract the problematic 君 instances
        $originalContent = $chapter['original_content'];
        $kunPositions = [];
        $offset = 0;
        while (($pos = mb_strpos($originalContent, '君', $offset)) !== false) {
            $kunPositions[] = $pos;
            $offset = $pos + 1;
        }
        
        echo "Found " . count($kunPositions) . " instances of '君' in chapter 24\n";
        
        foreach ($kunPositions as $i => $pos) {
            $start = max(0, $pos - 10);
            $length = 20;
            $context = mb_substr($originalContent, $start, $length);
            $context = str_replace('君', '**君**', $context);
            echo "  " . ($i + 1) . ". ...{$context}...\n";
            
            // Test each context
            $testText = mb_substr($originalContent, max(0, $pos - 5), 15);
            $result = $honorificService->preprocessTextForTranslation($testText, 'japanese');
            
            if (!empty($result['honorifics'])) {
                echo "     → Would be processed as honorific (WRONG for standalone 君)\n";
            } else {
                echo "     → Not processed as honorific (CORRECT for standalone 君)\n";
            }
        }
    } else {
        echo "Could not load chapter 24 content for testing.\n";
    }
    
} catch (Exception $e) {
    echo "ERROR: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

echo "\n=== TEST COMPLETE ===\n";
?>
