<?php
/**
 * Test script to verify name dictionary usage in translations
 */

require_once 'config/config.php';

echo "=== NAME DICTIONARY USAGE TEST - " . date('Y-m-d H:i:s') . " ===\n";

try {
    $translationService = new TranslationService();
    $db = Database::getInstance();
    
    // Test with a specific novel that has name dictionary entries
    $novelId = 3; // Using novel ID 3 from the debug log
    
    // Get name dictionary for this novel
    $nameDictionary = $translationService->getNameDictionary($novelId);
    echo "Found " . count($nameDictionary) . " names in dictionary for novel {$novelId}\n";
    
    if (empty($nameDictionary)) {
        echo "ERROR: No names found in dictionary. Cannot test name usage.\n";
        exit(1);
    }
    
    // Display first 10 names for reference
    echo "\nFirst 10 names in dictionary:\n";
    for ($i = 0; $i < min(10, count($nameDictionary)); $i++) {
        $name = $nameDictionary[$i];
        $targetName = $name['translation'] ?? $name['romanization'] ?? $name['original_name'];
        $source = $name['translation'] ? 'translation' : ($name['romanization'] ? 'romanization' : 'original');
        echo "  - {$name['original_name']} → {$targetName} (using {$source})\n";
    }
    
    // Create a test text that contains some of these names
    $testNames = array_slice($nameDictionary, 0, 3); // Use first 3 names
    $testText = "今日は";
    foreach ($testNames as $name) {
        $testText .= $name['original_name'] . "と";
    }
    $testText .= "一緒に行きました。";

    echo "\nTest text containing names: {$testText}\n";

    // Also test with simpler character names
    $simpleNames = [];
    foreach ($nameDictionary as $name) {
        if (mb_strlen($name['original_name']) <= 4 && !preg_match('/[の"『』【】]/', $name['original_name'])) {
            $simpleNames[] = $name;
            if (count($simpleNames) >= 3) break;
        }
    }

    if (!empty($simpleNames)) {
        $simpleTestText = "昨日は";
        foreach ($simpleNames as $name) {
            $simpleTestText .= $name['original_name'] . "と";
        }
        $simpleTestText .= "話しました。";
        echo "Simple test text: {$simpleTestText}\n";

        echo "\nSimple names being tested:\n";
        foreach ($simpleNames as $name) {
            $targetName = $name['translation'] ?? $name['romanization'] ?? $name['original_name'];
            $source = $name['translation'] ? 'translation' : ($name['romanization'] ? 'romanization' : 'original');
            echo "  - {$name['original_name']} → {$targetName} (using {$source})\n";
        }
    }
    
    // Test translation with name dictionary context
    echo "\n=== Testing Translation with Name Dictionary ===\n";
    
    $context = [
        'type' => 'chapter',
        'names' => $nameDictionary
    ];
    
    $result = $translationService->translateText($testText, 'en', 'auto', $context);
    
    if ($result['success']) {
        echo "Translation successful!\n";
        echo "Original: {$testText}\n";
        echo "Translated: {$result['translated_text']}\n";
        echo "API used: " . ($result['api_used'] ?? 'unknown') . "\n";
        
        // Check if the expected names appear in the translation
        echo "\n=== Checking Name Usage ===\n";
        $translatedText = $result['translated_text'];
        $namesFound = 0;
        $namesExpected = 0;
        
        foreach ($testNames as $name) {
            $originalName = $name['original_name'];
            $expectedName = $name['translation'] ?? $name['romanization'] ?? $name['original_name'];
            $namesExpected++;
            
            // Check if the expected name appears in the translation
            if (stripos($translatedText, $expectedName) !== false) {
                echo "✓ Found expected name: {$expectedName} (from {$originalName})\n";
                $namesFound++;
            } else {
                echo "✗ Missing expected name: {$expectedName} (from {$originalName})\n";
                
                // Check if the original name appears (indicating it wasn't translated)
                if (stripos($translatedText, $originalName) !== false) {
                    echo "  → Original name {$originalName} still present in translation\n";
                }
            }
        }
        
        echo "\nSummary: {$namesFound}/{$namesExpected} expected names found in translation\n";
        
        if ($namesFound == 0) {
            echo "\n❌ ISSUE DETECTED: No names from dictionary were used in translation!\n";
            echo "This indicates the name dictionary is not being properly applied.\n";
        } elseif ($namesFound < $namesExpected) {
            echo "\n⚠️  PARTIAL ISSUE: Some names from dictionary were not used.\n";
        } else {
            echo "\n✅ SUCCESS: All expected names were found in translation.\n";
        }
        
    } else {
        echo "Translation failed: {$result['error']}\n";
    }
    
    // Test simple names if available
    if (!empty($simpleNames)) {
        echo "\n=== Testing Simple Names Translation ===\n";

        $simpleResult = $translationService->translateText($simpleTestText, 'en', 'auto', $context);

        if ($simpleResult['success']) {
            echo "Simple translation successful!\n";
            echo "Original: {$simpleTestText}\n";
            echo "Translated: {$simpleResult['translated_text']}\n";
            echo "API used: " . ($simpleResult['api_used'] ?? 'unknown') . "\n";

            // Check simple name usage
            echo "\n=== Checking Simple Name Usage ===\n";
            $simpleTranslatedText = $simpleResult['translated_text'];
            $simpleNamesFound = 0;
            $simpleNamesExpected = count($simpleNames);

            foreach ($simpleNames as $name) {
                $originalName = $name['original_name'];
                $expectedName = $name['translation'] ?? $name['romanization'] ?? $name['original_name'];

                if (stripos($simpleTranslatedText, $expectedName) !== false) {
                    echo "✓ Found expected simple name: {$expectedName} (from {$originalName})\n";
                    $simpleNamesFound++;
                } else {
                    echo "✗ Missing expected simple name: {$expectedName} (from {$originalName})\n";
                    if (stripos($simpleTranslatedText, $originalName) !== false) {
                        echo "  → Original name {$originalName} still present in translation\n";
                    }
                }
            }

            echo "\nSimple names summary: {$simpleNamesFound}/{$simpleNamesExpected} expected names found\n";
        }
    }

    // Test without name dictionary for comparison
    echo "\n=== Testing Translation WITHOUT Name Dictionary (for comparison) ===\n";

    $contextWithoutNames = [
        'type' => 'chapter'
        // No 'names' key
    ];

    $resultWithoutNames = $translationService->translateText($testText, 'en', 'auto', $contextWithoutNames);
    
    if ($resultWithoutNames['success']) {
        echo "Translation without names successful!\n";
        echo "Original: {$testText}\n";
        echo "Translated: {$resultWithoutNames['translated_text']}\n";
        echo "API used: " . ($resultWithoutNames['api_used'] ?? 'unknown') . "\n";
        
        // Compare the two translations
        echo "\n=== Comparison ===\n";
        echo "With names:    {$result['translated_text']}\n";
        echo "Without names: {$resultWithoutNames['translated_text']}\n";
        
        if ($result['translated_text'] === $resultWithoutNames['translated_text']) {
            echo "\n❌ CRITICAL ISSUE: Translations are identical!\n";
            echo "This strongly indicates that the name dictionary is not being used at all.\n";
        } else {
            echo "\n✅ Translations are different, which is expected.\n";
        }
    } else {
        echo "Translation without names failed: {$resultWithoutNames['error']}\n";
    }
    
} catch (Exception $e) {
    echo "Test failed with exception: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}

    // Test the new name substitution service directly
    echo "\n=== Testing Name Substitution Service Directly ===\n";

    $nameSubstitutionService = new NameSubstitutionService();

    // Test with a problematic translation that should be fixed
    $problematicTranslation = "Today I went with the Insects of the Death King, Elma, and Tabo.";
    $originalTestText = "今日は死王の蟲とエルマとタボと一緒に行きました。";

    echo "Testing name substitution on: {$problematicTranslation}\n";

    $substitutionResult = $nameSubstitutionService->applyNameSubstitutions(
        $problematicTranslation,
        $nameDictionary,
        $originalTestText
    );

    if ($substitutionResult['success']) {
        echo "Name substitution successful!\n";
        echo "Original translation: {$substitutionResult['original_text']}\n";
        echo "After substitution:   {$substitutionResult['processed_text']}\n";
        echo "Substitutions made:   {$substitutionResult['substitution_count']}\n";

        if ($substitutionResult['substitution_count'] > 0) {
            echo "\nSubstitution details:\n";
            foreach ($substitutionResult['substitutions'] as $sub) {
                echo "  - {$sub['original_name']} → {$sub['target_name']}\n";
                echo "    Found variations: " . implode(', ', $sub['found_variations']) . "\n";
                echo "    Replacements: {$sub['replacement_count']}\n";
            }
        }

        // Check if the exact dictionary name is now present
        $exactMatch = false;
        foreach ($testNames as $name) {
            $expectedName = $name['translation'] ?? $name['romanization'] ?? $name['original_name'];
            if (strpos($substitutionResult['processed_text'], $expectedName) !== false) {
                echo "✓ Found exact dictionary match: {$expectedName}\n";
                $exactMatch = true;
            }
        }

        if (!$exactMatch) {
            echo "⚠️  No exact dictionary matches found after substitution\n";
        }

    } else {
        echo "Name substitution failed: {$substitutionResult['error']}\n";
    }

    // Test fuzzy matching capabilities
    echo "\n=== Testing Fuzzy Matching ===\n";

    $fuzzyMatches = $nameSubstitutionService->findFuzzyMatches($problematicTranslation, $nameDictionary);

    if (!empty($fuzzyMatches)) {
        echo "Found " . count($fuzzyMatches) . " fuzzy matches:\n";
        foreach (array_slice($fuzzyMatches, 0, 5) as $match) { // Show top 5
            echo "  - '{$match['found_text']}' → '{$match['target_name']}' ";
            echo "(similarity: " . round($match['similarity'], 2) . ", ";
            echo "confidence: " . round($match['confidence'], 2) . ")\n";
        }
    } else {
        echo "No fuzzy matches found\n";
    }

    // Test edge cases
    echo "\n=== Testing Edge Cases ===\n";

    // Test 1: Names within names
    $edgeCase1 = "The King of Death and the Death King's insects are different.";
    echo "Edge case 1 (names within names): {$edgeCase1}\n";

    $edgeResult1 = $nameSubstitutionService->applyNameSubstitutions(
        $edgeCase1,
        $nameDictionary,
        "死王と死王の蟲は違います。"
    );

    if ($edgeResult1['success']) {
        echo "After substitution: {$edgeResult1['processed_text']}\n";
        echo "Substitutions: {$edgeResult1['substitution_count']}\n";
    }

    // Test 2: Multiple occurrences of the same name
    $edgeCase2 = "Elma met Elma's friend, and then Elma left.";
    echo "\nEdge case 2 (multiple occurrences): {$edgeCase2}\n";

    $edgeResult2 = $nameSubstitutionService->applyNameSubstitutions(
        $edgeCase2,
        $nameDictionary,
        "エルマはエルマの友達に会って、エルマは去りました。"
    );

    if ($edgeResult2['success']) {
        echo "After substitution: {$edgeResult2['processed_text']}\n";
        echo "Substitutions: {$edgeResult2['substitution_count']}\n";
    }

    // Test 3: Names with different capitalization
    $edgeCase3 = "The INSECTS OF THE DEATH KING and insects of the death king are the same.";
    echo "\nEdge case 3 (capitalization): {$edgeCase3}\n";

    $edgeResult3 = $nameSubstitutionService->applyNameSubstitutions(
        $edgeCase3,
        $nameDictionary,
        "死王の蟲と死王の蟲は同じです。"
    );

    if ($edgeResult3['success']) {
        echo "After substitution: {$edgeResult3['processed_text']}\n";
        echo "Substitutions: {$edgeResult3['substitution_count']}\n";
    }

    // Test 4: Names in quotes
    $edgeCase4 = 'He said "The Insects of the Death King are coming" loudly.';
    echo "\nEdge case 4 (quoted names): {$edgeCase4}\n";

    $edgeResult4 = $nameSubstitutionService->applyNameSubstitutions(
        $edgeCase4,
        $nameDictionary,
        "彼は「死王の蟲が来る」と大声で言いました。"
    );

    if ($edgeResult4['success']) {
        echo "After substitution: {$edgeResult4['processed_text']}\n";
        echo "Substitutions: {$edgeResult4['substitution_count']}\n";
    }

    // Test 5: Complex sentence with multiple names
    $edgeCase5 = "Yesterday, Elma and Tabo visited the World Tree Forest where they met the Insects of the Death King.";
    echo "\nEdge case 5 (multiple names): {$edgeCase5}\n";

    $edgeResult5 = $nameSubstitutionService->applyNameSubstitutions(
        $edgeCase5,
        $nameDictionary,
        "昨日、エルマとタボは世界樹林を訪れ、そこで死王の蟲に会いました。"
    );

    if ($edgeResult5['success']) {
        echo "After substitution: {$edgeResult5['processed_text']}\n";
        echo "Substitutions: {$edgeResult5['substitution_count']}\n";

        if ($edgeResult5['substitution_count'] > 0) {
            echo "Substitution details:\n";
            foreach ($edgeResult5['substitutions'] as $sub) {
                echo "  - {$sub['original_name']} → {$sub['target_name']}\n";
            }
        }
    }

    echo "\n=== Edge Case Testing Complete ===\n";

echo "\n=== Test completed ===\n";
?>
