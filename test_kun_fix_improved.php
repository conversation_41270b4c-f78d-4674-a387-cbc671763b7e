<?php
/**
 * Improved test script to verify the 君 (kun) pronoun vs honorific fix
 */

require_once 'config/config.php';

echo "=== IMPROVED TESTING 君 (KUN) PRONOUN VS HONORIFIC FIX ===\n";
echo "Date: " . date('Y-m-d H:i:s') . "\n\n";

try {
    $honorificService = new HonorificService();
    
    // Test cases with more realistic scenarios
    $testCases = [
        // Standalone 君 (pronoun) - should NOT be processed
        [
            'text' => 'これから君は、彼らに犯される。',
            'description' => 'Standalone 君 as pronoun (should NOT be processed)',
            'expected_honorifics' => 0
        ],
        [
            'text' => '君は材料なんだ',
            'description' => 'Standalone 君 as pronoun (should NOT be processed)',
            'expected_honorifics' => 0
        ],
        [
            'text' => 'ゲームオーバーだよ、君',
            'description' => 'Standalone 君 as pronoun (should NOT be processed)',
            'expected_honorifics' => 0
        ],
        
        // Name+君 (honorific) with proper word boundaries - should be processed
        [
            'text' => '田中君、おはよう！',
            'description' => 'Name+君 with punctuation (should be processed)',
            'expected_honorifics' => 1
        ],
        [
            'text' => '山田君。',
            'description' => 'Name+君 at sentence end (should be processed)',
            'expected_honorifics' => 1
        ],
        [
            'text' => 'エルマ君！',
            'description' => 'Katakana name+君 with exclamation (should be processed)',
            'expected_honorifics' => 1
        ],
        [
            'text' => '「田中君」と呼んだ。',
            'description' => 'Name+君 in quotes (should be processed)',
            'expected_honorifics' => 1
        ],
        
        // Edge cases
        [
            'text' => '君主',
            'description' => 'Compound word with 君 (should NOT be processed)',
            'expected_honorifics' => 0
        ],
        [
            'text' => '君臨',
            'description' => 'Another compound word with 君 (should NOT be processed)',
            'expected_honorifics' => 0
        ]
    ];
    
    echo "TESTING HONORIFIC PREPROCESSING:\n";
    echo "=" . str_repeat("=", 50) . "\n";
    
    $passCount = 0;
    $totalCount = count($testCases);
    
    foreach ($testCases as $i => $testCase) {
        echo "\nTest " . ($i + 1) . ": {$testCase['description']}\n";
        echo "Input: {$testCase['text']}\n";
        echo "Expected honorifics: {$testCase['expected_honorifics']}\n";
        
        // Test the preprocessing
        $result = $honorificService->preprocessTextForTranslation($testCase['text'], 'japanese');
        
        echo "Processed text: {$result['text']}\n";
        echo "Honorifics found: " . count($result['honorifics']) . "\n";
        
        if (!empty($result['honorifics'])) {
            foreach ($result['honorifics'] as $marker => $honorific) {
                echo "  - {$marker}: {$honorific['original']} → {$honorific['romanized']}\n";
            }
        }
        
        // Verify the result
        $actualHonorifics = count($result['honorifics']);
        $expectedHonorifics = $testCase['expected_honorifics'];
        
        if ($actualHonorifics === $expectedHonorifics) {
            echo "✓ PASS\n";
            $passCount++;
        } else {
            echo "✗ FAIL: Expected {$expectedHonorifics} honorifics but got {$actualHonorifics}\n";
        }
    }
    
    echo "\n" . str_repeat("=", 50) . "\n";
    echo "SUMMARY: {$passCount}/{$totalCount} tests passed\n";
    
    if ($passCount === $totalCount) {
        echo "🎉 ALL TESTS PASSED! The fix is working correctly.\n";
    } else {
        echo "❌ Some tests failed. The fix needs more work.\n";
    }
    
    // Test with actual chapter 24 content
    echo "\nTESTING WITH CHAPTER 24 CONTENT:\n";
    echo "=" . str_repeat("=", 50) . "\n";
    
    $db = Database::getInstance();
    $chapter = $db->fetchOne(
        "SELECT original_content FROM chapters WHERE novel_id = 3 AND chapter_number = 24",
        []
    );
    
    if ($chapter && !empty($chapter['original_content'])) {
        $originalContent = $chapter['original_content'];
        
        // Test the full chapter content
        $result = $honorificService->preprocessTextForTranslation($originalContent, 'japanese');
        
        echo "Chapter 24 processing results:\n";
        echo "- Original length: " . mb_strlen($originalContent) . " characters\n";
        echo "- Processed length: " . mb_strlen($result['text']) . " characters\n";
        echo "- Honorifics found: " . count($result['honorifics']) . "\n";
        
        if (!empty($result['honorifics'])) {
            echo "Honorifics detected:\n";
            foreach ($result['honorifics'] as $marker => $honorific) {
                echo "  - {$honorific['original']} → {$honorific['romanized']}\n";
            }
        }
        
        // Count standalone 君 in original vs processed
        $originalKunCount = mb_substr_count($originalContent, '君');
        $processedKunCount = mb_substr_count($result['text'], '君');
        
        echo "- Original 君 count: {$originalKunCount}\n";
        echo "- Processed 君 count: {$processedKunCount}\n";
        
        if ($originalKunCount === $processedKunCount) {
            echo "✓ All standalone 君 correctly preserved (not processed as honorifics)\n";
        } else {
            echo "✗ Some 君 were incorrectly processed as honorifics\n";
        }
    }
    
} catch (Exception $e) {
    echo "ERROR: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

echo "\n=== TEST COMPLETE ===\n";
?>
