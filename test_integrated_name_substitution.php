<?php
/**
 * Test script to verify integrated name substitution in the full translation pipeline
 */

require_once 'config/config.php';

echo "=== INTEGRATED NAME SUBSTITUTION TEST - " . date('Y-m-d H:i:s') . " ===\n";

try {
    $translationService = new TranslationService();
    
    // Test with novel ID 3 which has a good name dictionary
    $novelId = 3;
    
    // Get name dictionary
    $nameDictionary = $translationService->getNameDictionary($novelId);
    echo "Found " . count($nameDictionary) . " names in dictionary for novel {$novelId}\n";
    
    if (empty($nameDictionary)) {
        echo "ERROR: No names found in dictionary. Cannot test integration.\n";
        exit(1);
    }
    
    // Test cases that should trigger name substitution
    $testCases = [
        [
            'japanese' => '死王の蟲とエルマが世界樹林で戦った。',
            'description' => 'Complex name with particle + character names + location'
        ],
        [
            'japanese' => 'エルマは「死王の蟲が来る！」と叫んだ。',
            'description' => 'Character name + quoted complex name'
        ],
        [
            'japanese' => '世界樹の下で、タボとパパが話していた。',
            'description' => 'Location + character names'
        ],
        [
            'japanese' => '炎王"メルマス"は厄王と戦った。',
            'description' => 'Names with quotes and special characters'
        ]
    ];
    
    foreach ($testCases as $i => $testCase) {
        echo "\n=== Test Case " . ($i + 1) . ": {$testCase['description']} ===\n";
        echo "Japanese: {$testCase['japanese']}\n";
        
        // Translate with name dictionary context
        $context = [
            'type' => 'chapter',
            'names' => $nameDictionary
        ];
        
        $result = $translationService->translateText($testCase['japanese'], 'en', 'auto', $context);
        
        if ($result['success']) {
            echo "Translation: {$result['translated_text']}\n";
            echo "API used: " . ($result['api_used'] ?? 'unknown') . "\n";
            
            // Check if dictionary names are present
            $foundNames = [];
            foreach ($nameDictionary as $nameEntry) {
                $targetName = $nameEntry['translation'] ?? $nameEntry['romanization'] ?? $nameEntry['original_name'];
                if (!empty($targetName) && stripos($result['translated_text'], $targetName) !== false) {
                    $foundNames[] = $nameEntry['original_name'] . ' → ' . $targetName;
                }
            }
            
            if (!empty($foundNames)) {
                echo "Dictionary names found:\n";
                foreach ($foundNames as $foundName) {
                    echo "  ✓ {$foundName}\n";
                }
            } else {
                echo "⚠️  No dictionary names found in translation\n";
            }
            
        } else {
            echo "Translation failed: {$result['error']}\n";
        }
    }
    
    // Test comparison: with vs without name dictionary
    echo "\n=== Comparison Test: With vs Without Name Dictionary ===\n";
    
    $comparisonText = '死王の蟲とエルマとタボが世界樹林で出会った。';
    echo "Test text: {$comparisonText}\n";
    
    // With name dictionary
    $contextWithNames = ['type' => 'chapter', 'names' => $nameDictionary];
    $resultWithNames = $translationService->translateText($comparisonText, 'en', 'auto', $contextWithNames);
    
    // Without name dictionary
    $contextWithoutNames = ['type' => 'chapter'];
    $resultWithoutNames = $translationService->translateText($comparisonText, 'en', 'auto', $contextWithoutNames);
    
    if ($resultWithNames['success'] && $resultWithoutNames['success']) {
        echo "\nWith names:    {$resultWithNames['translated_text']}\n";
        echo "Without names: {$resultWithoutNames['translated_text']}\n";
        
        if ($resultWithNames['translated_text'] !== $resultWithoutNames['translated_text']) {
            echo "✅ SUCCESS: Translations are different, indicating name dictionary is being used.\n";
            
            // Count dictionary names in each translation
            $namesInWith = 0;
            $namesInWithout = 0;
            
            foreach ($nameDictionary as $nameEntry) {
                $targetName = $nameEntry['translation'] ?? $nameEntry['romanization'] ?? $nameEntry['original_name'];
                if (!empty($targetName)) {
                    if (stripos($resultWithNames['translated_text'], $targetName) !== false) {
                        $namesInWith++;
                    }
                    if (stripos($resultWithoutNames['translated_text'], $targetName) !== false) {
                        $namesInWithout++;
                    }
                }
            }
            
            echo "Dictionary names found - With: {$namesInWith}, Without: {$namesInWithout}\n";
            
            if ($namesInWith > $namesInWithout) {
                echo "✅ EXCELLENT: More dictionary names found in 'with names' translation.\n";
            } else {
                echo "⚠️  Note: Similar number of dictionary names in both translations.\n";
            }
            
        } else {
            echo "❌ ISSUE: Translations are identical, name dictionary may not be working.\n";
        }
    } else {
        echo "Translation comparison failed.\n";
    }
    
    echo "\n=== Integration Test Complete ===\n";
    
} catch (Exception $e) {
    echo "Test failed with exception: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}

echo "\n=== Test completed ===\n";
?>
