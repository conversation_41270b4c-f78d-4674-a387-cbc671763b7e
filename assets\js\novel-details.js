/**
 * Novel Details JavaScript
 * Handles novel details page functionality
 */

class NovelDetails {
    constructor(novelId) {
        this.novelId = novelId;
        this.novel = null;
        this.chapters = [];
        this.selectedChapters = new Set();
        this.bulkMode = false;

        // Search and pagination state
        this.searchTerm = '';
        this.currentPage = 1;
        this.chaptersPerPage = parseInt(sessionStorage.getItem('chaptersPerPage')) || 10;
        this.pagination = {};

        // Chapter range filter state
        this.chapterRangeFilter = {
            enabled: false,
            fromChapter: null,
            toChapter: null
        };

        // POV preference for chunk translation session
        this.currentChunkPOVPreference = null;

        // Manual chapter creation state
        this.manualChapterMode = false;

        // Ensure toast notifications are working
        this.initializeNotifications();

        this.loadNovelDetails();
    }

    initializeNotifications() {
        // Check if toast element exists and utils is available
        setTimeout(() => {
            const toastElement = document.getElementById('toast');
            if (!toastElement) {
                console.warn('[NovelDetails] Toast element not found - creating fallback');
                this.createFallbackToast();
            }

            // Test toast functionality
            if (utils && typeof utils.showToast === 'function') {
                console.log('[NovelDetails] Toast system initialized successfully');
                // Show a test notification to verify it's working
                utils.showToast('Chunk translation interface ready', 'info');
            } else {
                console.error('[NovelDetails] Utils or showToast not available');
            }
        }, 500); // Wait for DOM to be ready
    }

    createFallbackToast() {
        // Create a simple fallback toast if the main one is missing
        const toastHtml = `
            <div class="toast-container position-fixed bottom-0 end-0 p-3">
                <div id="toast" class="toast" role="alert">
                    <div class="toast-header">
                        <strong class="me-auto">Notification</strong>
                        <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
                    </div>
                    <div class="toast-body"></div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', toastHtml);
        console.log('[NovelDetails] Created fallback toast element');

        // Reinitialize utils toast
        if (utils) {
            utils.initializeToasts();
        }
    }

    async loadNovelDetails() {
        utils.showLoading(true);

        try {
            // Build query parameters
            const params = new URLSearchParams({
                novel_id: this.novelId,
                page: this.currentPage,
                limit: this.chaptersPerPage === 'all' ? 999999 : this.chaptersPerPage
            });

            if (this.searchTerm) {
                params.append('search', this.searchTerm);
            }

            if (this.chapterRangeFilter.enabled) {
                if (this.chapterRangeFilter.fromChapter) {
                    params.append('from_chapter', this.chapterRangeFilter.fromChapter);
                }
                if (this.chapterRangeFilter.toChapter) {
                    params.append('to_chapter', this.chapterRangeFilter.toChapter);
                }
            }

            const result = await utils.makeApiRequest(`api/chapters.php?${params.toString()}`);

            if (result.success) {
                this.novel = result.data.novel;
                this.chapters = result.data.chapters;
                this.pagination = result.data.pagination || {};

                // Load WordPress status for the novel
                await this.loadWordPressStatus();

                this.displayNovelDetails();
            } else {
                this.displayError('Failed to load novel details');
            }
        } catch (error) {
            console.error('Load details error:', error);
            this.displayError('Network error occurred');
        } finally {
            utils.showLoading(false);
        }
    }

    async loadWordPressStatus() {
        try {
            const response = await fetch(`api/wordpress.php?novel_id=${this.novelId}`);
            const result = await response.json();

            if (result.success) {
                this.wordpressStatus = result.status;
            } else {
                this.wordpressStatus = { novel_posted: false, chapters_posted: 0, all_domains: [] };
            }
        } catch (error) {
            console.warn('Failed to load WordPress status:', error);
            this.wordpressStatus = { novel_posted: false, chapters_posted: 0, all_domains: [] };
        }
    }

    displayNovelDetails() {
        const detailsHtml = `
            <div class="row">
                <div class="col-12">
                    <!-- POV Management Section -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-eye me-2"></i>
                                Narrative Perspective (POV) Settings
                            </h5>
                        </div>
                        <div class="card-body">
                            <div id="pov-management-content">
                                <div class="text-center">
                                    <div class="spinner-border spinner-border-sm text-primary" role="status">
                                        <span class="visually-hidden">Loading POV settings...</span>
                                    </div>
                                    <p class="mt-2 text-muted">Loading POV settings...</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Novel Information -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <div class="d-flex justify-content-between align-items-start">
                                <div class="flex-grow-1">
                                    <h4 class="mb-0 d-flex align-items-center">
                                        <i class="fas fa-book me-2"></i>
                                        <span id="novel-title-display" class="editable-title" onclick="novelDetails.editTitle()">
                                            ${utils.escapeHtml(this.novel.translated_title || this.novel.original_title)}
                                        </span>
                                        <button class="btn btn-sm btn-outline-secondary ms-2" onclick="novelDetails.editTitle()" title="Edit title">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                    </h4>
                                    ${this.novel.translated_title && this.novel.original_title !== this.novel.translated_title ? `
                                        <div class="mt-1">
                                            <small class="text-muted">
                                                <strong>Original:</strong> ${utils.escapeHtml(this.novel.original_title)}
                                            </small>
                                        </div>
                                    ` : ''}
                                    <div id="novel-title-edit" class="mt-2" style="display: none;">
                                        <div class="input-group">
                                            <input type="text" id="novel-title-input" class="form-control"
                                                   value="${utils.escapeHtml(this.novel.translated_title || this.novel.original_title)}"
                                                   placeholder="Enter novel title">
                                            <button class="btn btn-success" onclick="novelDetails.saveTitle()">
                                                <i class="fas fa-check"></i> Save
                                            </button>
                                            <button class="btn btn-secondary" onclick="novelDetails.cancelEditTitle()">
                                                <i class="fas fa-times"></i> Cancel
                                            </button>
                                        </div>
                                        <small class="text-muted">Original: ${utils.escapeHtml(this.novel.original_title)}</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="novel-meta mb-3">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="novel-meta-item">
                                            <i class="fas fa-user"></i>
                                            <span><strong>Author:</strong> ${utils.escapeHtml(this.novel.author || 'Unknown')}</span>
                                        </div>
                                        <div class="novel-meta-item">
                                            <i class="fas fa-globe"></i>
                                            <span><strong>Platform:</strong> 
                                                <span class="platform-badge platform-${this.novel.platform}">
                                                    ${utils.getPlatformName(this.novel.platform)}
                                                </span>
                                            </span>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="novel-meta-item">
                                            <i class="fas fa-calendar"></i>
                                            <span><strong>Published:</strong> ${utils.formatDate(this.novel.publication_date)}</span>
                                        </div>
                                        <div class="novel-meta-item">
                                            <i class="fas fa-list"></i>
                                            <span><strong>Chapters:</strong> ${this.chapters.length}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            ${this.renderSynopsis()}

                            <div class="novel-progress">
                                <h6><i class="fas fa-chart-line me-2"></i>Translation Progress</h6>
                                ${this.renderProgressBar()}
                            </div>

                            ${this.renderWordPressStatus()}
                        </div>
                    </div>

                    <!-- Chapter List -->
                    <div class="card">
                        <div class="card-header">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <h5 class="mb-0">
                                    <i class="fas fa-list me-2"></i>
                                    Chapters ${this.pagination.total_chapters ? `(${this.pagination.total_chapters})` : `(${this.chapters.length})`}
                                </h5>
                                <div class="d-flex gap-2">
                                    ${['kakuyomu', 'syosetu', 'shuba69'].includes(this.novel.platform) ? `
                                        <button class="btn btn-outline-info btn-sm" onclick="novelDetails.checkForNewChapters()">
                                            <i class="fas fa-search me-1"></i>
                                            Check for New Chapters
                                        </button>
                                    ` : ''}
                                    <button class="btn btn-outline-warning btn-sm" onclick="novelDetails.toggleManualChapterMode()"
                                            title="Add chapter manually as fallback">
                                        <i class="fas fa-plus me-1"></i>
                                        ${this.manualChapterMode ? 'Cancel Manual Entry' : 'Add Chapter Manually'}
                                    </button>
                                    <a href="name-dictionary.php?novel_id=${this.novelId}"
                                       class="btn btn-outline-primary btn-sm"
                                       title="Open name dictionary management">
                                        <i class="fas fa-users me-1"></i>
                                        Name Dictionary
                                    </a>
                                    <button class="btn btn-outline-secondary btn-sm" onclick="novelDetails.toggleBulkMode()">
                                        <i class="fas fa-check-square me-1"></i>
                                        ${this.bulkMode ? 'Exit Bulk Mode' : 'Bulk Select'}
                                    </button>
                                    <button class="btn btn-outline-primary btn-sm" onclick="novelDetails.refreshChapters()">
                                        <i class="fas fa-sync-alt me-1"></i>
                                        Refresh
                                    </button>
                                </div>
                            </div>

                            <!-- Search and Filter Controls -->
                            <div class="chapter-controls mb-3">
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <div class="input-group input-group-sm">
                                            <span class="input-group-text">
                                                <i class="fas fa-search"></i>
                                            </span>
                                            <input type="text" id="chapter-search" class="form-control"
                                                   placeholder="Search chapters by title or number..."
                                                   value="${this.searchTerm}"
                                                   onkeyup="novelDetails.handleSearchInput(event)">
                                            ${this.searchTerm ? `
                                                <button class="btn btn-outline-secondary" type="button"
                                                        onclick="novelDetails.clearSearch()"
                                                        title="Clear search">
                                                    <i class="fas fa-times"></i>
                                                </button>
                                            ` : ''}
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="d-flex justify-content-end align-items-center">
                                            ${this.renderPaginationInfo()}
                                        </div>
                                    </div>
                                </div>

                                <!-- Enhanced Controls Row -->
                                <div class="row">
                                    <div class="col-md-4">
                                        <label class="form-label-sm">Chapters per page:</label>
                                        <select class="form-select form-select-sm" id="chapters-per-page"
                                                onchange="novelDetails.changeChaptersPerPage(this.value)">
                                            <option value="10" ${this.chaptersPerPage == 10 ? 'selected' : ''}>10</option>
                                            <option value="20" ${this.chaptersPerPage == 20 ? 'selected' : ''}>20</option>
                                            <option value="50" ${this.chaptersPerPage == 50 ? 'selected' : ''}>50</option>
                                            <option value="100" ${this.chaptersPerPage == 100 ? 'selected' : ''}>100</option>
                                            <option value="all" ${this.chaptersPerPage == 'all' ? 'selected' : ''}>All</option>
                                        </select>
                                    </div>
                                    <div class="col-md-8">
                                        <label class="form-label-sm">Chapter range filter:</label>
                                        <div class="d-flex gap-2 align-items-center">
                                            <div class="input-group input-group-sm" style="max-width: 120px;">
                                                <span class="input-group-text">From</span>
                                                <input type="number" class="form-control" id="from-chapter"
                                                       placeholder="1" min="1"
                                                       value="${this.chapterRangeFilter.fromChapter || ''}"
                                                       onchange="novelDetails.updateRangeFilter()">
                                            </div>
                                            <div class="input-group input-group-sm" style="max-width: 120px;">
                                                <span class="input-group-text">To</span>
                                                <input type="number" class="form-control" id="to-chapter"
                                                       placeholder="999" min="1"
                                                       value="${this.chapterRangeFilter.toChapter || ''}"
                                                       onchange="novelDetails.updateRangeFilter()">
                                            </div>
                                            <button class="btn btn-outline-primary btn-sm" onclick="novelDetails.applyRangeFilter()">
                                                <i class="fas fa-filter me-1"></i>Filter
                                            </button>
                                            ${this.chapterRangeFilter.enabled ? `
                                                <button class="btn btn-outline-secondary btn-sm" onclick="novelDetails.clearRangeFilter()">
                                                    <i class="fas fa-times me-1"></i>Clear
                                                </button>
                                            ` : ''}
                                        </div>
                                        ${this.chapterRangeFilter.enabled ? `
                                            <small class="text-muted">
                                                <i class="fas fa-info-circle me-1"></i>
                                                Showing chapters ${this.chapterRangeFilter.fromChapter || 1} to ${this.chapterRangeFilter.toChapter || 'end'}
                                            </small>
                                        ` : ''}
                                    </div>
                                </div>
                            </div>

                            ${this.renderTitleTranslationControls()}
                            ${this.bulkMode ? this.renderBulkControls() : ''}
                            ${this.manualChapterMode ? this.renderManualChapterForm() : ''}
                        </div>
                        <div class="card-body p-0">
                            <div class="chapter-list">
                                ${this.renderChapterList()}
                            </div>
                        </div>
                        ${this.renderPagination()}
                    </div>
                </div>
            </div>
        `;

        document.getElementById('novel-details-content').innerHTML = detailsHtml;

        // Add custom styles for new features
        this.addCustomStyles();

        // Initialize manual chapter form if in manual mode
        if (this.manualChapterMode) {
            // Use setTimeout to ensure DOM is ready
            setTimeout(() => {
                this.initializeManualChapterForm();
            }, 100);
        }

        // Load POV management content
        this.loadPOVManagement();
    }

    renderProgressBar() {
        const completed = this.chapters.filter(ch => ch.translation_status === 'completed').length;
        const total = this.chapters.length;
        const progress = utils.calculateProgress(completed, total);

        return `
            <div class="progress-info mb-2">
                <span>Completed: ${completed}/${total} chapters (${progress}%)</span>
            </div>
            <div class="progress" style="height: 8px;">
                <div class="progress-bar bg-success" role="progressbar" 
                     style="width: ${progress}%" 
                     aria-valuenow="${progress}" 
                     aria-valuemin="0" 
                     aria-valuemax="100">
                </div>
            </div>
        `;
    }

    renderWordPressStatus() {
        if (!this.wordpressStatus) {
            return '';
        }

        const hasPostedDomains = this.wordpressStatus.all_domains && this.wordpressStatus.all_domains.length > 0;

        return `
            <div class="wordpress-status mt-4">
                <h6><i class="fab fa-wordpress me-2"></i>WordPress Status</h6>

                ${hasPostedDomains ? `
                    <div class="posted-domains mb-3">
                        <div class="row">
                            ${this.wordpressStatus.all_domains.map(domain => `
                                <div class="col-md-6 mb-2">
                                    <div class="card border-success">
                                        <div class="card-body p-2">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <div>
                                                    <small class="text-muted">Posted to:</small>
                                                    <div class="fw-bold">${utils.escapeHtml(domain.wordpress_domain)}</div>
                                                </div>
                                                <div class="text-end">
                                                    <span class="badge bg-success">Posted</span>
                                                    <div class="mt-1">
                                                        <a href="${utils.escapeHtml(domain.wordpress_url)}" target="_blank"
                                                           class="btn btn-sm btn-outline-primary">
                                                            <i class="fas fa-external-link-alt"></i>
                                                        </a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                ` : ''}

                <div class="wordpress-actions">
                    <div class="d-flex gap-2 align-items-center">
                        <button class="btn btn-outline-success btn-sm" onclick="novelDetails.showNovelWordPressProfileSelector()"
                                title="Post this novel to WordPress">
                            <i class="fab fa-wordpress me-1"></i>
                            Post Novel to WordPress
                        </button>

                        ${hasPostedDomains ? `
                            <small class="text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                Novel posted to ${this.wordpressStatus.all_domains.length} domain(s)
                            </small>
                        ` : `
                            <small class="text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                Novel not posted to WordPress yet
                            </small>
                        `}
                    </div>
                </div>
            </div>
        `;
    }

    renderChapterList() {
        if (this.chapters.length === 0) {
            return `
                <div class="text-center text-muted p-4">
                    <i class="fas fa-info-circle fa-2x mb-3"></i>
                    <p>No chapters found</p>
                </div>
            `;
        }

        return this.chapters.map(chapter => `
            <div class="chapter-item ${this.selectedChapters.has(chapter.chapter_number) ? 'selected' : ''} ${chapter.has_chunks ? 'has-chunks' : ''}">
                ${this.bulkMode ? `
                    <div class="chapter-checkbox">
                        <input type="checkbox" class="form-check-input"
                               ${this.selectedChapters.has(chapter.chapter_number) ? 'checked' : ''}
                               onchange="novelDetails.toggleChapterSelection(${chapter.chapter_number})">
                    </div>
                ` : ''}
                <div class="chapter-number">Ch. ${chapter.chapter_number}</div>
                <div class="chapter-title">
                    <div class="title-display" id="title-display-${chapter.chapter_number}">
                        <div class="title-text">
                            ${chapter.translated_title ?
                                utils.escapeHtml(chapter.translated_title) :
                                `<span class="text-muted">${utils.escapeHtml(chapter.original_title)}</span>`
                            }
                            ${chapter.translated_title ? `
                                <button class="btn btn-sm btn-outline-secondary ms-2"
                                        onclick="novelDetails.editChapterTitle(${chapter.chapter_number})"
                                        title="Edit translated title">
                                    <i class="fas fa-edit"></i>
                                </button>
                            ` : ''}
                        </div>
                        ${chapter.has_chunks && chapter.chunk_count > 0 ? `<small class="text-muted ms-2">(${chapter.chunk_count} parts)</small>` : ''}
                    </div>
                    <div class="title-edit" id="title-edit-${chapter.chapter_number}" style="display: none;">
                        <div class="input-group input-group-sm">
                            <input type="text" class="form-control" id="title-input-${chapter.chapter_number}"
                                   value="${utils.escapeHtml(chapter.translated_title || '')}"
                                   placeholder="Enter translated title..."
                                   onkeypress="if(event.key==='Enter') novelDetails.saveChapterTitle(${chapter.chapter_number})"
                                   onkeydown="if(event.key==='Escape') novelDetails.cancelEditChapterTitle(${chapter.chapter_number})">
                            <button class="btn btn-success" onclick="novelDetails.saveChapterTitle(${chapter.chapter_number})" title="Save title">
                                <i class="fas fa-check"></i>
                            </button>
                            <button class="btn btn-secondary" onclick="novelDetails.cancelEditChapterTitle(${chapter.chapter_number})" title="Cancel">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                </div>
                <div class="chapter-status status-${chapter.translation_status}">
                    ${utils.getStatusText(chapter.translation_status)}
                </div>
                <div class="chapter-actions">
                    ${this.renderChapterActions(chapter)}
                </div>
            </div>
        `).join('');
    }

    renderChapterActions(chapter) {
        let actions = [];

        // Save button for chapters without content
        if (!chapter.original_content) {
            actions.push(`
                <button class="btn btn-outline-primary btn-sm"
                        onclick="novelDetails.saveChapter(${chapter.chapter_number})"
                        ${chapter.translation_status === 'translating' ? 'disabled' : ''}>
                    <i class="fas fa-download me-1"></i>
                    Save
                </button>
            `);

            // Manual Add Content button for chapters without content
            actions.push(`
                <button class="btn btn-outline-warning btn-sm manual-content-btn"
                        onclick="novelDetails.showManualContentModal(${chapter.chapter_number})"
                        title="Manually add content for this chapter when automatic saving fails">
                    <i class="fas fa-edit me-1"></i>
                    Add Content
                </button>
            `);
        }

        // Split chapter button for chapters without chunks
        if (chapter.original_content && (!chapter.has_chunks || chapter.chunk_count === 0)) {
            actions.push(`
                <button class="btn btn-outline-warning btn-sm"
                        onclick="novelDetails.showSplitChapterModal(${chapter.chapter_number})"
                        title="Split chapter into smaller parts for easier translation">
                    <i class="fas fa-cut me-1"></i>
                    Split
                </button>
            `);
        }

        // Show chunking interface for chapters with chunks
        if (chapter.original_content && chapter.has_chunks && chapter.chunk_count > 0) {
            actions.push(`
                <button class="btn btn-outline-info btn-sm"
                        onclick="novelDetails.showChunkInterface(${chapter.id}, ${chapter.chapter_number})"
                        title="Translate chapter parts individually">
                    <i class="fas fa-puzzle-piece me-1"></i>
                    Parts (${chapter.chunk_count})
                </button>
            `);
        }

        // Translate button for saved chapters (regular translation)
        if (chapter.original_content && chapter.translation_status !== 'completed') {
            // Show different button text based on whether chapter has chunks
            const buttonText = chapter.has_chunks && chapter.chunk_count > 0 ? 'Translate All' : 'Translate';
            actions.push(`
                <button class="btn btn-outline-success btn-sm"
                        onclick="novelDetails.translateChapter(${chapter.chapter_number})"
                        ${chapter.translation_status === 'translating' ? 'disabled' : ''}>
                    <i class="fas fa-language me-1"></i>
                    ${chapter.translation_status === 'translating' ? 'Translating...' : buttonText}
                </button>
            `);
        }

        // Re-translate button for completed chapters
        if (chapter.translation_status === 'completed') {
            actions.push(`
                <button class="btn btn-outline-warning btn-sm"
                        onclick="novelDetails.retranslateChapter(${chapter.chapter_number})"
                        title="Clear and re-translate this chapter">
                    <i class="fas fa-redo me-1"></i>
                    Re-translate
                </button>
            `);
        }

        // Quality check button for completed chapters
        if (chapter.translation_status === 'completed') {
            actions.push(`
                <button class="btn btn-outline-info btn-sm"
                        onclick="novelDetails.showQualityCheckModal(${chapter.chapter_number})"
                        title="Check translation quality and consistency">
                    <i class="fas fa-check-circle me-1"></i>
                    Quality Check
                </button>
            `);
        }

        // Clear translation button for completed chapters
        if (chapter.translation_status === 'completed') {
            actions.push(`
                <button class="btn btn-outline-danger btn-sm"
                        onclick="novelDetails.clearChapterTranslation(${chapter.chapter_number})"
                        title="Clear translation for this chapter">
                    <i class="fas fa-trash me-1"></i>
                    Clear
                </button>
            `);
        }

        // Clear content button for chapters with content (similar to chapter view functionality)
        if (chapter.original_content) {
            actions.push(`
                <button class="btn btn-outline-warning btn-sm"
                        onclick="novelDetails.showClearContentConfirmation(${chapter.chapter_number})"
                        title="Clear chapter content while preserving the chapter entry">
                    <i class="fas fa-eraser me-1"></i>
                    Clear Content
                </button>
            `);
        }

        // Translate title button for chapters with original titles but no translated titles
        if (chapter.original_title && !chapter.translated_title) {
            actions.push(`
                <button class="btn btn-outline-secondary btn-sm"
                        onclick="novelDetails.translateChapterTitle(${chapter.chapter_number})"
                        title="Translate chapter title">
                    <i class="fas fa-font me-1"></i>
                    Title
                </button>
            `);

            // Add manual title button
            actions.push(`
                <button class="btn btn-outline-primary btn-sm"
                        onclick="novelDetails.addChapterTitle(${chapter.chapter_number})"
                        title="Add translated title manually">
                    <i class="fas fa-plus me-1"></i>
                    Add Title
                </button>
            `);
        }

        // View button for all chapters (show interface even for chapters without content)
        actions.push(`
            <a href="chapter-view.php?novel_id=${this.novelId}&chapter=${chapter.chapter_number}"
               class="btn btn-outline-info btn-sm"
               title="${chapter.original_content || chapter.translated_content ? 'View chapter content' : 'View chapter (no content yet - can save from source)'}">
                <i class="fas fa-eye me-1"></i>
                View
            </a>
        `);

        return actions.join(' ');
    }



    renderTitleTranslationControls() {
        // Count chapters that need title translation
        const chaptersNeedingTitleTranslation = this.chapters.filter(ch =>
            ch.original_title && !ch.translated_title
        ).length;

        if (chaptersNeedingTitleTranslation === 0) {
            return '';
        }

        return `
            <div class="title-translation-controls border-top pt-3 mb-3">
                <div class="d-flex justify-content-between align-items-center">
                    <div class="title-translation-info">
                        <small class="text-muted">
                            <i class="fas fa-font me-1"></i>
                            ${chaptersNeedingTitleTranslation} chapter titles need translation
                        </small>
                    </div>
                    <div class="title-translation-actions">
                        <div class="btn-group" role="group">
                            <button class="btn btn-outline-primary btn-sm"
                                    onclick="novelDetails.translateAllChapterTitles()"
                                    title="Translate all chapter titles at once">
                                <i class="fas fa-language me-1"></i>
                                Translate All Titles
                            </button>
                            <a href="chapter-title-manager.php?novel_id=${this.novelId}"
                               class="btn btn-outline-secondary btn-sm"
                               title="Open advanced title management interface">
                                <i class="fas fa-cog me-1"></i>
                                Manage Titles
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    renderBulkControls() {
        const selectedCount = this.selectedChapters.size;
        const translatedChapters = Array.from(this.selectedChapters).filter(chapterNum => {
            const chapter = this.chapters.find(ch => ch.chapter_number === chapterNum);
            return chapter && chapter.translation_status === 'completed';
        });

        return `
            <div class="bulk-controls border-top pt-3">
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <div class="bulk-selection-info">
                        <span class="badge bg-primary">${selectedCount} selected</span>
                        ${translatedChapters.length > 0 ? `<span class="badge bg-success">${translatedChapters.length} translated</span>` : ''}
                    </div>
                    <div class="bulk-selection-controls">
                        <button class="btn btn-outline-secondary btn-sm me-2" onclick="novelDetails.selectAllChapters()">
                            <i class="fas fa-check-double me-1"></i>
                            Select All
                        </button>
                        <button class="btn btn-outline-secondary btn-sm" onclick="novelDetails.clearSelection()">
                            <i class="fas fa-times me-1"></i>
                            Clear
                        </button>
                    </div>
                </div>
                ${selectedCount > 0 ? `
                    <div class="bulk-actions d-flex gap-2">
                        <button class="btn btn-success btn-sm" onclick="novelDetails.bulkTranslate()"
                                ${selectedCount === 0 ? 'disabled' : ''}>
                            <i class="fas fa-language me-1"></i>
                            Translate Selected (${selectedCount})
                        </button>
                        ${translatedChapters.length > 0 ? `
                            <button class="btn btn-warning btn-sm" onclick="novelDetails.bulkRetranslate()"
                                    title="Clear and re-translate selected chapters">
                                <i class="fas fa-redo me-1"></i>
                                Re-translate (${translatedChapters.length})
                            </button>
                            <button class="btn btn-info btn-sm" onclick="novelDetails.showBulkQualityCheckModal()"
                                    title="Check translation quality for selected chapters">
                                <i class="fas fa-check-circle me-1"></i>
                                Quality Check (${translatedChapters.length})
                            </button>
                            <div class="btn-group">
                                <button class="btn btn-outline-success btn-sm" onclick="novelDetails.showBulkWordPressProfileSelector()"
                                        title="Post selected translated chapters to WordPress">
                                    <i class="fab fa-wordpress me-1"></i>
                                    Post to WordPress (${translatedChapters.length})
                                </button>
                                <button type="button" class="btn btn-outline-success btn-sm dropdown-toggle dropdown-toggle-split"
                                        data-bs-toggle="dropdown" aria-expanded="false">
                                    <span class="visually-hidden">Toggle Dropdown</span>
                                </button>
                                <ul class="dropdown-menu">
                                    <li><h6 class="dropdown-header">Select WordPress Profile</h6></li>
                                    <li><a class="dropdown-item" href="#" onclick="novelDetails.showBulkWordPressProfileSelector()">
                                        <i class="fas fa-list me-2"></i>Choose Profile...
                                    </a></li>
                                </ul>
                            </div>
                            <button class="btn btn-danger btn-sm" onclick="novelDetails.bulkClearTranslations()"
                                    title="Clear translations for selected chapters">
                                <i class="fas fa-trash me-1"></i>
                                Clear Translations (${translatedChapters.length})
                            </button>
                        ` : ''}
                    </div>
                ` : ''}
            </div>
        `;
    }

    toggleBulkMode() {
        this.bulkMode = !this.bulkMode;
        if (!this.bulkMode) {
            this.selectedChapters.clear();
        }
        this.displayNovelDetails();
    }

    toggleChapterSelection(chapterNumber) {
        if (this.selectedChapters.has(chapterNumber)) {
            this.selectedChapters.delete(chapterNumber);
        } else {
            this.selectedChapters.add(chapterNumber);
        }
        this.displayNovelDetails();
    }

    selectAllChapters() {
        this.chapters.forEach(chapter => {
            this.selectedChapters.add(chapter.chapter_number);
        });
        this.displayNovelDetails();
    }

    clearSelection() {
        this.selectedChapters.clear();
        this.displayNovelDetails();
    }

    // Search and Pagination Methods
    handleSearchInput(event) {
        const searchTerm = event.target.value.trim();

        // Use debounced search to avoid too many API calls
        clearTimeout(this.searchTimeout);
        this.searchTimeout = setTimeout(() => {
            this.searchTerm = searchTerm;
            this.currentPage = 1; // Reset to first page when searching
            this.loadNovelDetails();
        }, 300);
    }

    clearSearch() {
        this.searchTerm = '';
        this.currentPage = 1;
        this.loadNovelDetails();
    }

    renderPaginationInfo() {
        if (!this.pagination.total_pages || this.pagination.total_pages <= 1) {
            return '';
        }

        const start = ((this.pagination.current_page - 1) * this.pagination.per_page) + 1;
        const end = Math.min(start + this.pagination.per_page - 1, this.pagination.total_chapters);

        return `
            <small class="text-muted">
                Showing ${start}-${end} of ${this.pagination.total_chapters} chapters
            </small>
        `;
    }

    renderPagination() {
        if (!this.pagination.total_pages || this.pagination.total_pages <= 1) {
            return '';
        }

        const currentPage = this.pagination.current_page;
        const totalPages = this.pagination.total_pages;

        let paginationHtml = `
            <div class="card-footer">
                <nav aria-label="Chapter pagination">
                    <ul class="pagination pagination-sm justify-content-center mb-0">
        `;

        // Previous button
        paginationHtml += `
            <li class="page-item ${!this.pagination.has_prev ? 'disabled' : ''}">
                <button class="page-link" onclick="novelDetails.goToPage(${currentPage - 1})"
                        ${!this.pagination.has_prev ? 'disabled' : ''}>
                    <i class="fas fa-chevron-left"></i> Previous
                </button>
            </li>
        `;

        // Page numbers
        const startPage = Math.max(1, currentPage - 2);
        const endPage = Math.min(totalPages, currentPage + 2);

        if (startPage > 1) {
            paginationHtml += `
                <li class="page-item">
                    <button class="page-link" onclick="novelDetails.goToPage(1)">1</button>
                </li>
            `;
            if (startPage > 2) {
                paginationHtml += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
            }
        }

        for (let i = startPage; i <= endPage; i++) {
            paginationHtml += `
                <li class="page-item ${i === currentPage ? 'active' : ''}">
                    <button class="page-link" onclick="novelDetails.goToPage(${i})">${i}</button>
                </li>
            `;
        }

        if (endPage < totalPages) {
            if (endPage < totalPages - 1) {
                paginationHtml += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
            }
            paginationHtml += `
                <li class="page-item">
                    <button class="page-link" onclick="novelDetails.goToPage(${totalPages})">${totalPages}</button>
                </li>
            `;
        }

        // Next button
        paginationHtml += `
            <li class="page-item ${!this.pagination.has_next ? 'disabled' : ''}">
                <button class="page-link" onclick="novelDetails.goToPage(${currentPage + 1})"
                        ${!this.pagination.has_next ? 'disabled' : ''}>
                    Next <i class="fas fa-chevron-right"></i>
                </button>
            </li>
        `;

        paginationHtml += `
                    </ul>
                </nav>
            </div>
        `;

        return paginationHtml;
    }

    goToPage(page) {
        if (page >= 1 && page <= this.pagination.total_pages && page !== this.currentPage) {
            this.currentPage = page;
            this.loadNovelDetails();
        }
    }

    // Enhanced Chapter Controls Methods
    changeChaptersPerPage(value) {
        this.chaptersPerPage = value === 'all' ? 'all' : parseInt(value);
        sessionStorage.setItem('chaptersPerPage', this.chaptersPerPage);
        this.currentPage = 1; // Reset to first page
        this.loadNovelDetails();
    }

    updateRangeFilter() {
        const fromChapter = document.getElementById('from-chapter')?.value;
        const toChapter = document.getElementById('to-chapter')?.value;

        this.chapterRangeFilter.fromChapter = fromChapter ? parseInt(fromChapter) : null;
        this.chapterRangeFilter.toChapter = toChapter ? parseInt(toChapter) : null;
    }

    applyRangeFilter() {
        this.updateRangeFilter();

        // Validate range
        if (this.chapterRangeFilter.fromChapter && this.chapterRangeFilter.toChapter) {
            if (this.chapterRangeFilter.fromChapter > this.chapterRangeFilter.toChapter) {
                utils.showToast('From chapter cannot be greater than To chapter', 'warning');
                return;
            }
        }

        this.chapterRangeFilter.enabled = !!(this.chapterRangeFilter.fromChapter || this.chapterRangeFilter.toChapter);
        this.currentPage = 1; // Reset to first page
        this.loadNovelDetails();
    }

    clearRangeFilter() {
        this.chapterRangeFilter = {
            enabled: false,
            fromChapter: null,
            toChapter: null
        };

        // Clear the input fields
        const fromInput = document.getElementById('from-chapter');
        const toInput = document.getElementById('to-chapter');
        if (fromInput) fromInput.value = '';
        if (toInput) toInput.value = '';

        this.currentPage = 1; // Reset to first page
        this.loadNovelDetails();
    }

    // Title Translation Methods
    async translateAllChapterTitles() {
        const chaptersNeedingTranslation = this.chapters.filter(ch =>
            ch.original_title && !ch.translated_title
        );

        if (chaptersNeedingTranslation.length === 0) {
            utils.showToast('No chapter titles need translation', 'info');
            return;
        }

        const confirmed = confirm(`Translate ${chaptersNeedingTranslation.length} chapter titles?`);
        if (!confirmed) return;

        utils.showLoading(true);

        try {
            const result = await utils.makeApiRequest('api/chapter-titles.php', {
                method: 'PUT',
                body: JSON.stringify({
                    novel_id: this.novelId,
                    translate_all: true,
                    target_language: 'en'
                })
            });

            if (result.success) {
                utils.showToast(result.data.message, 'success');
                this.refreshChapters();
            } else {
                utils.showToast(result.error || 'Failed to translate titles', 'error');
            }
        } catch (error) {
            console.error('Translate all titles error:', error);
            utils.showToast('Network error occurred', 'error');
        } finally {
            utils.showLoading(false);
        }
    }

    async translateChapterTitle(chapterNumber) {
        const chapter = this.chapters.find(ch => ch.chapter_number === chapterNumber);
        if (!chapter || !chapter.original_title) {
            utils.showToast('Chapter or original title not found', 'error');
            return;
        }

        if (chapter.translated_title) {
            utils.showToast('Chapter title already translated', 'info');
            return;
        }

        utils.showLoading(true);

        try {
            const result = await utils.makeApiRequest('api/chapter-titles.php', {
                method: 'PUT',
                body: JSON.stringify({
                    novel_id: this.novelId,
                    chapter_number: chapterNumber,
                    target_language: 'en'
                })
            });

            if (result.success) {
                utils.showToast(`Chapter ${chapterNumber} title translated successfully`, 'success');
                this.refreshChapters();
            } else {
                utils.showToast(result.error || 'Failed to translate title', 'error');
            }
        } catch (error) {
            console.error('Translate title error:', error);
            utils.showToast('Network error occurred', 'error');
        } finally {
            utils.showLoading(false);
        }
    }

    // Title Editing Methods
    editTitle() {
        document.getElementById('novel-title-display').style.display = 'none';
        document.getElementById('novel-title-edit').style.display = 'block';
        document.getElementById('novel-title-input').focus();
    }

    cancelEditTitle() {
        document.getElementById('novel-title-display').style.display = 'block';
        document.getElementById('novel-title-edit').style.display = 'none';
        // Reset input value
        document.getElementById('novel-title-input').value = this.novel.translated_title || this.novel.original_title;
    }

    async saveTitle() {
        const newTitle = document.getElementById('novel-title-input').value.trim();

        if (!newTitle) {
            utils.showToast('Title cannot be empty', 'error');
            return;
        }

        if (newTitle === (this.novel.translated_title || this.novel.original_title)) {
            this.cancelEditTitle();
            return;
        }

        try {
            const result = await utils.makeApiRequest('api/novels.php', {
                method: 'PUT',
                body: JSON.stringify({
                    novel_id: this.novelId,
                    translated_title: newTitle
                })
            });

            if (result.success) {
                this.novel.translated_title = newTitle;
                utils.showToast('Title updated successfully', 'success');
                this.cancelEditTitle();
                // Update the display
                document.getElementById('novel-title-display').textContent = newTitle;
            } else {
                utils.showToast(result.error || 'Failed to update title', 'error');
            }
        } catch (error) {
            console.error('Save title error:', error);
            utils.showToast('Network error occurred', 'error');
        }
    }

    // Synopsis Rendering
    renderSynopsis() {
        const hasOriginal = this.novel.original_synopsis && this.novel.original_synopsis.trim();
        const hasTranslated = this.novel.translated_synopsis && this.novel.translated_synopsis.trim();

        return `
            <div class="novel-synopsis mb-3">
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>Synopsis</h6>
                    <div class="synopsis-controls">
                        <button class="btn btn-sm btn-outline-primary" onclick="novelDetails.editDescription()" title="Edit Description">
                            <i class="fas fa-edit me-1"></i>
                            Edit Description
                        </button>
                        ${hasOriginal ? `
                            <button class="btn btn-sm btn-outline-success ms-1" onclick="novelDetails.translateDescription()" title="Translate Description">
                                <i class="fas fa-language me-1"></i>
                                Translate
                            </button>
                        ` : ''}
                    </div>
                </div>

                <!-- Edit Description Form (hidden by default) -->
                <div id="edit-description-form" class="card mb-3" style="display: none;">
                    <div class="card-header">
                        <h6 class="mb-0">Edit Original Description</h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="original-description-input" class="form-label">Original Description (Japanese)</label>
                            <textarea id="original-description-input" class="form-control" rows="8"
                                      placeholder="Enter the original novel description in Japanese...">${utils.escapeHtml(this.novel.original_synopsis || '')}</textarea>
                        </div>
                        <div class="d-flex gap-2">
                            <button class="btn btn-primary" onclick="novelDetails.saveDescription()">
                                <i class="fas fa-save me-1"></i>
                                Save Description
                            </button>
                            <button class="btn btn-success" onclick="novelDetails.saveAndTranslateDescription()">
                                <i class="fas fa-language me-1"></i>
                                Save & Translate
                            </button>
                            <button class="btn btn-secondary" onclick="novelDetails.cancelEditDescription()">
                                <i class="fas fa-times me-1"></i>
                                Cancel
                            </button>
                        </div>
                    </div>
                </div>

                ${hasTranslated ? `
                    <div class="synopsis-section mb-2">
                        <button class="btn btn-sm btn-outline-primary mb-2" type="button"
                                data-bs-toggle="collapse" data-bs-target="#translated-synopsis"
                                aria-expanded="true" aria-controls="translated-synopsis">
                            <i class="fas fa-eye me-1"></i>
                            Hide Translated Synopsis
                        </button>
                        <div class="collapse show" id="translated-synopsis">
                            <div class="synopsis-content">
                                ${this.renderTranslatedSynopsisWithReadMore(this.novel.translated_synopsis)}
                            </div>
                        </div>
                    </div>
                ` : ''}
                ${hasOriginal ? `
                    <div class="synopsis-section">
                        <button class="btn btn-sm btn-outline-secondary mb-2" type="button"
                                data-bs-toggle="collapse" data-bs-target="#original-synopsis"
                                aria-expanded="false" aria-controls="original-synopsis">
                            <i class="fas fa-eye me-1"></i>
                            Show Original Synopsis
                        </button>
                        <div class="collapse" id="original-synopsis">
                            <div class="synopsis-content text-muted">
                                ${utils.escapeHtml(this.novel.original_synopsis).replace(/\n/g, '<br>')}
                            </div>
                        </div>
                    </div>
                ` : ''}
                ${!hasOriginal && !hasTranslated ? `
                    <div class="text-muted">
                        <i class="fas fa-info-circle me-1"></i>
                        No synopsis available. Click "Edit Description" to add one.
                    </div>
                ` : ''}
            </div>
        `;
    }

    renderTranslatedSynopsisWithReadMore(synopsis) {
        if (!synopsis) return '';

        const maxLength = 250; // Characters to show initially
        const fullText = utils.escapeHtml(synopsis).replace(/\n/g, '<br>');

        if (synopsis.length <= maxLength) {
            return fullText;
        }

        const truncatedText = utils.escapeHtml(synopsis.substring(0, maxLength)).replace(/\n/g, '<br>');

        return `
            <div id="synopsis-preview">
                <span id="synopsis-truncated">${truncatedText}...</span>
                <span id="synopsis-full" style="display: none;">${fullText}</span>
                <br>
                <button class="btn btn-sm btn-link p-0 mt-1" onclick="novelDetails.toggleSynopsisReadMore()" id="synopsis-toggle">
                    <i class="fas fa-chevron-down me-1"></i>
                    Read more
                </button>
            </div>
        `;
    }

    toggleSynopsisReadMore() {
        const truncated = document.getElementById('synopsis-truncated');
        const full = document.getElementById('synopsis-full');
        const toggle = document.getElementById('synopsis-toggle');

        if (!truncated || !full || !toggle) return;

        if (truncated.style.display === 'none') {
            // Show truncated version
            truncated.style.display = 'inline';
            full.style.display = 'none';
            toggle.innerHTML = '<i class="fas fa-chevron-down me-1"></i>Read more';
        } else {
            // Show full version
            truncated.style.display = 'none';
            full.style.display = 'inline';
            toggle.innerHTML = '<i class="fas fa-chevron-up me-1"></i>Show less';
        }
    }

    addCustomStyles() {
        // Add custom styles for the new features if not already added
        if (!document.getElementById('novel-details-custom-styles')) {
            const style = document.createElement('style');
            style.id = 'novel-details-custom-styles';
            style.textContent = `
                .editable-title {
                    cursor: pointer;
                    border-bottom: 1px dashed transparent;
                    transition: border-color 0.2s;
                }
                .editable-title:hover {
                    border-bottom-color: #007bff;
                }
                .chapter-controls {
                    background-color: #f8f9fa;
                    padding: 15px;
                    border-radius: 0.375rem;
                    margin-bottom: 0;
                }
                .synopsis-content {
                    line-height: 1.6;
                    padding: 10px;
                    background-color: #f8f9fa;
                    border-radius: 0.375rem;
                    border-left: 4px solid #007bff;
                }
                .pagination .page-link {
                    color: #007bff;
                }
                .pagination .page-item.active .page-link {
                    background-color: #007bff;
                    border-color: #007bff;
                }
                .title-translation-controls {
                    background-color: #f8f9fa;
                    border-radius: 0.375rem;
                    padding: 10px 15px;
                }
                .title-translation-info {
                    color: #6c757d;
                    font-size: 0.875rem;
                }
                .title-translation-actions .btn {
                    font-size: 0.875rem;
                }
                .chapter-title {
                    position: relative;
                    flex-grow: 1;
                }
                .title-display {
                    display: flex;
                    align-items: center;
                    flex-wrap: wrap;
                    gap: 0.5rem;
                }
                .title-text {
                    display: flex;
                    align-items: center;
                    flex-grow: 1;
                }
                .title-edit {
                    width: 100%;
                }
                .title-edit .input-group {
                    max-width: 100%;
                }
                .title-edit .form-control {
                    min-width: 200px;
                }
                .chapter-item .btn-sm {
                    padding: 0.125rem 0.25rem;
                    font-size: 0.75rem;
                }
                .wordpress-status {
                    background-color: #f8f9fa;
                    border-radius: 0.375rem;
                    padding: 15px;
                    border-left: 4px solid #21759b;
                }
                .wordpress-status h6 {
                    color: #21759b;
                    margin-bottom: 15px;
                }
                .posted-domains .card {
                    border-color: #198754;
                    background-color: #f8fff9;
                }
                .posted-domains .card-body {
                    padding: 0.75rem;
                }
                .wordpress-actions {
                    margin-top: 10px;
                }
                .wordpress-actions .btn {
                    font-size: 0.875rem;
                }
                .manual-chapter-form {
                    border: 2px dashed #ffc107;
                    background: linear-gradient(135deg, #fff9e6 0%, #fff3cd 100%);
                    transition: all 0.3s ease;
                }
                .manual-chapter-form:hover {
                    border-color: #ffb300;
                    box-shadow: 0 4px 8px rgba(255, 193, 7, 0.2);
                }
                .manual-chapter-form .form-label {
                    font-weight: 600;
                    color: #856404;
                }
                .manual-chapter-form .form-control {
                    border-color: #ffc107;
                }
                .manual-chapter-form .form-control:focus {
                    border-color: #ffb300;
                    box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.25);
                }
                .manual-chapter-form .alert-sm {
                    padding: 0.375rem 0.75rem;
                    font-size: 0.875rem;
                }
                .manual-chapter-form textarea {
                    resize: vertical;
                    min-height: 120px;
                }
                .manual-chapter-form #manual-original-content {
                    font-family: 'Courier New', monospace;
                    line-height: 1.5;
                }
                /* Manual content modal styling */
                #manualContentModal .modal-body {
                    max-height: 70vh;
                    overflow-y: auto;
                }
                #manualContentModal textarea {
                    font-family: 'Courier New', monospace;
                    line-height: 1.5;
                    resize: vertical;
                }
                /* Add Content button styling */
                .btn-outline-warning.manual-content-btn {
                    border-color: #fd7e14;
                    color: #fd7e14;
                }
                .btn-outline-warning.manual-content-btn:hover {
                    background-color: #fd7e14;
                    border-color: #fd7e14;
                    color: white;
                }
            `;
            document.head.appendChild(style);
        }
    }

    async bulkTranslate() {
        if (this.selectedChapters.size === 0) {
            utils.showToast('No chapters selected', 'warning');
            return;
        }

        const chapterNumbers = Array.from(this.selectedChapters);
        const confirmed = confirm(`Translate ${chapterNumbers.length} selected chapters?`);

        if (!confirmed) return;

        // Show POV selection modal before bulk translation
        const povSelection = await this.showPOVSelectionModal(chapterNumbers, 'bulk');
        if (!povSelection) {
            return; // User cancelled
        }

        utils.showLoading(true);

        try {
            const result = await utils.makeApiRequest('api/chapters.php', {
                method: 'PUT',
                body: JSON.stringify({
                    novel_id: this.novelId,
                    chapter_numbers: chapterNumbers,
                    target_language: 'en',
                    pov_preference: povSelection.perspective,
                    save_pov_preference: povSelection.saveAsDefault
                })
            });

            if (result.success) {
                utils.showToast(result.data.message, 'success');
                this.refreshChapters();
            } else {
                utils.showToast(result.error || 'Failed to translate chapters', 'error');
            }
        } catch (error) {
            console.error('Bulk translate error:', error);
            utils.showToast('Network error occurred', 'error');
        } finally {
            utils.showLoading(false);
        }
    }

    async bulkRetranslate() {
        if (this.selectedChapters.size === 0) {
            utils.showToast('No chapters selected', 'warning');
            return;
        }

        const chapterNumbers = Array.from(this.selectedChapters);
        const confirmed = confirm(`Re-translate ${chapterNumbers.length} selected chapters? This will clear existing translations and translate them again.`);

        if (!confirmed) return;

        // Show POV selection modal before bulk retranslation
        const povSelection = await this.showPOVSelectionModal(chapterNumbers, 'bulk_retranslate');
        if (!povSelection) {
            return; // User cancelled
        }

        utils.showLoading(true);

        try {
            const result = await utils.makeApiRequest('api/chapters.php', {
                method: 'PUT',
                body: JSON.stringify({
                    novel_id: this.novelId,
                    chapter_numbers: chapterNumbers,
                    target_language: 'en',
                    action: 'retranslate',
                    pov_preference: povSelection.perspective,
                    save_pov_preference: povSelection.saveAsDefault
                })
            });

            if (result.success) {
                utils.showToast(result.data.message, 'success');
                this.refreshChapters();
            } else {
                utils.showToast(result.error || 'Failed to re-translate chapters', 'error');
            }
        } catch (error) {
            console.error('Bulk retranslate error:', error);
            utils.showToast('Network error occurred', 'error');
        } finally {
            utils.showLoading(false);
        }
    }

    async bulkClearTranslations() {
        if (this.selectedChapters.size === 0) {
            utils.showToast('No chapters selected', 'warning');
            return;
        }

        const chapterNumbers = Array.from(this.selectedChapters);
        const confirmed = confirm(`Clear translations for ${chapterNumbers.length} selected chapters? This action cannot be undone.`);

        if (!confirmed) return;

        utils.showLoading(true);

        try {
            const result = await utils.makeApiRequest('api/chapters.php', {
                method: 'DELETE',
                body: JSON.stringify({
                    novel_id: this.novelId,
                    chapter_numbers: chapterNumbers
                })
            });

            if (result.success) {
                utils.showToast(result.data.message, 'success');
                this.refreshChapters();
            } else {
                utils.showToast(result.error || 'Failed to clear translations', 'error');
            }
        } catch (error) {
            console.error('Bulk clear translations error:', error);
            utils.showToast('Network error occurred', 'error');
        } finally {
            utils.showLoading(false);
        }
    }

    async saveChapter(chapterNumber) {
        utils.showLoading(true);

        try {
            const result = await utils.makeApiRequest('api/chapters.php', {
                method: 'POST',
                body: JSON.stringify({
                    novel_id: this.novelId,
                    chapter_number: chapterNumber
                })
            });

            if (result.success) {
                utils.showToast('Chapter saved successfully', 'success');
                this.refreshChapters();
            } else {
                utils.showToast(result.error || 'Failed to save chapter', 'error');
            }
        } catch (error) {
            console.error('Save chapter error:', error);
            utils.showToast('Network error occurred', 'error');
        } finally {
            utils.showLoading(false);
        }
    }

    async translateChapter(chapterNumber) {
        console.log(`Starting translation for chapter ${chapterNumber}`);

        // Show POV selection modal before translation
        const povSelection = await this.showPOVSelectionModal(chapterNumber, 'single');
        if (!povSelection) {
            return; // User cancelled
        }

        // Create progress bar container if it doesn't exist
        let progressContainer = document.getElementById('translation-progress');
        if (!progressContainer) {
            console.log('Creating new progress container');
            progressContainer = document.createElement('div');
            progressContainer.id = 'translation-progress';
            progressContainer.className = 'mb-3';

            // Use a more robust approach to find the insertion point
            let insertionSuccessful = false;

            try {
                // Try to find the chapters card specifically by looking for the chapter list
                const chapterList = document.querySelector('.chapter-list');
                if (chapterList) {
                    // Navigate up to find the chapters card (.card containing the chapter-list)
                    let chaptersCard = chapterList.closest('.card');
                    if (chaptersCard && chaptersCard.parentElement) {
                        chaptersCard.parentElement.insertBefore(progressContainer, chaptersCard);
                        console.log('Progress container inserted before chapters card via chapter-list');
                        insertionSuccessful = true;
                    }
                }

                // Fallback 1: Try to insert in the first column (col-lg-8)
                if (!insertionSuccessful) {
                    const firstColumn = document.querySelector('.col-lg-8');
                    if (firstColumn) {
                        const chaptersCard = firstColumn.querySelector('.card:last-child'); // Last card in first column
                        if (chaptersCard) {
                            firstColumn.insertBefore(progressContainer, chaptersCard);
                            console.log('Progress container inserted before chapters card in first column');
                            insertionSuccessful = true;
                        }
                    }
                }

                // Fallback 2: Insert at the top of the main content area
                if (!insertionSuccessful) {
                    const fallbackContainer = document.getElementById('novel-details-content');
                    if (fallbackContainer) {
                        const firstChild = fallbackContainer.firstElementChild;
                        if (firstChild) {
                            fallbackContainer.insertBefore(progressContainer, firstChild);
                            console.log('Progress container inserted at top of content area');
                            insertionSuccessful = true;
                        } else {
                            fallbackContainer.appendChild(progressContainer);
                            console.log('Progress container appended to content area');
                            insertionSuccessful = true;
                        }
                    }
                }

            } catch (error) {
                console.error('Error inserting progress container:', error);
                insertionSuccessful = false;
            }

            if (!insertionSuccessful) {
                console.warn('Could not find suitable container for progress bar, continuing without progress indication');
            }
        } else {
            console.log('Using existing progress container');
        }

        // Create progress bar
        const progressBar = utils.createProgressBar('translation-progress', {
            title: `Translating Chapter ${chapterNumber}`,
            showPercentage: true,
            showStatus: true,
            animated: true,
            color: 'primary'
        });

        if (!progressBar) {
            console.warn('Failed to create progress bar, continuing without progress indication');
        }

        try {
            // Use progress bar if available, otherwise fall back to regular request
            let result;
            if (progressBar) {
                result = await this.makeTranslationRequestWithRetry({
                    novel_id: this.novelId,
                    chapter_number: chapterNumber,
                    target_language: 'en',
                    pov_preference: povSelection.perspective,
                    save_pov_preference: povSelection.saveAsDefault
                }, progressBar);
            } else {
                // Fallback to regular API request without progress bar
                utils.showLoading(true);
                result = await utils.makeApiRequest('api/chapters.php', {
                    method: 'PUT',
                    body: JSON.stringify({
                        novel_id: this.novelId,
                        chapter_number: chapterNumber,
                        target_language: 'en',
                        pov_preference: povSelection.perspective,
                        save_pov_preference: povSelection.saveAsDefault
                    })
                }, true); // Mark as translation request
                utils.showLoading(false);
            }

            if (result.success) {
                utils.showToast('Chapter translated successfully', 'success');
                this.refreshChapters();
            } else {
                utils.showToast(result.error || 'Failed to translate chapter', 'error');
            }
        } catch (error) {
            console.error('Translate chapter error:', error);

            // Provide more specific error messages
            let errorMessage = 'Network error occurred';
            if (error.message.includes('timed out')) {
                errorMessage = 'Translation timed out. This may happen with very long content. Please try again or consider breaking the content into smaller sections.';
            } else if (error.message.includes('Rate limit')) {
                errorMessage = 'API rate limit reached. Please wait a moment and try again.';
            } else if (error.message.includes('Service Unavailable')) {
                errorMessage = 'Translation service is temporarily busy. Please try again in a few moments.';
            }

            utils.showToast(errorMessage, 'error');
            if (!progressBar) {
                utils.showLoading(false);
            }
        }
    }

    async retranslateChapter(chapterNumber) {
        const confirmed = confirm(`Re-translate chapter ${chapterNumber}? This will clear the existing translation and translate it again.`);

        if (!confirmed) return;

        // Show POV selection modal before retranslation
        const povSelection = await this.showPOVSelectionModal(chapterNumber, 'retranslate');
        if (!povSelection) {
            return; // User cancelled
        }

        // Create progress bar container if it doesn't exist
        let progressContainer = document.getElementById('translation-progress');
        if (!progressContainer) {
            progressContainer = document.createElement('div');
            progressContainer.id = 'translation-progress';
            progressContainer.className = 'mb-3';

            // Use a more robust approach to find the insertion point
            let insertionSuccessful = false;

            try {
                // Try to find the chapters card specifically by looking for the chapter list
                const chapterList = document.querySelector('.chapter-list');
                if (chapterList) {
                    // Navigate up to find the chapters card (.card containing the chapter-list)
                    let chaptersCard = chapterList.closest('.card');
                    if (chaptersCard && chaptersCard.parentElement) {
                        chaptersCard.parentElement.insertBefore(progressContainer, chaptersCard);
                        console.log('Progress container inserted before chapters card via chapter-list');
                        insertionSuccessful = true;
                    }
                }

                // Fallback 1: Try to insert in the first column (col-lg-8)
                if (!insertionSuccessful) {
                    const firstColumn = document.querySelector('.col-lg-8');
                    if (firstColumn) {
                        const chaptersCard = firstColumn.querySelector('.card:last-child'); // Last card in first column
                        if (chaptersCard) {
                            firstColumn.insertBefore(progressContainer, chaptersCard);
                            console.log('Progress container inserted before chapters card in first column');
                            insertionSuccessful = true;
                        }
                    }
                }

                // Fallback 2: Insert at the top of the main content area
                if (!insertionSuccessful) {
                    const fallbackContainer = document.getElementById('novel-details-content');
                    if (fallbackContainer) {
                        const firstChild = fallbackContainer.firstElementChild;
                        if (firstChild) {
                            fallbackContainer.insertBefore(progressContainer, firstChild);
                            console.log('Progress container inserted at top of content area');
                            insertionSuccessful = true;
                        } else {
                            fallbackContainer.appendChild(progressContainer);
                            console.log('Progress container appended to content area');
                            insertionSuccessful = true;
                        }
                    }
                }

            } catch (error) {
                console.error('Error inserting progress container:', error);
                insertionSuccessful = false;
            }

            if (!insertionSuccessful) {
                console.warn('Could not find suitable container for progress bar, continuing without progress indication');
                // Don't return early - continue with translation without progress bar
            }
        }

        // Create progress bar
        const progressBar = utils.createProgressBar('translation-progress', {
            title: `Re-translating Chapter ${chapterNumber}`,
            showPercentage: true,
            showStatus: true,
            animated: true,
            color: 'warning'
        });

        if (!progressBar) {
            console.warn('Failed to create progress bar, continuing without progress indication');
        }

        try {
            // Use progress bar if available, otherwise fall back to regular request
            let result;
            if (progressBar) {
                result = await this.makeTranslationRequestWithRetry({
                    novel_id: this.novelId,
                    chapter_number: chapterNumber,
                    target_language: 'en',
                    action: 'retranslate',
                    pov_preference: povSelection.perspective,
                    save_pov_preference: povSelection.saveAsDefault
                }, progressBar);
            } else {
                // Fallback to regular API request without progress bar
                utils.showLoading(true);
                result = await utils.makeApiRequest('api/chapters.php', {
                    method: 'PUT',
                    body: JSON.stringify({
                        novel_id: this.novelId,
                        chapter_number: chapterNumber,
                        target_language: 'en',
                        action: 'retranslate',
                        pov_preference: povSelection.perspective,
                        save_pov_preference: povSelection.saveAsDefault
                    })
                }, true); // Mark as translation request
                utils.showLoading(false);
            }

            if (result.success) {
                utils.showToast('Chapter re-translated successfully', 'success');
                this.refreshChapters();
            } else {
                utils.showToast(result.error || 'Failed to re-translate chapter', 'error');
            }
        } catch (error) {
            console.error('Retranslate chapter error:', error);

            // Provide more specific error messages
            let errorMessage = 'Network error occurred';
            if (error.message.includes('timed out')) {
                errorMessage = 'Re-translation timed out. This may happen with very long content. Please try again or consider breaking the content into smaller sections.';
            } else if (error.message.includes('Rate limit')) {
                errorMessage = 'API rate limit reached. Please wait a moment and try again.';
            } else if (error.message.includes('Service Unavailable')) {
                errorMessage = 'Translation service is temporarily busy. Please try again in a few moments.';
            }

            utils.showToast(errorMessage, 'error');
            if (!progressBar) {
                utils.showLoading(false);
            }
        }
    }

    async clearChapterTranslation(chapterNumber) {
        const confirmed = confirm(`Clear translation for chapter ${chapterNumber}? This action cannot be undone.`);

        if (!confirmed) return;

        utils.showLoading(true);

        try {
            const result = await utils.makeApiRequest('api/chapters.php', {
                method: 'DELETE',
                body: JSON.stringify({
                    novel_id: this.novelId,
                    chapter_number: chapterNumber
                })
            });

            if (result.success) {
                utils.showToast('Chapter translation cleared successfully', 'success');
                this.refreshChapters();
            } else {
                utils.showToast(result.error || 'Failed to clear translation', 'error');
            }
        } catch (error) {
            console.error('Clear translation error:', error);
            utils.showToast('Network error occurred', 'error');
        } finally {
            utils.showLoading(false);
        }
    }



    async makeTranslationRequestWithRetry(data, progressBar, maxRetries = 2) {
        let lastError = null;

        for (let attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                console.log(`Translation attempt ${attempt}/${maxRetries}`);

                if (progressBar && attempt > 1) {
                    progressBar.setProgress(10, `Retrying translation (attempt ${attempt})...`);
                }

                const result = await utils.makeTranslationRequest('api/chapters.php', data, progressBar);

                // If successful, return immediately
                if (result.success) {
                    if (attempt > 1) {
                        console.log(`Translation succeeded on attempt ${attempt}`);
                    }
                    return result;
                }

                // If not successful but not the last attempt, prepare for retry
                if (attempt < maxRetries) {
                    lastError = new Error(result.error || 'Translation failed');
                    console.warn(`Translation attempt ${attempt} failed: ${result.error}. Retrying...`);

                    // Wait before retrying (exponential backoff)
                    const delay = Math.min(2000 * Math.pow(2, attempt - 1), 10000); // Max 10 seconds
                    await new Promise(resolve => setTimeout(resolve, delay));

                    if (progressBar) {
                        progressBar.setProgress(5, 'Preparing to retry...');
                    }
                } else {
                    // Last attempt failed
                    return result;
                }

            } catch (error) {
                lastError = error;
                console.error(`Translation attempt ${attempt} error:`, error);

                // Check if this is a retryable error
                const isRetryable = attempt < maxRetries && (
                    error.message.includes('timed out') ||
                    error.message.includes('503') ||
                    error.message.includes('Service Unavailable') ||
                    error.message.includes('overloaded') ||
                    error.message.includes('temporarily unavailable')
                );

                if (isRetryable) {
                    console.log(`Retryable error on attempt ${attempt}, retrying...`);

                    // Determine delay based on error type
                    let delay;
                    if (error.message.includes('503') || error.message.includes('overloaded')) {
                        delay = Math.min(5000 * attempt, 15000); // Longer delay for overload
                    } else if (error.message.includes('timed out')) {
                        delay = Math.min(3000 * attempt, 10000); // Standard delay for timeouts
                    } else {
                        delay = Math.min(2000 * attempt, 8000); // Shorter delay for other errors
                    }

                    if (progressBar) {
                        progressBar.setProgress(5, `Service temporarily busy, retrying in ${delay/1000}s...`);
                    }

                    await new Promise(resolve => setTimeout(resolve, delay));
                } else {
                    // Non-retryable error or last attempt - don't retry
                    throw error;
                }
            }
        }

        // If we get here, all attempts failed
        throw lastError || new Error('All translation attempts failed');
    }

    async refreshChapters() {
        await this.loadNovelDetails();
    }

    async checkForNewChapters() {
        const supportedPlatforms = ['kakuyomu', 'syosetu', 'shuba69', 'dxmwx'];
        if (!supportedPlatforms.includes(this.novel.platform)) {
            utils.showToast('Chapter checking is not supported for this platform', 'warning');
            return;
        }

        utils.showLoading(true);

        try {
            const result = await utils.makeApiRequest(`api/check-new-chapters.php?novel_id=${this.novelId}`);

            if (result.success) {
                this.displayNewChaptersResult(result);
            } else {
                if (result.source_available === false) {
                    utils.showToast('Source website is temporarily unavailable. Please try again later.', 'warning');
                } else {
                    utils.showToast(result.error || 'Failed to check for new chapters', 'error');
                }
            }
        } catch (error) {
            console.error('Check new chapters error:', error);
            utils.showToast('Network error occurred while checking for new chapters', 'error');
        } finally {
            utils.showLoading(false);
        }
    }

    displayNewChaptersResult(result) {
        const modalHtml = `
            <div class="modal fade" id="newChaptersModal" tabindex="-1" aria-labelledby="newChaptersModalLabel" aria-hidden="true">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="newChaptersModalLabel">
                                <i class="fas fa-search me-2"></i>
                                New Chapters Check Results
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <div class="mb-3">
                                <h6><i class="fas fa-info-circle me-2"></i>Check Summary</h6>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="stat-item">
                                            <span class="stat-label">Current Chapters:</span>
                                            <span class="stat-value">${result.current_chapters.count}</span>
                                        </div>
                                        <div class="stat-item">
                                            <span class="stat-label">Latest Available:</span>
                                            <span class="stat-value">${result.latest_chapters.count}</span>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="stat-item">
                                            <span class="stat-label">New Chapters:</span>
                                            <span class="stat-value ${result.new_chapters_available ? 'text-success' : 'text-muted'}">
                                                ${result.new_chapter_count}
                                            </span>
                                        </div>
                                        <div class="stat-item">
                                            <span class="stat-label">Checked:</span>
                                            <span class="stat-value">${result.check_timestamp}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            ${result.new_chapters_available ? `
                                <div class="mb-3">
                                    <h6><i class="fas fa-list me-2"></i>New Chapters Available</h6>
                                    <div class="new-chapters-list" style="max-height: 300px; overflow-y: auto;">
                                        ${result.new_chapters.map(chapter => `
                                            <div class="new-chapter-item">
                                                <div class="chapter-number">Ch. ${chapter.chapter_number}</div>
                                                <div class="chapter-title">${utils.escapeHtml(chapter.original_title)}</div>
                                            </div>
                                        `).join('')}
                                        ${result.total_new_chapters > result.new_chapters.length ? `
                                            <div class="text-muted text-center mt-2">
                                                <small>And ${result.total_new_chapters - result.new_chapters.length} more chapters...</small>
                                            </div>
                                        ` : ''}
                                    </div>
                                </div>
                            ` : `
                                <div class="text-center text-muted">
                                    <i class="fas fa-check-circle fa-2x mb-3"></i>
                                    <p>No new chapters available. You're up to date!</p>
                                </div>
                            `}
                        </div>
                        <div class="modal-footer">
                            ${result.new_chapters_available ? `
                                <button type="button" class="btn btn-success" onclick="novelDetails.fetchNewChapters(${result.total_new_chapters})">
                                    <i class="fas fa-download me-1"></i>
                                    Fetch All New Chapters (${result.total_new_chapters})
                                </button>
                            ` : ''}
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Remove existing modal if any
        const existingModal = document.getElementById('newChaptersModal');
        if (existingModal) {
            existingModal.remove();
        }

        // Add modal to page
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // Show modal
        const modal = new bootstrap.Modal(document.getElementById('newChaptersModal'));
        modal.show();

        // Clean up modal when hidden
        document.getElementById('newChaptersModal').addEventListener('hidden.bs.modal', function () {
            this.remove();
        });
    }

    async fetchNewChapters(totalNewChapters) {
        const confirmed = confirm(`Fetch ${totalNewChapters} new chapters? This may take a few minutes.`);
        if (!confirmed) return;

        // Close the modal
        const modal = bootstrap.Modal.getInstance(document.getElementById('newChaptersModal'));
        if (modal) {
            modal.hide();
        }

        utils.showLoading(true);

        try {
            const result = await utils.makeApiRequest('api/fetch-new-chapters.php', {
                method: 'POST',
                body: JSON.stringify({
                    novel_id: this.novelId,
                    fetch_all: true
                })
            });

            if (result.success) {
                let message = result.message;

                // Add additional information if available
                if (result.chapters_added > 0) {
                    message += ` (${result.chapters_added} chapters added)`;
                }

                utils.showToast(message, 'success');

                // If there are more chapters available, show an additional message
                if (result.has_more_chapters && result.remaining_chapters > 0) {
                    setTimeout(() => {
                        utils.showToast(`${result.remaining_chapters} more chapters available. Click "Check for New Chapters" again to fetch more.`, 'info');
                    }, 2000);
                }

                // Refresh the chapter list to show new chapters
                await this.loadNovelDetails();
            } else {
                utils.showToast(result.error || 'Failed to fetch new chapters', 'error');
            }
        } catch (error) {
            console.error('Fetch new chapters error:', error);
            utils.showToast('Network error occurred while fetching new chapters', 'error');
        } finally {
            utils.showLoading(false);
        }
    }

    async showChunkInterface(chapterId, chapterNumber) {
        // Clear any previous chunk POV preference when opening a new chunk interface
        this.clearChunkPOVPreference();

        try {
            utils.showLoading(true);

            // Get chunk data for this chapter
            const result = await utils.makeApiRequest(`api/chapter-chunks.php?chapter_id=${chapterId}`);

            if (result.success) {
                this.displayChunkInterface(result.data, chapterNumber);
            } else {
                utils.showToast(result.error || 'Failed to load chunk data', 'error');
            }
        } catch (error) {
            console.error('Load chunks error:', error);
            utils.showToast('Network error occurred', 'error');
        } finally {
            utils.showLoading(false);
        }
    }

    displayChunkInterface(data, chapterNumber) {
        const { chapter, chunks, progress } = data;

        const modalHtml = `
            <div class="modal fade" id="chunkModal" tabindex="-1" aria-labelledby="chunkModalLabel" aria-hidden="true" data-chapter-id="${chapter.id}">
                <div class="modal-dialog modal-xl">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="chunkModalLabel">
                                <i class="fas fa-puzzle-piece me-2"></i>
                                Chapter ${chapterNumber} - Translation Parts
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <!-- Progress Container for Individual Chunk Translation -->
                            <div id="chunk-progress-container" style="display: none;">
                                <div class="alert alert-info">
                                    <div class="d-flex align-items-center">
                                        <div class="spinner-border spinner-border-sm me-3" role="status">
                                            <span class="visually-hidden">Loading...</span>
                                        </div>
                                        <div class="flex-grow-1">
                                            <strong>Translating Part <span id="current-chunk-number">1</span>...</strong>
                                            <div class="progress mt-2" style="height: 8px;">
                                                <div id="chunk-progress-bar" class="progress-bar progress-bar-striped progress-bar-animated"
                                                     role="progressbar" style="width: 0%" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">
                                                </div>
                                            </div>
                                            <small id="chunk-progress-status" class="text-muted">Initializing translation...</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- Progress Overview -->
                            <div class="mb-4">
                                <h6><i class="fas fa-chart-line me-2"></i>Translation Progress</h6>
                                <div class="progress mb-2" style="height: 20px;">
                                    <div class="progress-bar bg-success" role="progressbar"
                                         style="width: ${progress.percentage}%"
                                         aria-valuenow="${progress.percentage}"
                                         aria-valuemin="0"
                                         aria-valuemax="100">
                                        ${progress.percentage}%
                                    </div>
                                </div>
                                <div class="row text-center">
                                    <div class="col">
                                        <div class="stat-item">
                                            <span class="stat-value text-success">${progress.completed}</span>
                                            <span class="stat-label">Completed</span>
                                        </div>
                                    </div>
                                    <div class="col">
                                        <div class="stat-item">
                                            <span class="stat-value text-warning">${progress.translating}</span>
                                            <span class="stat-label">Translating</span>
                                        </div>
                                    </div>
                                    <div class="col">
                                        <div class="stat-item">
                                            <span class="stat-value text-danger">${progress.error}</span>
                                            <span class="stat-label">Error</span>
                                        </div>
                                    </div>
                                    <div class="col">
                                        <div class="stat-item">
                                            <span class="stat-value text-muted">${progress.pending}</span>
                                            <span class="stat-label">Pending</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Bulk Actions -->
                            <div class="mb-3">
                                <div class="btn-group" role="group">
                                    <button class="btn btn-success btn-sm" onclick="novelDetails.translateAllChunks(${chapter.id})"
                                            ${progress.translating > 0 ? 'disabled' : ''}>
                                        <i class="fas fa-language me-1"></i>
                                        Translate All Pending
                                    </button>
                                    <button class="btn btn-warning btn-sm" onclick="novelDetails.retranslateAllChunks(${chapter.id})"
                                            ${progress.translating > 0 ? 'disabled' : ''}>
                                        <i class="fas fa-redo me-1"></i>
                                        Re-translate All
                                    </button>
                                    <button class="btn btn-outline-danger btn-sm" onclick="novelDetails.clearAllChunks(${chapter.id})">
                                        <i class="fas fa-trash me-1"></i>
                                        Clear All
                                    </button>
                                </div>
                            </div>

                            <!-- Chunk List -->
                            <div class="chunk-list" style="max-height: 500px; overflow-y: auto;">
                                ${this.renderChunkList(chunks, chapter.id)}
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Remove existing modal if any
        const existingModal = document.getElementById('chunkModal');
        if (existingModal) {
            existingModal.remove();
        }

        // Add modal to page
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // Show modal
        const modal = new bootstrap.Modal(document.getElementById('chunkModal'));
        modal.show();

        // Clean up modal when hidden
        document.getElementById('chunkModal').addEventListener('hidden.bs.modal', () => {
            // Clear auto-refresh when modal is closed
            this.clearAutoRefresh();
            // Remove modal from DOM
            document.getElementById('chunkModal').remove();
        });
    }

    renderChunkList(chunks, chapterId) {
        if (chunks.length === 0) {
            return `
                <div class="text-center text-muted p-4">
                    <i class="fas fa-info-circle fa-2x mb-3"></i>
                    <p>No chunks found for this chapter</p>
                </div>
            `;
        }

        return chunks.map(chunk => `
            <div class="chunk-item border rounded p-3 mb-3" data-chunk-id="${chunk.id}">
                <div class="d-flex justify-content-between align-items-start mb-2">
                    <div class="chunk-header">
                        <h6 class="mb-1">
                            <i class="fas fa-puzzle-piece me-2"></i>
                            Part ${chunk.chunk_number}
                            <span class="badge badge-${this.getChunkStatusColor(chunk.translation_status)} ms-2">
                                ${this.getChunkStatusText(chunk.translation_status)}
                            </span>
                        </h6>
                        <small class="text-muted">
                            ${chunk.character_count} characters | ${chunk.word_count} words
                            ${chunk.translation_date ? ` | Translated: ${utils.formatDate(chunk.translation_date)}` : ''}
                        </small>
                    </div>
                    <div class="chunk-actions">
                        ${this.renderChunkActions(chunk, chapterId)}
                    </div>
                </div>

                <!-- Chunk Content Preview -->
                <div class="chunk-content">
                    <div class="row">
                        <div class="col-md-6">
                            <label class="form-label-sm"><strong>Original:</strong></label>
                            <div class="content-preview border rounded p-2 bg-light" style="max-height: 150px; overflow-y: auto;">
                                ${utils.escapeHtml(chunk.original_content.substring(0, 300))}${chunk.original_content.length > 300 ? '...' : ''}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label-sm"><strong>Translation:</strong></label>
                            <div class="content-preview border rounded p-2 ${chunk.translated_content ? 'bg-success-subtle' : 'bg-light'}" style="max-height: 150px; overflow-y: auto;">
                                ${chunk.translated_content ?
                                    utils.escapeHtml(chunk.translated_content.substring(0, 300)) + (chunk.translated_content.length > 300 ? '...' : '') :
                                    '<em class="text-muted">Not translated yet</em>'
                                }
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `).join('');
    }

    renderChunkActions(chunk, chapterId) {
        let actions = [];

        if (chunk.translation_status === 'pending' || chunk.translation_status === 'error') {
            actions.push(`
                <button class="btn btn-success btn-sm"
                        onclick="novelDetails.translateChunk(${chunk.id}, ${chunk.chunk_number})"
                        title="Translate this part">
                    <i class="fas fa-language me-1"></i>
                    Translate
                </button>
            `);
        }

        if (chunk.translation_status === 'completed') {
            actions.push(`
                <button class="btn btn-warning btn-sm"
                        onclick="novelDetails.retranslateChunk(${chunk.id}, ${chunk.chunk_number})"
                        title="Re-translate this part">
                    <i class="fas fa-redo me-1"></i>
                    Re-translate
                </button>
            `);
        }

        if (chunk.translation_status === 'completed') {
            actions.push(`
                <button class="btn btn-outline-danger btn-sm"
                        onclick="novelDetails.clearChunk(${chunk.id}, ${chunk.chunk_number})"
                        title="Clear translation for this part">
                    <i class="fas fa-trash me-1"></i>
                    Clear
                </button>
            `);
        }

        if (chunk.translation_status === 'translating') {
            actions.push(`
                <button class="btn btn-secondary btn-sm" disabled>
                    <i class="fas fa-spinner fa-spin me-1"></i>
                    Translating...
                </button>
            `);
        }

        return actions.join(' ');
    }

    getChunkStatusColor(status) {
        switch (status) {
            case 'completed': return 'success';
            case 'translating': return 'warning';
            case 'error': return 'danger';
            case 'pending':
            default: return 'secondary';
        }
    }

    getChunkStatusText(status) {
        switch (status) {
            case 'completed': return 'Completed';
            case 'translating': return 'Translating';
            case 'error': return 'Error';
            case 'pending':
            default: return 'Pending';
        }
    }

    async translateChunk(chunkId, chunkNumber) {
        // Check if we need to show POV selection modal for this chunk session
        if (!this.currentChunkPOVPreference) {
            const povSelection = await this.showPOVSelectionModal(chunkId, 'chunk');
            if (!povSelection) {
                return; // User cancelled
            }
            // Store POV preference for this chunk session
            this.currentChunkPOVPreference = povSelection.perspective;
        }

        const button = document.querySelector(`[data-chunk-id="${chunkId}"] .btn-success`);
        const originalText = button ? button.innerHTML : '';

        // Declare startTime outside try block so it's accessible in catch block
        let startTime = Date.now();

        // Show progress container
        this.showChunkProgress(chunkNumber);

        try {
            // Update button state
            if (button) {
                button.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Translating...';
                button.disabled = true;
            }

            // Update chunk status in the interface
            this.updateChunkStatus(chunkId, 'translating');

            console.log(`[ChunkTranslation] Starting translation for chunk ${chunkId} (Part ${chunkNumber})`);
            startTime = Date.now(); // Update the start time

            // Update progress
            this.updateChunkProgress(10, 'Sending translation request...');

            const requestBody = {
                chunk_id: chunkId,
                target_language: 'en'
            };

            // Add POV preference if available
            if (this.currentChunkPOVPreference) {
                requestBody.pov_preference = this.currentChunkPOVPreference;
            }

            const result = await utils.makeApiRequest('api/chapter-chunks.php', {
                method: 'PUT',
                body: JSON.stringify(requestBody)
            }, true); // Mark as translation request for longer timeout

            const duration = ((Date.now() - startTime) / 1000).toFixed(1);
            console.log(`[ChunkTranslation] Chunk ${chunkId} translation completed in ${duration}s`);

            // Complete progress
            this.updateChunkProgress(100, 'Translation completed!');

            if (result.success) {
                console.log(`[ChunkTranslation] Success - Part ${chunkNumber} translated (${result.character_count || 'unknown'} characters)`);

                // Show success toast with detailed info
                const successMessage = `Part ${chunkNumber} translated successfully! (${duration}s, ${result.character_count || 'unknown'} characters)`;
                utils.showToast(successMessage, 'success');

                // Update chunk status
                this.updateChunkStatus(chunkId, 'completed');

                // Hide progress after a short delay
                setTimeout(() => {
                    this.hideChunkProgress();
                }, 2000);

                // Refresh the chunk interface
                const modal = document.getElementById('chunkModal');
                const chapterId = modal.dataset.chapterId || this.getCurrentChapterId();
                if (chapterId) {
                    console.log(`[ChunkTranslation] Refreshing interface for chapter ${chapterId}`);
                    await this.refreshChunkInterface(chapterId);
                }
            } else {
                console.error(`[ChunkTranslation] Chunk ${chunkId} translation failed:`, result.error);

                // Show error toast
                const errorMessage = result.error || 'Failed to translate chunk';
                utils.showToast(`Part ${chunkNumber} translation failed: ${errorMessage}`, 'error');

                // Update chunk status to error
                this.updateChunkStatus(chunkId, 'error');

                // Hide progress
                this.hideChunkProgress();

                // Restore button state
                if (button) {
                    button.innerHTML = originalText;
                    button.disabled = false;
                }
            }
        } catch (error) {
            const duration = ((Date.now() - startTime) / 1000).toFixed(1);
            console.error(`[ChunkTranslation] Translate chunk ${chunkId} error after ${duration}s:`, error);

            // Hide progress
            this.hideChunkProgress();

            // Check if it's a timeout error
            if (error.message && error.message.includes('timed out')) {
                const timeoutMessage = `Part ${chunkNumber} translation timed out after ${duration}s. Checking if translation completed in background...`;
                utils.showToast(timeoutMessage, 'warning');
                console.warn(`[ChunkTranslation] Timeout occurred - ${timeoutMessage}`);

                // Wait a moment then check if the translation actually completed
                setTimeout(async () => {
                    await this.checkChunkStatusAfterTimeout(chunkId, chunkNumber, button, originalText);
                }, 3000); // Wait 3 seconds before checking

                return; // Don't immediately set to error state
            } else {
                const networkMessage = `Network error occurred while translating Part ${chunkNumber}: ${error.message}`;
                utils.showToast(networkMessage, 'error');
                console.error(`[ChunkTranslation] Network error - ${networkMessage}`);
            }

            // Update chunk status to error (only for non-timeout errors)
            this.updateChunkStatus(chunkId, 'error');

            // Restore button state
            if (button) {
                button.innerHTML = originalText;
                button.disabled = false;
            }
        }
    }

    /**
     * Check chunk status after a frontend timeout to see if translation actually completed
     */
    async checkChunkStatusAfterTimeout(chunkId, chunkNumber, button, originalText) {
        try {
            console.log(`[ChunkTranslation] Checking post-timeout status for chunk ${chunkId}`);

            // Get current chunk status from server
            const modal = document.getElementById('chunkModal');
            const chapterId = modal.dataset.chapterId || this.getCurrentChapterId();

            if (!chapterId) {
                console.warn('[ChunkTranslation] No chapter ID available for status check');
                this.handleTimeoutFailure(chunkId, chunkNumber, button, originalText);
                return;
            }

            const result = await utils.makeApiRequest(`api/chapter-chunks.php?chapter_id=${chapterId}`);

            if (result.success) {
                const chunk = result.data.chunks.find(c => c.id == chunkId);

                if (chunk && chunk.translation_status === 'completed') {
                    // Translation actually completed!
                    console.log(`[ChunkTranslation] Success - Part ${chunkNumber} completed despite timeout`);
                    utils.showToast(`Part ${chunkNumber} completed successfully! (Translation finished in background)`, 'success');

                    // Refresh the interface to show the completed translation
                    await this.refreshChunkInterface(chapterId);

                    return; // Success, no need to set error state
                } else if (chunk && chunk.translation_status === 'translating') {
                    // Still translating, check again in a bit
                    console.log(`[ChunkTranslation] Part ${chunkNumber} still translating, checking again...`);
                    utils.showToast(`Part ${chunkNumber} still processing in background...`, 'info');

                    setTimeout(async () => {
                        await this.checkChunkStatusAfterTimeout(chunkId, chunkNumber, button, originalText);
                    }, 10000); // Check again in 10 seconds

                    return;
                }
            }

            // If we get here, the translation truly failed
            this.handleTimeoutFailure(chunkId, chunkNumber, button, originalText);

        } catch (error) {
            console.error('[ChunkTranslation] Error checking post-timeout status:', error);
            this.handleTimeoutFailure(chunkId, chunkNumber, button, originalText);
        }
    }

    /**
     * Handle confirmed timeout failure
     */
    handleTimeoutFailure(chunkId, chunkNumber, button, originalText) {
        console.log(`[ChunkTranslation] Confirmed timeout failure for chunk ${chunkId}`);
        utils.showToast(`Part ${chunkNumber} translation failed due to timeout. Please try again.`, 'error');

        // Update chunk status to error
        this.updateChunkStatus(chunkId, 'error');

        // Restore button state
        if (button) {
            button.innerHTML = originalText;
            button.disabled = false;
        }
    }

    showChunkProgress(chunkNumber) {
        const progressContainer = document.getElementById('chunk-progress-container');
        const chunkNumberSpan = document.getElementById('current-chunk-number');

        if (progressContainer) {
            progressContainer.style.display = 'block';
            if (chunkNumberSpan) {
                chunkNumberSpan.textContent = chunkNumber;
            }
            this.updateChunkProgress(0, 'Initializing translation...');
            console.log(`[ChunkProgress] Showing progress for chunk ${chunkNumber}`);
        }
    }

    hideChunkProgress() {
        const progressContainer = document.getElementById('chunk-progress-container');
        if (progressContainer) {
            progressContainer.style.display = 'none';
            console.log(`[ChunkProgress] Hiding progress container`);
        }
    }

    updateChunkProgress(percentage, status) {
        const progressBar = document.getElementById('chunk-progress-bar');
        const progressStatus = document.getElementById('chunk-progress-status');

        if (progressBar) {
            progressBar.style.width = `${percentage}%`;
            progressBar.setAttribute('aria-valuenow', percentage);
        }

        if (progressStatus && status) {
            progressStatus.textContent = status;
        }

        console.log(`[ChunkProgress] Updated to ${percentage}% - ${status}`);
    }

    updateChunkStatus(chunkId, status) {
        const chunkItem = document.querySelector(`[data-chunk-id="${chunkId}"]`);
        if (!chunkItem) return;

        const badge = chunkItem.querySelector('.badge');
        if (badge) {
            // Remove old status classes
            badge.classList.remove('bg-success', 'bg-warning', 'bg-danger', 'bg-secondary');
            badge.classList.remove('text-dark');

            // Add new status class and text
            switch (status) {
                case 'completed':
                    badge.classList.add('bg-success');
                    badge.textContent = 'Completed';
                    break;
                case 'translating':
                    badge.classList.add('bg-warning', 'text-dark');
                    badge.textContent = 'Translating';
                    break;
                case 'error':
                    badge.classList.add('bg-danger');
                    badge.textContent = 'Error';
                    break;
                case 'pending':
                default:
                    badge.classList.add('bg-secondary');
                    badge.textContent = 'Pending';
                    break;
            }
        }
    }

    async retranslateChunk(chunkId, chunkNumber) {
        const confirmed = confirm(`Re-translate part ${chunkNumber}? This will replace the existing translation.`);
        if (!confirmed) return;

        // Clear the chunk first, then translate
        await this.clearChunk(chunkId, chunkNumber, false);
        await this.translateChunk(chunkId, chunkNumber);
    }

    async clearChunk(chunkId, chunkNumber, showConfirm = true) {
        if (showConfirm) {
            const confirmed = confirm(`Clear translation for part ${chunkNumber}? This action cannot be undone.`);
            if (!confirmed) return;
        }

        try {
            const result = await utils.makeApiRequest('api/chapter-chunks.php', {
                method: 'DELETE',
                body: JSON.stringify({
                    chapter_id: this.getCurrentChapterId(),
                    chunk_ids: [chunkId]
                })
            });

            if (result.success) {
                if (showConfirm) {
                    utils.showToast(`Part ${chunkNumber} translation cleared`, 'success');
                }
                // Refresh the chunk interface
                const chapterId = this.getCurrentChapterId();
                if (chapterId) {
                    await this.refreshChunkInterface(chapterId);
                }
            } else {
                utils.showToast(result.error || 'Failed to clear chunk', 'error');
            }
        } catch (error) {
            console.error('Clear chunk error:', error);
            utils.showToast('Network error occurred', 'error');
        }
    }

    getCurrentChapterId() {
        // Try to get chapter ID from modal or other context
        const modal = document.getElementById('chunkModal');
        if (modal && modal.dataset.chapterId) {
            const chapterId = parseInt(modal.dataset.chapterId);
            console.log(`[ChunkInterface] Got chapter ID from modal: ${chapterId}`);
            return chapterId;
        }

        // Fallback: try to extract from chunk data
        const chunkItem = document.querySelector('.chunk-item');
        if (chunkItem) {
            const chapterId = parseInt(chunkItem.dataset.chapterId);
            console.log(`[ChunkInterface] Got chapter ID from chunk item: ${chapterId}`);
            return chapterId;
        }

        console.warn(`[ChunkInterface] Could not determine chapter ID`);
        return null;
    }

    async refreshChunkInterface(chapterId) {
        try {
            console.log(`[ChunkInterface] Refreshing interface for chapter ${chapterId}`);
            const result = await utils.makeApiRequest(`api/chapter-chunks.php?chapter_id=${chapterId}`);

            if (result.success) {
                console.log(`[ChunkInterface] Received updated data:`, result.data.progress);

                // Update the chunk list in the modal
                const chunkListContainer = document.querySelector('.chunk-list');
                if (chunkListContainer) {
                    chunkListContainer.innerHTML = this.renderChunkList(result.data.chunks, chapterId);
                    console.log(`[ChunkInterface] Updated chunk list with ${result.data.chunks.length} chunks`);
                } else {
                    console.warn(`[ChunkInterface] Chunk list container not found`);
                }

                // Update progress bar
                const progress = result.data.progress;
                const progressBar = document.querySelector('.progress-bar');
                if (progressBar) {
                    progressBar.style.width = `${progress.percentage}%`;
                    progressBar.setAttribute('aria-valuenow', progress.percentage);
                    progressBar.textContent = `${progress.percentage}%`;
                    console.log(`[ChunkInterface] Updated progress bar to ${progress.percentage}%`);
                } else {
                    console.warn(`[ChunkInterface] Progress bar not found`);
                }

                // Update stats
                const stats = document.querySelectorAll('.stat-value');
                if (stats.length >= 4) {
                    stats[0].textContent = progress.completed;
                    stats[1].textContent = progress.translating;
                    stats[2].textContent = progress.error;
                    stats[3].textContent = progress.pending;
                    console.log(`[ChunkInterface] Updated stats: ${progress.completed}/${progress.total} completed`);
                } else {
                    console.warn(`[ChunkInterface] Stats elements not found (found ${stats.length})`);
                }

                // Show success feedback
                console.log(`[ChunkInterface] Interface refresh completed successfully`);

                // Check if there are any chunks still translating and set up auto-refresh
                const translatingChunks = result.data.chunks.filter(chunk => chunk.translation_status === 'translating');
                if (translatingChunks.length > 0) {
                    console.log(`[ChunkInterface] ${translatingChunks.length} chunks still translating, setting up auto-refresh`);
                    this.setupAutoRefresh(chapterId);
                } else {
                    this.clearAutoRefresh();
                }
            } else {
                console.error(`[ChunkInterface] Failed to refresh: ${result.error}`);
                utils.showToast('Failed to refresh chunk interface', 'warning');
            }
        } catch (error) {
            console.error('[ChunkInterface] Refresh chunk interface error:', error);
            utils.showToast('Error refreshing chunk interface', 'error');
        }
    }

    /**
     * Set up automatic refresh for chunks that are still translating
     */
    setupAutoRefresh(chapterId) {
        // Clear any existing auto-refresh
        this.clearAutoRefresh();

        // Set up new auto-refresh every 15 seconds
        this.autoRefreshInterval = setInterval(async () => {
            try {
                console.log('[ChunkInterface] Auto-refreshing to check translation progress');
                await this.refreshChunkInterface(chapterId);
            } catch (error) {
                console.error('[ChunkInterface] Auto-refresh error:', error);
                this.clearAutoRefresh(); // Stop auto-refresh on error
            }
        }, 15000); // Refresh every 15 seconds

        console.log('[ChunkInterface] Auto-refresh enabled (15s interval)');
    }

    /**
     * Clear automatic refresh
     */
    clearAutoRefresh() {
        if (this.autoRefreshInterval) {
            clearInterval(this.autoRefreshInterval);
            this.autoRefreshInterval = null;
            console.log('[ChunkInterface] Auto-refresh disabled');
        }
    }

    async translateAllChunks(chapterId) {
        const confirmed = confirm('Translate all pending chunks? This may take several minutes.');
        if (!confirmed) return;

        // Show POV selection modal before bulk chunk translation
        const povSelection = await this.showPOVSelectionModal(chapterId, 'chunks');
        if (!povSelection) {
            return; // User cancelled
        }

        try {
            utils.showLoading(true);

            // Get current chunk data to find pending chunks
            const result = await utils.makeApiRequest(`api/chapter-chunks.php?chapter_id=${chapterId}`);

            if (!result.success) {
                utils.showToast('Failed to load chunk data', 'error');
                return;
            }

            const pendingChunks = result.data.chunks.filter(chunk =>
                chunk.translation_status === 'pending' || chunk.translation_status === 'error'
            );

            if (pendingChunks.length === 0) {
                utils.showToast('No pending chunks to translate', 'info');
                return;
            }

            let successCount = 0;
            let errorCount = 0;

            // Translate chunks one by one to avoid timeouts
            for (const chunk of pendingChunks) {
                try {
                    const requestBody = {
                        chunk_id: chunk.id,
                        target_language: 'en'
                    };

                    // Add POV preference if selected
                    if (povSelection.perspective) {
                        requestBody.pov_preference = povSelection.perspective;
                    }

                    const translateResult = await utils.makeApiRequest('api/chapter-chunks.php', {
                        method: 'PUT',
                        body: JSON.stringify(requestBody)
                    }, true); // Mark as translation request for longer timeout

                    if (translateResult.success) {
                        successCount++;
                        utils.showToast(`Part ${chunk.chunk_number} completed`, 'success');
                    } else {
                        errorCount++;
                        console.error(`Failed to translate chunk ${chunk.chunk_number}:`, translateResult.error);
                    }

                    // Refresh interface after each chunk
                    await this.refreshChunkInterface(chapterId);

                    // Small delay between chunks to prevent overwhelming the API
                    await new Promise(resolve => setTimeout(resolve, 1000));

                } catch (error) {
                    errorCount++;
                    console.error(`Error translating chunk ${chunk.chunk_number}:`, error);
                }
            }

            const message = errorCount === 0
                ? `All ${successCount} chunks translated successfully`
                : `${successCount} chunks translated, ${errorCount} failed`;

            utils.showToast(message, errorCount === 0 ? 'success' : 'warning');

        } catch (error) {
            console.error('Translate all chunks error:', error);
            utils.showToast('Network error occurred', 'error');
        } finally {
            utils.showLoading(false);
        }
    }

    async retranslateAllChunks(chapterId) {
        const confirmed = confirm('Re-translate ALL chunks? This will replace all existing translations and may take several minutes.');
        if (!confirmed) return;

        try {
            // First clear all chunks
            await this.clearAllChunks(chapterId, false);

            // Then translate all chunks
            await this.translateAllChunks(chapterId);

        } catch (error) {
            console.error('Retranslate all chunks error:', error);
            utils.showToast('Network error occurred', 'error');
        }
    }

    async clearAllChunks(chapterId, showConfirm = true) {
        if (showConfirm) {
            const confirmed = confirm('Clear ALL chunk translations? This action cannot be undone.');
            if (!confirmed) return;
        }

        try {
            const result = await utils.makeApiRequest('api/chapter-chunks.php', {
                method: 'DELETE',
                body: JSON.stringify({
                    chapter_id: chapterId
                })
            });

            if (result.success) {
                if (showConfirm) {
                    utils.showToast('All chunk translations cleared', 'success');
                }
                await this.refreshChunkInterface(chapterId);
            } else {
                utils.showToast(result.error || 'Failed to clear chunks', 'error');
            }
        } catch (error) {
            console.error('Clear all chunks error:', error);
            utils.showToast('Network error occurred', 'error');
        }
    }

    displayError(message) {
        document.getElementById('novel-details-content').innerHTML = `
            <div class="text-center text-danger">
                <i class="fas fa-exclamation-triangle fa-2x mb-3"></i>
                <p>${message}</p>
                <button class="btn btn-primary" onclick="novelDetails.loadNovelDetails()">
                    <i class="fas fa-sync-alt me-1"></i>
                    Retry
                </button>
            </div>
        `;
    }

    // Description editing methods
    editDescription() {
        const form = document.getElementById('edit-description-form');
        const textarea = document.getElementById('original-description-input');

        if (form && textarea) {
            form.style.display = 'block';
            textarea.focus();
        }
    }

    cancelEditDescription() {
        const form = document.getElementById('edit-description-form');
        if (form) {
            form.style.display = 'none';
        }
    }

    async saveDescription() {
        const textarea = document.getElementById('original-description-input');
        if (!textarea) return;

        const originalSynopsis = textarea.value.trim();

        utils.showLoading(true);

        try {
            const result = await utils.makeApiRequest('api/novels.php', {
                method: 'PUT',
                body: JSON.stringify({
                    novel_id: this.novelId,
                    original_synopsis: originalSynopsis
                })
            });

            if (result.success) {
                utils.showToast('Description saved successfully', 'success');
                this.novel.original_synopsis = originalSynopsis;
                this.cancelEditDescription();
                this.displayNovelDetails(); // Refresh the display
            } else {
                utils.showToast(result.error || 'Failed to save description', 'error');
            }
        } catch (error) {
            console.error('Save description error:', error);
            utils.showToast('Network error occurred', 'error');
        } finally {
            utils.showLoading(false);
        }
    }

    async saveAndTranslateDescription() {
        const textarea = document.getElementById('original-description-input');
        if (!textarea) return;

        const originalSynopsis = textarea.value.trim();

        if (!originalSynopsis) {
            utils.showToast('Please enter a description before translating', 'warning');
            return;
        }

        utils.showLoading(true);

        try {
            // First save the description
            const saveResult = await utils.makeApiRequest('api/novels.php', {
                method: 'PUT',
                body: JSON.stringify({
                    novel_id: this.novelId,
                    original_synopsis: originalSynopsis
                })
            });

            if (!saveResult.success) {
                throw new Error(saveResult.error || 'Failed to save description');
            }

            // Then translate it
            const translateResult = await utils.makeApiRequest('api/novels.php', {
                method: 'PATCH',
                body: JSON.stringify({
                    novel_id: this.novelId,
                    target_language: 'en'
                })
            });

            if (translateResult.success) {
                utils.showToast('Description saved and translated successfully', 'success');
                this.novel.original_synopsis = originalSynopsis;
                this.novel.translated_synopsis = translateResult.data.data.translated_synopsis;
                this.cancelEditDescription();
                this.displayNovelDetails(); // Refresh the display
            } else {
                utils.showToast('Description saved but translation failed: ' + (translateResult.error || 'Unknown error'), 'warning');
                this.novel.original_synopsis = originalSynopsis;
                this.cancelEditDescription();
                this.displayNovelDetails(); // Refresh the display
            }
        } catch (error) {
            console.error('Save and translate description error:', error);
            utils.showToast('Error: ' + error.message, 'error');
        } finally {
            utils.showLoading(false);
        }
    }

    async translateDescription() {
        if (!this.novel.original_synopsis || !this.novel.original_synopsis.trim()) {
            utils.showToast('No original description to translate. Please add one first.', 'warning');
            return;
        }

        utils.showLoading(true);

        try {
            const result = await utils.makeApiRequest('api/novels.php', {
                method: 'PATCH',
                body: JSON.stringify({
                    novel_id: this.novelId,
                    target_language: 'en'
                })
            });

            if (result.success) {
                utils.showToast('Description translated successfully', 'success');
                this.novel.translated_synopsis = result.data.data.translated_synopsis;
                this.displayNovelDetails(); // Refresh the display
            } else {
                utils.showToast(result.error || 'Failed to translate description', 'error');
            }
        } catch (error) {
            console.error('Translate description error:', error);
            utils.showToast('Network error occurred', 'error');
        } finally {
            utils.showLoading(false);
        }
    }

    // WordPress posting methods
    async showBulkWordPressProfileSelector() {
        const selectedChapters = Array.from(this.selectedChapters);
        const translatedChapters = selectedChapters.filter(chapterNum => {
            const chapter = this.chapters.find(ch => ch.chapter_number === chapterNum);
            return chapter && chapter.translation_status === 'completed';
        });

        if (translatedChapters.length === 0) {
            utils.showToast('No translated chapters selected for WordPress posting', 'warning');
            return;
        }

        try {
            // Load available profiles
            const response = await fetch('api/wordpress-profiles.php?action=list&active_only=true');
            const result = await response.json();

            if (!result.success) {
                utils.showToast('Failed to load WordPress profiles', 'error');
                return;
            }

            const profiles = result.profiles;

            if (profiles.length === 0) {
                utils.showToast('No active WordPress profiles found. Please create a profile in Settings first.', 'warning');
                return;
            }

            // If only one profile, post directly
            if (profiles.length === 1) {
                await this.bulkPostToWordPress(profiles[0].id);
                return;
            }

            // Show profile selection modal
            this.showBulkProfileSelectionModal(profiles, translatedChapters.length);

        } catch (error) {
            console.error('Error loading profiles:', error);
            utils.showToast('Failed to load WordPress profiles', 'error');
        }
    }

    showBulkProfileSelectionModal(profiles, chapterCount) {
        const modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.id = 'bulkWordpressProfileSelectorModal';
        modal.innerHTML = `
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="fab fa-wordpress me-2"></i>
                            Select WordPress Profile
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <p class="text-muted">Choose which WordPress profile to post ${chapterCount} chapters to:</p>
                        <div class="list-group">
                            ${profiles.map(profile => {
                                let hostname;
                                try {
                                    hostname = new URL(profile.site_url).hostname;
                                } catch (e) {
                                    hostname = profile.site_url;
                                }
                                return `
                                    <button type="button" class="list-group-item list-group-item-action"
                                            onclick="novelDetails.bulkPostToWordPress(${profile.id}); bootstrap.Modal.getInstance(document.getElementById('bulkWordpressProfileSelectorModal')).hide();">
                                        <div class="d-flex w-100 justify-content-between">
                                            <h6 class="mb-1">${utils.escapeHtml(profile.profile_name)}</h6>
                                            <small class="text-muted">${utils.escapeHtml(hostname)}</small>
                                        </div>
                                        <small class="text-muted">${utils.escapeHtml(profile.site_url)}</small>
                                    </button>
                                `;
                            }).join('')}
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        const bootstrapModal = new bootstrap.Modal(modal);
        bootstrapModal.show();

        // Clean up when modal is hidden
        modal.addEventListener('hidden.bs.modal', () => {
            document.body.removeChild(modal);
        });
    }

    async bulkPostToWordPress(profileId = null) {
        const selectedChapters = Array.from(this.selectedChapters);
        const translatedChapters = selectedChapters.filter(chapterNum => {
            const chapter = this.chapters.find(ch => ch.chapter_number === chapterNum);
            return chapter && chapter.translation_status === 'completed';
        });

        if (translatedChapters.length === 0) {
            utils.showToast('No translated chapters selected for WordPress posting', 'warning');
            return;
        }

        // Validate profile configuration
        try {
            if (profileId) {
                const profileResponse = await fetch(`api/wordpress-profiles.php?action=get&id=${profileId}`);
                const profileResult = await profileResponse.json();

                if (!profileResult.success) {
                    utils.showToast('WordPress profile not found', 'error');
                    return;
                }
            } else {
                // No profile specified - this shouldn't happen with the new system
                utils.showToast('No WordPress profile selected. Please select a profile to post to.', 'error');
                return;
            }
        } catch (error) {
            utils.showToast('Failed to validate WordPress profile', 'error');
            return;
        }

        const confirmed = confirm(`Post ${translatedChapters.length} translated chapters to WordPress?`);
        if (!confirmed) return;

        // Create progress tracking
        let successCount = 0;
        let errorCount = 0;
        const errors = [];

        utils.showLoading(true);

        try {
            // First ensure the novel is posted to WordPress
            const novelRequestBody = {
                action: 'post_novel',
                novel_id: this.novelId
            };

            // Profile ID is required for the new system
            novelRequestBody.profile_id = profileId;

            const novelResult = await utils.makeApiRequest('api/wordpress.php', {
                method: 'POST',
                body: JSON.stringify(novelRequestBody)
            });

            if (!novelResult.success && !novelResult.error.includes('already posted')) {
                utils.showToast('Failed to post novel to WordPress: ' + novelResult.error, 'error');
                return;
            }

            // Post each chapter
            for (const chapterNum of translatedChapters) {
                const chapter = this.chapters.find(ch => ch.chapter_number === chapterNum);
                if (!chapter) continue;

                try {
                    const chapterRequestBody = {
                        action: 'post_chapter',
                        chapter_id: chapter.id
                    };

                    // Profile ID is required for the new system
                    chapterRequestBody.profile_id = profileId;

                    const result = await utils.makeApiRequest('api/wordpress.php', {
                        method: 'POST',
                        body: JSON.stringify(chapterRequestBody)
                    });

                    if (result.success) {
                        successCount++;
                    } else if (result.error && result.error.includes('already posted')) {
                        successCount++; // Count as success if already posted
                    } else {
                        errorCount++;
                        errors.push(`Chapter ${chapterNum}: ${result.error}`);
                    }
                } catch (error) {
                    errorCount++;
                    errors.push(`Chapter ${chapterNum}: Network error`);
                }
            }

            // Show results
            if (successCount > 0 && errorCount === 0) {
                utils.showToast(`Successfully posted ${successCount} chapters to WordPress!`, 'success');
            } else if (successCount > 0 && errorCount > 0) {
                utils.showToast(`Posted ${successCount} chapters, ${errorCount} failed. Check console for details.`, 'warning');
                console.warn('WordPress posting errors:', errors);
            } else {
                utils.showToast('Failed to post chapters to WordPress', 'error');
                console.error('WordPress posting errors:', errors);
            }

        } catch (error) {
            console.error('Bulk WordPress posting error:', error);
            utils.showToast('Network error occurred during WordPress posting', 'error');
        } finally {
            utils.showLoading(false);
        }
    }

    // Novel WordPress posting methods
    async showNovelWordPressProfileSelector() {
        console.log('showNovelWordPressProfileSelector called');
        try {
            // Load available profiles
            console.log('Loading WordPress profiles...');
            const response = await fetch('api/wordpress-profiles.php?action=list&active_only=true');
            console.log('Profile API response status:', response.status);

            const result = await response.json();
            console.log('Profile API result:', result);

            if (!result.success) {
                console.error('Failed to load WordPress profiles:', result.error);
                utils.showToast('Failed to load WordPress profiles: ' + (result.error || 'Unknown error'), 'error');
                return;
            }

            const profiles = result.profiles;
            console.log('Loaded profiles:', profiles);

            if (profiles.length === 0) {
                console.warn('No active WordPress profiles found');
                utils.showToast('No active WordPress profiles found. Please create a profile in Settings first.', 'warning');
                return;
            }

            // If only one profile, show title customization modal
            if (profiles.length === 1) {
                console.log('Single profile found, showing title customization modal for:', profiles[0]);
                this.showNovelTitleCustomizationModal(profiles[0]);
                return;
            }

            // Show profile selection modal
            console.log('Multiple profiles found, showing selection modal');
            this.showNovelProfileSelectionModal(profiles);

        } catch (error) {
            console.error('Error loading WordPress profiles:', error);
            utils.showToast('Failed to load WordPress profiles: ' + error.message, 'error');
        }
    }

    showNovelProfileSelectionModal(profiles) {
        console.log('showNovelProfileSelectionModal called with profiles:', profiles);

        try {
            const modal = document.createElement('div');
            modal.className = 'modal fade';
            modal.id = 'novelWordpressProfileSelectorModal';

            console.log('Creating modal HTML...');
            modal.innerHTML = `
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="fab fa-wordpress me-2"></i>
                            Select WordPress Profile
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <p class="text-muted">Choose which WordPress profile to post this novel to:</p>
                        <div class="list-group">
                            ${profiles.map(profile => {
                                let hostname;
                                try {
                                    hostname = new URL(profile.site_url).hostname;
                                } catch (e) {
                                    hostname = profile.site_url;
                                }
                                return `
                                    <button type="button" class="list-group-item list-group-item-action"
                                            onclick="novelDetails.showNovelTitleCustomizationModal(${JSON.stringify(profile).replace(/"/g, '&quot;')}); bootstrap.Modal.getInstance(document.getElementById('novelWordpressProfileSelectorModal')).hide();">
                                        <div class="d-flex w-100 justify-content-between">
                                            <h6 class="mb-1">${utils.escapeHtml(profile.profile_name)}</h6>
                                            <small class="text-muted">${utils.escapeHtml(hostname)}</small>
                                        </div>
                                        <small class="text-muted">${utils.escapeHtml(profile.site_url)}</small>
                                    </button>
                                `;
                            }).join('')}
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    </div>
                </div>
            </div>
        `;

            console.log('Modal HTML created, appending to body...');
            document.body.appendChild(modal);

            console.log('Creating Bootstrap modal...');
            const bootstrapModal = new bootstrap.Modal(modal);

            console.log('Showing modal...');
            bootstrapModal.show();

            // Clean up when modal is hidden
            modal.addEventListener('hidden.bs.modal', () => {
                console.log('Modal hidden, cleaning up...');
                document.body.removeChild(modal);
            });

            console.log('Modal setup complete');

        } catch (error) {
            console.error('Error creating profile selection modal:', error);
            utils.showToast('Error creating profile selection modal: ' + error.message, 'error');
        }
    }

    showNovelTitleCustomizationModal(profile) {
        // Generate default title for preview
        const defaultTitle = this.novel.translated_title || this.novel.original_title;

        const modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.id = 'novelWordpressTitleCustomizationModal';
        modal.innerHTML = `
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="fab fa-wordpress me-2"></i>
                            Customize WordPress Post Title
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="mb-3">
                            <p class="text-muted">
                                <strong>Profile:</strong> ${profile.profile_name} (${new URL(profile.site_url).hostname})
                            </p>
                        </div>

                        <div class="mb-3">
                            <label for="customNovelPostTitle" class="form-label">
                                <strong>Post Title</strong>
                            </label>
                            <input type="text" class="form-control" id="customNovelPostTitle"
                                   value="${defaultTitle}" placeholder="Enter custom title or leave default">
                            <div class="form-text">
                                Leave empty to use the default title format, or enter a custom title for this WordPress post.
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Categories</label>
                            <div class="input-group">
                                <select class="form-select" id="novelCategorySelect" multiple>
                                    <option value="">Loading categories...</option>
                                </select>
                                <button class="btn btn-outline-secondary" type="button" id="loadNovelCategories" title="Refresh categories">
                                    <i class="fas fa-sync-alt"></i>
                                </button>
                            </div>
                            <div class="form-text">
                                <small class="text-muted">Hold Ctrl/Cmd to select multiple categories. Leave empty to use profile default.</small>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label"><strong>Preview:</strong></label>
                            <div class="border rounded p-2 bg-light">
                                <small class="text-muted">Default title:</small><br>
                                <span class="text-dark">${defaultTitle}</span>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="button" class="btn btn-success" onclick="novelDetails.postNovelToWordPressWithTitle(${profile.id})">
                            <i class="fab fa-wordpress me-1"></i>
                            Post to WordPress
                        </button>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        const bootstrapModal = new bootstrap.Modal(modal);
        bootstrapModal.show();

        // Load categories for this profile
        this.loadCategoriesForNovelPosting(profile.id);

        // Add event listener for refresh categories button
        const loadCategoriesBtn = document.getElementById('loadNovelCategories');
        if (loadCategoriesBtn) {
            loadCategoriesBtn.addEventListener('click', () => {
                this.loadCategoriesForNovelPosting(profile.id);
            });
        }

        // Clean up when modal is hidden
        modal.addEventListener('hidden.bs.modal', () => {
            document.body.removeChild(modal);
        });
    }

    async postNovelToWordPressWithTitle(profileId) {
        const customTitleInput = document.getElementById('customNovelPostTitle');
        const customTitle = customTitleInput ? customTitleInput.value.trim() : '';

        // Get selected categories
        const categorySelect = document.getElementById('novelCategorySelect');
        const selectedCategories = categorySelect ? Array.from(categorySelect.selectedOptions).map(option => option.value).filter(val => val) : [];

        // Hide the modal
        const modal = bootstrap.Modal.getInstance(document.getElementById('novelWordpressTitleCustomizationModal'));
        if (modal) {
            modal.hide();
        }

        // Post with custom title and categories
        await this.postNovelToWordPress(profileId, customTitle, selectedCategories);
    }

    async postNovelToWordPress(profileId, customTitle = null, categories = null) {
        console.log('postNovelToWordPress called with profileId:', profileId);

        const confirmed = confirm('Post this novel to WordPress?');
        if (!confirmed) {
            console.log('User cancelled novel posting');
            return;
        }

        console.log('Starting novel posting to WordPress...');
        utils.showLoading(true);

        try {
            const requestBody = {
                action: 'post_novel',
                novel_id: this.novelId,
                profile_id: profileId
            };

            // Add custom title if provided
            if (customTitle && customTitle.length > 0) {
                requestBody.custom_title = customTitle;
            }

            // Add categories if provided
            if (categories && categories.length > 0) {
                requestBody.categories = categories;
            }

            console.log('Posting novel with request body:', requestBody);

            const result = await utils.makeApiRequest('api/wordpress.php', {
                method: 'POST',
                body: JSON.stringify(requestBody)
            });

            console.log('Novel posting result:', result);

            if (result.success) {
                console.log('Novel posted successfully');
                utils.showToast('Novel posted to WordPress successfully!', 'success');

                // Reload WordPress status to show the updated posting status
                console.log('Reloading WordPress status...');
                await this.loadWordPressStatus();
                this.displayNovelDetails();

            } else if (result.error && result.error.includes('already posted')) {
                console.log('Novel already posted');
                utils.showToast('Novel is already posted to this WordPress profile', 'info');
            } else {
                console.error('Novel posting failed:', result.error);
                utils.showToast('Failed to post novel to WordPress: ' + result.error, 'error');
            }

        } catch (error) {
            console.error('Novel WordPress posting error:', error);
            utils.showToast('Network error occurred during WordPress posting: ' + error.message, 'error');
        } finally {
            utils.showLoading(false);
        }
    }

    async loadCategoriesForNovelPosting(profileId) {
        const selectElement = document.getElementById('novelCategorySelect');
        const loadButton = document.getElementById('loadNovelCategories');

        if (!selectElement) {
            console.error('Novel category select element not found');
            return;
        }

        try {
            // Disable button and show loading state
            if (loadButton) {
                loadButton.disabled = true;
                loadButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
            }

            // Clear existing options
            selectElement.innerHTML = '<option value="">Loading categories...</option>';

            const response = await fetch(`api/wordpress-categories.php?profile_id=${profileId}&_t=${Date.now()}`);
            const result = await response.json();

            if (result.success) {
                // Clear loading message
                selectElement.innerHTML = '';

                // Add categories to select
                result.categories.forEach(category => {
                    const option = document.createElement('option');
                    option.value = category.id;
                    option.textContent = category.name + (category.count > 0 ? ` (${category.count})` : '');
                    selectElement.appendChild(option);
                });

                if (result.categories.length === 0) {
                    selectElement.innerHTML = '<option value="">No categories found</option>';
                }
            } else {
                selectElement.innerHTML = '<option value="">Failed to load categories</option>';
                utils.showToast(result.error || 'Failed to load categories', 'error');
            }
        } catch (error) {
            console.error('Error loading categories:', error);
            selectElement.innerHTML = '<option value="">Error loading categories</option>';
            utils.showToast('Network error occurred while loading categories', 'error');
        } finally {
            // Restore button state
            if (loadButton) {
                loadButton.disabled = false;
                loadButton.innerHTML = '<i class="fas fa-sync-alt"></i>';
            }
        }
    }

    // Chapter title editing methods
    editChapterTitle(chapterNumber) {
        const displayDiv = document.getElementById(`title-display-${chapterNumber}`);
        const editDiv = document.getElementById(`title-edit-${chapterNumber}`);
        const input = document.getElementById(`title-input-${chapterNumber}`);

        if (displayDiv && editDiv && input) {
            displayDiv.style.display = 'none';
            editDiv.style.display = 'block';
            // The input already has the current value from the HTML template
            input.focus();
            input.select();
        }
    }

    addChapterTitle(chapterNumber) {
        // For chapters without translated titles, we need to create the editing interface
        // This will trigger a refresh that includes the editing interface
        const chapter = this.chapters.find(ch => ch.chapter_number === chapterNumber);
        if (chapter) {
            // Temporarily set an empty translated title to trigger the edit interface
            chapter.translated_title = '';
            this.displayNovelDetails();

            // After refresh, immediately enter edit mode
            setTimeout(() => {
                this.editChapterTitle(chapterNumber);
            }, 100);
        }
    }

    cancelEditChapterTitle(chapterNumber) {
        const displayDiv = document.getElementById(`title-display-${chapterNumber}`);
        const editDiv = document.getElementById(`title-edit-${chapterNumber}`);

        if (displayDiv && editDiv) {
            displayDiv.style.display = 'block';
            editDiv.style.display = 'none';
        }
    }

    async saveChapterTitle(chapterNumber) {
        const input = document.getElementById(`title-input-${chapterNumber}`);
        if (!input) return;

        const newTitle = input.value.trim();

        if (!newTitle) {
            utils.showToast('Title cannot be empty', 'warning');
            input.focus();
            return;
        }

        const saveButton = input.parentElement.querySelector('.btn-success');
        const originalButtonContent = saveButton ? saveButton.innerHTML : '';

        try {
            // Show loading state
            if (saveButton) {
                saveButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
                saveButton.disabled = true;
            }

            const result = await utils.makeApiRequest('api/chapters.php', {
                method: 'PATCH',
                body: JSON.stringify({
                    action: 'update_title',
                    novel_id: this.novelId,
                    chapter_number: chapterNumber,
                    translated_title: newTitle
                })
            });

            if (result.success) {
                utils.showToast('Chapter title updated successfully', 'success');

                // Update the chapter data in memory
                const chapterIndex = this.chapters.findIndex(ch => ch.chapter_number === chapterNumber);
                if (chapterIndex !== -1) {
                    this.chapters[chapterIndex].translated_title = newTitle;
                }

                // Refresh the display to show the updated title
                this.displayNovelDetails();
            } else {
                utils.showToast(result.error || 'Failed to update chapter title', 'error');

                // Restore button state
                if (saveButton) {
                    saveButton.innerHTML = originalButtonContent;
                    saveButton.disabled = false;
                }
            }
        } catch (error) {
            console.error('Save chapter title error:', error);
            utils.showToast('Network error occurred', 'error');

            // Restore button state
            if (saveButton) {
                saveButton.innerHTML = originalButtonContent;
                saveButton.disabled = false;
            }
        }
    }

    /**
     * Show manual content input modal for existing chapters without content
     */
    showManualContentModal(chapterNumber) {
        const chapter = this.chapters.find(ch => ch.chapter_number === chapterNumber);
        if (!chapter) {
            utils.showToast('Chapter not found', 'error');
            return;
        }

        if (chapter.original_content) {
            utils.showToast('This chapter already has content. Use the View page to edit it.', 'warning');
            return;
        }

        const modalHtml = `
            <div class="modal fade" id="manualContentModal" tabindex="-1" aria-labelledby="manualContentModalLabel" aria-hidden="true">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="manualContentModalLabel">
                                <i class="fas fa-edit me-2"></i>
                                Add Content to Chapter ${chapterNumber}
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                <strong>Manual Content Entry:</strong> Use this when automatic saving fails due to bot protection or other issues.
                            </div>

                            <form id="manual-content-form">
                                <div class="mb-3">
                                    <label for="manual-content-title" class="form-label">
                                        <strong>Chapter Title:</strong>
                                    </label>
                                    <input type="text" class="form-control" id="manual-content-title"
                                           value="${utils.escapeHtml(chapter.original_title || '')}"
                                           placeholder="Enter chapter title...">
                                </div>

                                <div class="mb-3">
                                    <label for="manual-content-text" class="form-label">
                                        <strong>Chapter Content:</strong>
                                        <small class="text-muted">(Required)</small>
                                    </label>
                                    <textarea class="form-control" id="manual-content-text"
                                              rows="15"
                                              placeholder="Paste or type the chapter content here..."
                                              style="font-family: monospace; font-size: 14px;"></textarea>
                                    <div class="form-text">
                                        Character count: <span id="manual-content-char-count">0</span>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="manual-content-url" class="form-label">
                                        <strong>Source URL:</strong>
                                        <small class="text-muted">(Optional)</small>
                                    </label>
                                    <input type="url" class="form-control" id="manual-content-url"
                                           value="${utils.escapeHtml(chapter.url || '')}"
                                           placeholder="https://...">
                                </div>
                            </form>

                            <div id="manual-content-messages"></div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                                <i class="fas fa-times me-1"></i>
                                Cancel
                            </button>
                            <button type="button" class="btn btn-success" onclick="novelDetails.saveManualContent(${chapterNumber})">
                                <i class="fas fa-save me-1"></i>
                                Save Content
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Remove existing modal if present
        const existingModal = document.getElementById('manualContentModal');
        if (existingModal) {
            existingModal.remove();
        }

        // Add modal to page
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // Show modal
        const modal = new bootstrap.Modal(document.getElementById('manualContentModal'));
        modal.show();

        // Initialize character counter
        const contentTextarea = document.getElementById('manual-content-text');
        const charCountSpan = document.getElementById('manual-content-char-count');

        contentTextarea.addEventListener('input', () => {
            charCountSpan.textContent = contentTextarea.value.length;
        });

        // Clean up modal when hidden
        document.getElementById('manualContentModal').addEventListener('hidden.bs.modal', () => {
            document.getElementById('manualContentModal').remove();
        });
    }

    /**
     * Show confirmation dialog for clearing chapter content
     */
    showClearContentConfirmation(chapterNumber) {
        const chapter = this.chapters.find(ch => ch.chapter_number === chapterNumber);
        if (!chapter) {
            utils.showToast('Chapter not found', 'error');
            return;
        }

        const modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.innerHTML = `
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="fas fa-eraser text-warning me-2"></i>
                            Clear Chapter Content
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>Warning:</strong> This will clear the chapter content while preserving the chapter entry.
                        </div>
                        <p><strong>Chapter:</strong> ${chapter.original_title || 'Chapter ' + chapterNumber}</p>
                        <p><strong>Content length:</strong> ${(chapter.original_content || '').length} characters</p>
                        <p><strong>Translation status:</strong> ${chapter.translation_status || 'pending'}</p>

                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle me-2"></i>What will be cleared:</h6>
                            <ul class="mb-0">
                                <li>Chapter content (original and translated)</li>
                                <li>Chapter chunks and translation parts</li>
                                <li>Associated WordPress posts</li>
                                <li>Translation logs for this chapter</li>
                            </ul>
                        </div>

                        <p class="text-muted">After clearing, the chapter will appear as "not saved" and you can re-crawl it from the source URL.</p>

                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="confirmClearContent">
                            <label class="form-check-label" for="confirmClearContent">
                                I understand this will clear the chapter content
                            </label>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="button" class="btn btn-warning" onclick="novelDetails.clearChapterContent(${chapterNumber})" id="clearContentConfirmBtn" disabled>
                            <i class="fas fa-eraser me-1"></i>
                            Clear Content
                        </button>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
        const bsModal = new bootstrap.Modal(modal);
        bsModal.show();

        // Enable clear button only when checkbox is checked
        const checkbox = modal.querySelector('#confirmClearContent');
        const clearBtn = modal.querySelector('#clearContentConfirmBtn');
        checkbox.addEventListener('change', () => {
            clearBtn.disabled = !checkbox.checked;
        });

        // Clean up modal when hidden
        modal.addEventListener('hidden.bs.modal', () => {
            document.body.removeChild(modal);
        });
    }

    /**
     * Clear chapter content while preserving the chapter entry
     */
    async clearChapterContent(chapterNumber) {
        // Close any open modals
        const modals = document.querySelectorAll('.modal');
        modals.forEach(modal => {
            const bsModal = bootstrap.Modal.getInstance(modal);
            if (bsModal) bsModal.hide();
        });

        // Show loading state
        utils.showToast('Clearing chapter content...', 'info');

        try {
            const response = await fetch('/wc/api/chapters.php', {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    action: 'delete_chapter',
                    novel_id: this.novelId,
                    chapter_number: chapterNumber
                })
            });

            const result = await response.json();

            if (result.success) {
                utils.showToast(
                    `Chapter content cleared successfully! Chapter ${result.data.cleared_chapter.chapter_number} can now be re-crawled.`,
                    'success'
                );

                // Refresh the novel details to show the updated state
                this.loadNovelDetails();
            } else {
                utils.showToast(`Failed to clear chapter content: ${result.error}`, 'error');
            }
        } catch (error) {
            console.error('Clear content error:', error);
            utils.showToast('An error occurred while clearing the chapter content. Please try again.', 'error');
        }
    }

    showSplitChapterModal(chapterNumber) {
        const chapter = this.chapters.find(ch => ch.chapter_number === chapterNumber);
        if (!chapter || !chapter.original_content) {
            utils.showToast('Chapter has no content to split', 'error');
            return;
        }

        const contentLength = chapter.original_content.length;
        const recommendedParts = Math.ceil(contentLength / 3000); // Recommend splitting every 3000 characters

        const modalHtml = `
            <div class="modal fade" id="splitChapterModal" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-cut me-2"></i>
                                Split Chapter ${chapterNumber}
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="mb-3">
                                <p class="text-muted">
                                    This chapter has <strong>${contentLength.toLocaleString()}</strong> characters.
                                    Large chapters may cause translation timeouts.
                                </p>
                                <p class="text-info">
                                    <i class="fas fa-lightbulb me-1"></i>
                                    Recommended: Split into <strong>${recommendedParts}</strong> parts for optimal translation.
                                </p>
                            </div>
                            <div class="mb-3">
                                <label for="splitParts" class="form-label">Number of parts to split into:</label>
                                <input type="number" class="form-control" id="splitParts"
                                       min="2" max="10" value="${recommendedParts}">
                                <div class="form-text" id="splitPartsHelp">
                                    Each part will be approximately ${Math.round(contentLength / recommendedParts).toLocaleString()} characters.
                                </div>
                            </div>
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle me-1"></i>
                                <strong>Note:</strong> This will split the chapter content at natural boundaries
                                (paragraphs/sentences) and create separate chunks that can be translated individually.
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                            <button type="button" class="btn btn-success" onclick="novelDetails.splitChapter(${chapterNumber})">
                                <i class="fas fa-cut me-1"></i>
                                Split Chapter
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Remove existing modal if present
        const existingModal = document.getElementById('splitChapterModal');
        if (existingModal) {
            existingModal.remove();
        }

        // Add modal to page
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // Show modal
        const modal = new bootstrap.Modal(document.getElementById('splitChapterModal'));
        modal.show();

        // Update character count when parts number changes
        const splitPartsInput = document.getElementById('splitParts');
        splitPartsInput.addEventListener('input', () => {
            const parts = parseInt(splitPartsInput.value) || 2;
            const avgCharsPerPart = Math.round(contentLength / parts);
            const helpText = document.getElementById('splitPartsHelp');
            helpText.textContent = `Each part will be approximately ${avgCharsPerPart.toLocaleString()} characters.`;
        });
    }

    async splitChapter(chapterNumber) {
        const splitParts = parseInt(document.getElementById('splitParts').value);

        if (!splitParts || splitParts < 2 || splitParts > 10) {
            utils.showToast('Please enter a valid number of parts (2-10)', 'error');
            return;
        }

        const modal = bootstrap.Modal.getInstance(document.getElementById('splitChapterModal'));
        modal.hide();

        utils.showLoading(true, 'Splitting chapter...');

        try {
            const response = await utils.makeApiRequest('api/chapter-split.php', {
                method: 'POST',
                body: JSON.stringify({
                    novel_id: this.novelId,
                    chapter_number: chapterNumber,
                    split_parts: splitParts
                })
            });

            if (response.success) {
                utils.showToast(`Chapter ${chapterNumber} successfully split into ${response.data.chunks_created} parts`, 'success');

                // Reload novel details to show updated chunk information
                await this.loadNovelDetails();
            } else {
                utils.showToast(`Failed to split chapter: ${response.error}`, 'error');
            }
        } catch (error) {
            console.error('Split chapter error:', error);
            utils.showToast('Network error occurred while splitting chapter', 'error');
        } finally {
            utils.showLoading(false);
        }
    }

    /**
     * Show POV selection modal before translation
     */
    async showPOVSelectionModal(chapterNumber, translationType = 'single') {
        return new Promise(async (resolve) => {
            const modalId = 'povSelectionModal';

            // Remove existing modal if any
            const existingModal = document.getElementById(modalId);
            if (existingModal) {
                existingModal.remove();
            }

            // Get current POV preference for this novel
            const currentPOV = await this.getCurrentPOVPreference();

            const modalHtml = `
                <div class="modal fade" id="${modalId}" tabindex="-1" aria-labelledby="povSelectionModalLabel" aria-hidden="true">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title" id="povSelectionModalLabel">
                                    <i class="fas fa-eye me-2"></i>
                                    Select Narrative Perspective
                                </h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body">
                                <div class="mb-3">
                                    <p class="text-muted">
                                        ${this.getPOVModalDescription(translationType, chapterNumber)}
                                        This setting will influence how pronouns, narrative voice, and perspective-related language elements are translated.
                                    </p>
                                </div>

                                <div class="row">
                                    <div class="col-12">
                                        <div class="pov-options">
                                            ${this.renderPOVOption('preserve_original', 'Preserve Original', 'Maintain the original perspective as detected in the source text', currentPOV)}
                                            ${this.renderPOVOption('first_person', 'First Person', 'Use "I", "me", "my" - Personal, intimate narrative from the protagonist\'s viewpoint', currentPOV)}
                                            ${this.renderPOVOption('second_person', 'Second Person', 'Use "you", "your" - Direct address to the reader (rare in novels)', currentPOV)}
                                            ${this.renderPOVOption('third_person_limited', 'Third Person Limited', 'Use "he", "she", "they" - Focus on one character\'s thoughts and feelings', currentPOV)}
                                            ${this.renderPOVOption('third_person_omniscient', 'Third Person Omniscient', 'Use "he", "she", "they" - Access to all characters\' thoughts and feelings', currentPOV)}
                                        </div>
                                    </div>
                                </div>

                                <div class="mt-4">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="saveAsDefault" ${currentPOV ? 'checked' : ''}>
                                        <label class="form-check-label" for="saveAsDefault">
                                            <strong>Save as default for this novel</strong>
                                            <small class="text-muted d-block">Future translations will use this perspective by default</small>
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                <button type="button" class="btn btn-primary" id="confirmPOVSelection">
                                    <i class="fas fa-check me-2"></i>
                                    Start Translation
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // Add modal to page
            document.body.insertAdjacentHTML('beforeend', modalHtml);

            // Initialize modal
            const modal = new bootstrap.Modal(document.getElementById(modalId));

            // Handle POV option selection
            document.querySelectorAll('.pov-option').forEach(option => {
                option.addEventListener('click', () => {
                    document.querySelectorAll('.pov-option').forEach(opt => opt.classList.remove('selected'));
                    option.classList.add('selected');
                });
            });

            // Handle confirm button
            document.getElementById('confirmPOVSelection').addEventListener('click', () => {
                const selectedOption = document.querySelector('.pov-option.selected');
                if (!selectedOption) {
                    utils.showToast('Please select a narrative perspective', 'warning');
                    return;
                }

                const perspective = selectedOption.dataset.pov;
                const saveAsDefault = document.getElementById('saveAsDefault').checked;

                modal.hide();
                resolve({
                    perspective: perspective,
                    saveAsDefault: saveAsDefault
                });
            });

            // Handle modal close/cancel
            document.getElementById(modalId).addEventListener('hidden.bs.modal', function () {
                this.remove();
                resolve(null); // User cancelled
            });

            // Show modal
            modal.show();
        });
    }

    /**
     * Render a POV option for the selection modal
     */
    renderPOVOption(value, title, description, currentPOV) {
        const isSelected = currentPOV === value || (!currentPOV && value === 'preserve_original');

        return `
            <div class="pov-option ${isSelected ? 'selected' : ''}" data-pov="${value}">
                <div class="d-flex align-items-start">
                    <div class="pov-radio me-3">
                        <i class="fas fa-circle${isSelected ? '' : '-o'}"></i>
                    </div>
                    <div class="pov-content flex-grow-1">
                        <h6 class="pov-title mb-1">${title}</h6>
                        <p class="pov-description text-muted mb-0">${description}</p>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Get current POV preference for this novel
     */
    async getCurrentPOVPreference() {
        try {
            const response = await utils.makeApiRequest(`api/pov-preferences.php?novel_id=${this.novelId}`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            if (response.success && response.data) {
                return response.data.novel_default;
            }
        } catch (error) {
            console.error('Failed to get POV preference:', error);
        }

        // Default to preserve_original if no preference is set
        return 'preserve_original';
    }

    /**
     * Load and display POV management interface
     */
    async loadPOVManagement() {
        try {
            const response = await utils.makeApiRequest(`api/pov-preferences.php?novel_id=${this.novelId}`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            if (response.success && response.data) {
                this.renderPOVManagement(response.data);
            } else {
                this.renderPOVManagementError('Failed to load POV preferences');
            }
        } catch (error) {
            console.error('Failed to load POV management:', error);
            this.renderPOVManagementError('Network error occurred while loading POV preferences');
        }
    }

    /**
     * Render POV management interface
     */
    renderPOVManagement(data) {
        const povContainer = document.getElementById('pov-management-content');
        if (!povContainer) return;

        const currentPOV = data.novel_default || 'preserve_original';
        const statistics = data.statistics || {};
        const availableOptions = data.available_options || {};

        let html = `
            <div class="row">
                <div class="col-md-8">
                    <div class="d-flex align-items-center justify-content-between mb-3">
                        <div>
                            <h6 class="mb-1">Current Default POV Setting</h6>
                            <p class="text-muted mb-0">This perspective will be used for new translations</p>
                        </div>
                        <div class="text-end">
                            ${this.renderPOVBadge(currentPOV, availableOptions[currentPOV])}
                        </div>
                    </div>

                    <div class="d-flex gap-2 mb-3">
                        <button class="btn btn-primary btn-sm" onclick="novelDetails.changePOVPreference()">
                            <i class="fas fa-edit me-1"></i>
                            Change POV Setting
                        </button>
                        ${currentPOV !== 'preserve_original' ? `
                            <button class="btn btn-outline-secondary btn-sm" onclick="novelDetails.resetPOVPreference()">
                                <i class="fas fa-undo me-1"></i>
                                Reset to Original
                            </button>
                        ` : ''}
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="pov-statistics">
                        <h6 class="mb-2">Translation Statistics</h6>
                        <div class="small text-muted">
                            <div class="d-flex justify-content-between">
                                <span>Total Chapters:</span>
                                <span>${statistics.total_chapters || 0}</span>
                            </div>
                            <div class="d-flex justify-content-between">
                                <span>Has POV Settings:</span>
                                <span>${statistics.has_preferences ? 'Yes' : 'No'}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Add chapter-specific POV overrides if any
        if (data.chapter_specific && data.chapter_specific.length > 0) {
            html += `
                <hr class="my-3">
                <div class="chapter-pov-overrides">
                    <h6 class="mb-2">Chapter-Specific POV Overrides</h6>
                    <div class="row">
            `;

            data.chapter_specific.forEach(override => {
                html += `
                    <div class="col-md-6 mb-2">
                        <div class="d-flex align-items-center justify-content-between p-2 border rounded">
                            <span class="small">Chapter ${override.chapter_number}</span>
                            ${this.renderPOVBadge(override.pov_preference, availableOptions[override.pov_preference], true)}
                        </div>
                    </div>
                `;
            });

            html += `
                    </div>
                </div>
            `;
        }

        povContainer.innerHTML = html;
    }

    /**
     * Render POV badge
     */
    renderPOVBadge(povType, povInfo, isSmall = false) {
        const sizeClass = isSmall ? 'pov-status-small' : '';
        const povClass = povType.replace('_', '-');

        return `
            <span class="pov-status ${povClass} ${sizeClass}" title="${povInfo ? povInfo.description : ''}">
                <i class="fas fa-eye me-1"></i>
                ${povInfo ? povInfo.name : povType.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
            </span>
        `;
    }

    /**
     * Show POV preference change modal
     */
    async changePOVPreference() {
        try {
            const response = await utils.makeApiRequest(`api/pov-preferences.php?novel_id=${this.novelId}`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            if (response.success && response.data) {
                this.showPOVChangeModal(response.data);
            } else {
                utils.showToast('Failed to load POV options', 'error');
            }
        } catch (error) {
            console.error('Failed to load POV options:', error);
            utils.showToast('Network error occurred', 'error');
        }
    }

    /**
     * Show POV change modal
     */
    showPOVChangeModal(data) {
        const modalId = 'povChangeModal';

        // Remove existing modal if any
        const existingModal = document.getElementById(modalId);
        if (existingModal) {
            existingModal.remove();
        }

        const currentPOV = data.novel_default || 'preserve_original';
        const availableOptions = data.available_options || {};

        const modalHtml = `
            <div class="modal fade" id="${modalId}" tabindex="-1" aria-labelledby="povChangeModalLabel" aria-hidden="true">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="povChangeModalLabel">
                                <i class="fas fa-eye me-2"></i>
                                Change Default POV Setting
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <div class="mb-3">
                                <p class="text-muted">
                                    Choose the default narrative perspective for this novel. This setting will be used for all future translations unless overridden for specific chapters.
                                </p>
                            </div>

                            <div class="pov-options">
                                ${Object.entries(availableOptions).map(([key, option]) =>
                                    this.renderPOVChangeOption(key, option, currentPOV)
                                ).join('')}
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                            <button type="button" class="btn btn-primary" id="confirmPOVChange">
                                <i class="fas fa-save me-2"></i>
                                Save POV Setting
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Add modal to page
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // Initialize modal
        const modal = new bootstrap.Modal(document.getElementById(modalId));

        // Handle POV option selection
        document.querySelectorAll('.pov-option').forEach(option => {
            option.addEventListener('click', () => {
                document.querySelectorAll('.pov-option').forEach(opt => opt.classList.remove('selected'));
                option.classList.add('selected');
            });
        });

        // Handle confirm button
        document.getElementById('confirmPOVChange').addEventListener('click', async () => {
            const selectedOption = document.querySelector('.pov-option.selected');
            if (!selectedOption) {
                utils.showToast('Please select a POV preference', 'warning');
                return;
            }

            const newPOV = selectedOption.dataset.pov;
            await this.savePOVPreference(newPOV);
            modal.hide();
        });

        // Handle modal close
        document.getElementById(modalId).addEventListener('hidden.bs.modal', function () {
            this.remove();
        });

        // Show modal
        modal.show();
    }

    /**
     * Render POV change option
     */
    renderPOVChangeOption(key, option, currentPOV) {
        const isSelected = key === currentPOV;

        return `
            <div class="pov-option ${isSelected ? 'selected' : ''}" data-pov="${key}">
                <div class="d-flex align-items-start">
                    <div class="pov-radio me-3">
                        <i class="fas fa-circle${isSelected ? '' : '-o'}"></i>
                    </div>
                    <div class="pov-content flex-grow-1">
                        <h6 class="pov-title mb-1">${option.name}</h6>
                        <p class="pov-description text-muted mb-0">${option.description}</p>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Save POV preference
     */
    async savePOVPreference(povPreference) {
        try {
            utils.showLoading(true);

            const response = await utils.makeApiRequest('api/pov-preferences.php', {
                method: 'POST',
                body: JSON.stringify({
                    novel_id: this.novelId,
                    pov_preference: povPreference,
                    is_default: true
                })
            });

            if (response.success) {
                utils.showToast('POV preference saved successfully', 'success');
                this.loadPOVManagement(); // Reload the POV management interface
            } else {
                utils.showToast(response.error || 'Failed to save POV preference', 'error');
            }
        } catch (error) {
            console.error('Failed to save POV preference:', error);
            utils.showToast('Network error occurred', 'error');
        } finally {
            utils.showLoading(false);
        }
    }

    /**
     * Reset POV preference to preserve original
     */
    async resetPOVPreference() {
        const confirmed = confirm('Reset POV setting to "Preserve Original"? This will affect future translations.');
        if (!confirmed) return;

        await this.savePOVPreference('preserve_original');
    }

    /**
     * Render POV management error
     */
    renderPOVManagementError(message) {
        const povContainer = document.getElementById('pov-management-content');
        if (!povContainer) return;

        povContainer.innerHTML = `
            <div class="text-center text-muted">
                <i class="fas fa-exclamation-triangle mb-2"></i>
                <p class="mb-2">${message}</p>
                <button class="btn btn-sm btn-outline-primary" onclick="novelDetails.loadPOVManagement()">
                    <i class="fas fa-sync-alt me-1"></i>
                    Retry
                </button>
            </div>
        `;
    }

    /**
     * Clear chunk POV preference (for new translation sessions)
     */
    clearChunkPOVPreference() {
        this.currentChunkPOVPreference = null;
    }

    /**
     * Get POV modal description based on translation type
     */
    getPOVModalDescription(translationType, chapterNumber) {
        switch (translationType) {
            case 'single':
                return `Choose the narrative perspective for translating Chapter ${chapterNumber}.`;
            case 'bulk':
            case 'bulk_retranslate':
                return `Choose the narrative perspective for translating the selected chapters.`;
            case 'chunk':
                return `Choose the narrative perspective for translating this chapter part.`;
            case 'chunks':
                return `Choose the narrative perspective for translating all chapter parts.`;
            case 'retranslate':
                return `Choose the narrative perspective for re-translating Chapter ${chapterNumber}.`;
            default:
                return `Choose the narrative perspective for translation.`;
        }
    }

    /**
     * Save manual content for existing chapter
     */
    async saveManualContent(chapterNumber) {
        const titleInput = document.getElementById('manual-content-title');
        const contentTextarea = document.getElementById('manual-content-text');
        const urlInput = document.getElementById('manual-content-url');
        const messagesDiv = document.getElementById('manual-content-messages');
        const saveButton = document.querySelector('#manualContentModal .btn-success');

        // Clear previous messages
        messagesDiv.innerHTML = '';

        // Validate required fields
        const title = titleInput.value.trim();
        const content = contentTextarea.value.trim();

        if (!title) {
            messagesDiv.innerHTML = `
                <div class="alert alert-danger alert-sm">
                    <i class="fas fa-exclamation-triangle me-1"></i>
                    Chapter title is required.
                </div>
            `;
            titleInput.focus();
            return;
        }

        if (!content) {
            messagesDiv.innerHTML = `
                <div class="alert alert-danger alert-sm">
                    <i class="fas fa-exclamation-triangle me-1"></i>
                    Chapter content is required.
                </div>
            `;
            contentTextarea.focus();
            return;
        }

        // Disable save button and show loading
        const originalButtonContent = saveButton.innerHTML;
        saveButton.disabled = true;
        saveButton.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Saving...';

        try {
            // First update the title if it's different
            const chapter = this.chapters.find(ch => ch.chapter_number === chapterNumber);
            if (chapter && chapter.original_title !== title) {
                await utils.makeApiRequest('api/chapters.php', {
                    method: 'PATCH',
                    body: JSON.stringify({
                        action: 'update_original_title',
                        novel_id: this.novelId,
                        chapter_number: chapterNumber,
                        original_title: title
                    })
                });
            }

            // Update the original content
            const result = await utils.makeApiRequest('api/chapters.php', {
                method: 'PATCH',
                body: JSON.stringify({
                    action: 'update_original_content',
                    novel_id: this.novelId,
                    chapter_number: chapterNumber,
                    original_content: content
                })
            });

            if (result.success) {
                // Show success message
                messagesDiv.innerHTML = `
                    <div class="alert alert-success alert-sm">
                        <i class="fas fa-check-circle me-1"></i>
                        Chapter content saved successfully!
                    </div>
                `;

                // Show toast notification
                utils.showToast(`Chapter ${chapterNumber} content added successfully`, 'success');

                // Refresh chapters and close modal after a short delay
                setTimeout(async () => {
                    await this.refreshChapters();
                    const modal = bootstrap.Modal.getInstance(document.getElementById('manualContentModal'));
                    if (modal) {
                        modal.hide();
                    }
                }, 1500);

            } else {
                messagesDiv.innerHTML = `
                    <div class="alert alert-danger alert-sm">
                        <i class="fas fa-exclamation-triangle me-1"></i>
                        ${result.error || 'Failed to save chapter content'}
                    </div>
                `;
            }

        } catch (error) {
            console.error('Save manual content error:', error);
            messagesDiv.innerHTML = `
                <div class="alert alert-danger alert-sm">
                    <i class="fas fa-exclamation-triangle me-1"></i>
                    Network error occurred. Please try again.
                </div>
            `;
        } finally {
            // Restore button state
            saveButton.disabled = false;
            saveButton.innerHTML = originalButtonContent;
        }
    }

    /**
     * Toggle manual chapter creation mode
     */
    toggleManualChapterMode() {
        this.manualChapterMode = !this.manualChapterMode;

        // If entering manual mode, exit bulk mode
        if (this.manualChapterMode && this.bulkMode) {
            this.bulkMode = false;
            this.selectedChapters.clear();
        }

        // Re-render the page to show/hide the manual chapter form
        this.displayNovelDetails();

        // Show appropriate message
        if (this.manualChapterMode) {
            utils.showToast('Manual chapter creation mode enabled. Use this as a fallback when automatic saving fails.', 'info');
        } else {
            utils.showToast('Manual chapter creation mode disabled.', 'info');
        }
    }

    /**
     * Render manual chapter creation form
     */
    renderManualChapterForm() {
        const nextChapterNumber = Math.max(...this.chapters.map(ch => ch.chapter_number), 0) + 1;

        return `
            <div class="manual-chapter-form mt-3 p-3 border rounded bg-light">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h6 class="mb-0">
                        <i class="fas fa-plus-circle me-2 text-warning"></i>
                        Manual Chapter Creation (Fallback Mode)
                    </h6>
                    <small class="text-muted">
                        <i class="fas fa-info-circle me-1"></i>
                        Use when automatic chapter saving fails
                    </small>
                </div>

                <div class="alert alert-info alert-sm mb-3">
                    <div class="d-flex align-items-start">
                        <i class="fas fa-lightbulb me-2 mt-1"></i>
                        <div>
                            <strong>When to use this feature:</strong>
                            <ul class="mb-0 mt-1 small">
                                <li>When automatic chapter crawling fails or encounters errors</li>
                                <li>For adding chapters from sources not supported by automatic crawling</li>
                                <li>When you need to manually input chapter content as a backup method</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <form id="manual-chapter-form-inline">
                    <div class="row g-3">
                        <div class="col-md-2">
                            <label for="manual-chapter-number" class="form-label">Chapter #</label>
                            <input type="number" class="form-control form-control-sm"
                                   id="manual-chapter-number" min="1" value="${nextChapterNumber}" required>
                        </div>
                        <div class="col-md-5">
                            <label for="manual-original-title" class="form-label">Original Title <span class="text-danger">*</span></label>
                            <input type="text" class="form-control form-control-sm"
                                   id="manual-original-title" placeholder="Enter original chapter title" required>
                        </div>
                        <div class="col-md-5">
                            <label for="manual-translated-title" class="form-label">Translated Title</label>
                            <input type="text" class="form-control form-control-sm"
                                   id="manual-translated-title" placeholder="Enter translated title (optional)">
                        </div>
                    </div>

                    <div class="row g-3 mt-2">
                        <div class="col-12">
                            <label for="manual-original-content" class="form-label">Original Content <span class="text-danger">*</span></label>
                            <textarea class="form-control" id="manual-original-content" rows="8"
                                      placeholder="Paste or type the original chapter content here..." required></textarea>
                            <div class="form-text">
                                <span id="manual-content-stats">Character count: 0</span>
                                <span class="ms-3" id="manual-chunking-info"></span>
                            </div>
                        </div>
                    </div>

                    <div class="row g-3 mt-2">
                        <div class="col-12">
                            <label for="manual-translated-content" class="form-label">Translated Content</label>
                            <textarea class="form-control" id="manual-translated-content" rows="6"
                                      placeholder="Enter translated content (optional - can be translated later)"></textarea>
                            <div class="form-text">Leave empty to translate later using the AI translation system</div>
                        </div>
                    </div>

                    <div class="d-flex justify-content-between align-items-center mt-3">
                        <div id="manual-chapter-messages"></div>
                        <div>
                            <button type="button" class="btn btn-outline-secondary btn-sm me-2"
                                    onclick="novelDetails.resetManualChapterForm()">
                                <i class="fas fa-undo me-1"></i>
                                Reset Form
                            </button>
                            <button type="submit" class="btn btn-success btn-sm">
                                <i class="fas fa-save me-1"></i>
                                Save Chapter
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        `;
    }

    /**
     * Initialize manual chapter form event listeners
     */
    initializeManualChapterForm() {
        const form = document.getElementById('manual-chapter-form-inline');
        const contentTextarea = document.getElementById('manual-original-content');

        if (!form || !contentTextarea) return;

        // Add form submit handler
        form.addEventListener('submit', (e) => {
            e.preventDefault();
            this.submitManualChapter();
        });

        // Add content change handler for character count
        contentTextarea.addEventListener('input', () => {
            this.updateManualContentStats();
        });

        // Add chapter number validation
        const chapterNumberInput = document.getElementById('manual-chapter-number');
        if (chapterNumberInput) {
            chapterNumberInput.addEventListener('blur', () => {
                this.validateManualChapterNumber();
            });
        }
    }

    /**
     * Update content statistics for manual chapter form
     */
    updateManualContentStats() {
        const content = document.getElementById('manual-original-content')?.value || '';
        const charCount = content.length;
        const statsElement = document.getElementById('manual-content-stats');
        const chunkingElement = document.getElementById('manual-chunking-info');

        if (statsElement) {
            statsElement.textContent = `Character count: ${charCount.toLocaleString()}`;
        }

        if (chunkingElement) {
            if (charCount > 15000) {
                chunkingElement.innerHTML = `<span class="text-info"><i class="fas fa-info-circle me-1"></i>Large content will be automatically chunked</span>`;
            } else {
                chunkingElement.textContent = '';
            }
        }
    }

    /**
     * Validate manual chapter number
     */
    async validateManualChapterNumber() {
        const chapterNumber = document.getElementById('manual-chapter-number')?.value;
        const messagesDiv = document.getElementById('manual-chapter-messages');

        if (!chapterNumber || !messagesDiv) return;

        // Check if chapter already exists
        const existingChapter = this.chapters.find(ch => ch.chapter_number == chapterNumber);
        if (existingChapter) {
            messagesDiv.innerHTML = `
                <div class="alert alert-warning alert-sm mb-0">
                    <i class="fas fa-exclamation-triangle me-1"></i>
                    Chapter ${chapterNumber} already exists. This will overwrite the existing chapter.
                </div>
            `;
        } else {
            messagesDiv.innerHTML = '';
        }
    }

    /**
     * Reset manual chapter form
     */
    resetManualChapterForm() {
        const form = document.getElementById('manual-chapter-form-inline');
        if (form) {
            form.reset();

            // Reset chapter number to next available
            const nextChapterNumber = Math.max(...this.chapters.map(ch => ch.chapter_number), 0) + 1;
            const chapterNumberInput = document.getElementById('manual-chapter-number');
            if (chapterNumberInput) {
                chapterNumberInput.value = nextChapterNumber;
            }

            // Clear messages and stats
            document.getElementById('manual-chapter-messages').innerHTML = '';
            this.updateManualContentStats();
        }
    }

    /**
     * Submit manual chapter creation
     */
    async submitManualChapter() {
        const submitBtn = document.querySelector('#manual-chapter-form-inline button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        const messagesDiv = document.getElementById('manual-chapter-messages');

        try {
            // Disable submit button
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Saving...';

            // Collect form data
            const formData = {
                novel_id: this.novelId,
                chapter_number: parseInt(document.getElementById('manual-chapter-number').value),
                original_title: document.getElementById('manual-original-title').value.trim(),
                translated_title: document.getElementById('manual-translated-title').value.trim() || null,
                original_content: document.getElementById('manual-original-content').value.trim(),
                translated_content: document.getElementById('manual-translated-content').value.trim() || null
            };

            // Validate required fields
            if (!formData.original_title) {
                throw new Error('Original chapter title is required');
            }
            if (!formData.original_content) {
                throw new Error('Original chapter content is required');
            }

            // Submit to API
            const response = await utils.makeApiRequest('api/chapters.php', {
                method: 'POST',
                body: JSON.stringify(formData)
            });

            if (response.success) {
                const chunkingMsg = response.data.chunking_result && response.data.chunking_result.success
                    ? ` (${response.data.chunking_result.chunks_created} chunks created)`
                    : '';

                // Show success message
                messagesDiv.innerHTML = `
                    <div class="alert alert-success alert-sm mb-0">
                        <i class="fas fa-check-circle me-1"></i>
                        Chapter ${formData.chapter_number} saved successfully${chunkingMsg}!
                    </div>
                `;

                // Reset form and refresh chapters
                this.resetManualChapterForm();
                await this.refreshChapters();

                // Show toast notification
                utils.showToast(`Chapter ${formData.chapter_number} created successfully`, 'success');

                // Auto-hide manual mode after successful creation
                setTimeout(() => {
                    this.manualChapterMode = false;
                    this.displayNovelDetails();
                }, 3000);

            } else {
                throw new Error(response.error || 'Failed to save chapter');
            }

        } catch (error) {
            console.error('Manual chapter creation error:', error);
            messagesDiv.innerHTML = `
                <div class="alert alert-danger alert-sm mb-0">
                    <i class="fas fa-exclamation-circle me-1"></i>
                    Error: ${error.message}
                </div>
            `;
            utils.showToast('Failed to create chapter: ' + error.message, 'error');
        } finally {
            // Restore button state
            submitBtn.disabled = false;
            submitBtn.innerHTML = originalText;
        }
    }

    /**
     * Show quality check modal for a single chapter
     */
    async showQualityCheckModal(chapterNumber) {
        const modalId = 'qualityCheckModal';

        // Remove existing modal if any
        const existingModal = document.getElementById(modalId);
        if (existingModal) {
            existingModal.remove();
        }

        // Get available AI providers
        const providers = await this.getAvailableAIProviders();

        const modalHtml = `
            <div class="modal fade" id="${modalId}" tabindex="-1" aria-labelledby="${modalId}Label" aria-hidden="true">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="${modalId}Label">
                                <i class="fas fa-check-circle me-2"></i>
                                Translation Quality Check - Chapter ${chapterNumber}
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <div class="mb-3">
                                <label for="qualityCheckProvider" class="form-label">AI Provider for Analysis (Optional)</label>
                                <select class="form-select" id="qualityCheckProvider">
                                    <option value="">Automated Analysis Only</option>
                                    ${providers.map(provider =>
                                        `<option value="${provider.key}">${provider.name} - ${provider.description}</option>`
                                    ).join('')}
                                </select>
                                <div class="form-text">
                                    Select an AI provider for additional quality analysis, or leave blank for automated checks only.
                                </div>
                            </div>

                            <div id="qualityCheckProgress" class="d-none">
                                <div class="d-flex align-items-center mb-3">
                                    <div class="spinner-border spinner-border-sm me-2" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                    <span>Analyzing translation quality...</span>
                                </div>
                                <div class="progress">
                                    <div class="progress-bar progress-bar-striped progress-bar-animated"
                                         role="progressbar" style="width: 100%"></div>
                                </div>
                            </div>

                            <div id="qualityCheckResults" class="d-none">
                                <!-- Results will be populated here -->
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                            <button type="button" class="btn btn-primary" id="startQualityCheck"
                                    onclick="novelDetails.performQualityCheck(${chapterNumber})">
                                <i class="fas fa-play me-1"></i>
                                Start Quality Check
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHtml);
        const modal = new bootstrap.Modal(document.getElementById(modalId));
        modal.show();
    }

    /**
     * Show bulk quality check modal
     */
    async showBulkQualityCheckModal() {
        const selectedChapters = Array.from(this.selectedChapters);
        const translatedChapters = selectedChapters.filter(chapterNum => {
            const chapter = this.chapters.find(ch => ch.chapter_number === chapterNum);
            return chapter && chapter.translation_status === 'completed';
        });

        if (translatedChapters.length === 0) {
            utils.showToast('No translated chapters selected for quality check', 'warning');
            return;
        }

        const modalId = 'bulkQualityCheckModal';

        // Remove existing modal if any
        const existingModal = document.getElementById(modalId);
        if (existingModal) {
            existingModal.remove();
        }

        // Get available AI providers
        const providers = await this.getAvailableAIProviders();

        const modalHtml = `
            <div class="modal fade" id="${modalId}" tabindex="-1" aria-labelledby="${modalId}Label" aria-hidden="true">
                <div class="modal-dialog modal-xl">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="${modalId}Label">
                                <i class="fas fa-check-circle me-2"></i>
                                Bulk Translation Quality Check - ${translatedChapters.length} Chapters
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <div class="mb-3">
                                <label for="bulkQualityCheckProvider" class="form-label">AI Provider for Analysis (Optional)</label>
                                <select class="form-select" id="bulkQualityCheckProvider">
                                    <option value="">Automated Analysis Only</option>
                                    ${providers.map(provider =>
                                        `<option value="${provider.key}">${provider.name} - ${provider.description}</option>`
                                    ).join('')}
                                </select>
                                <div class="form-text">
                                    Select an AI provider for additional quality analysis. Note: AI analysis will significantly increase processing time.
                                </div>
                            </div>

                            <div class="mb-3">
                                <h6>Chapters to Check:</h6>
                                <div class="d-flex flex-wrap gap-1">
                                    ${translatedChapters.map(chapterNum =>
                                        `<span class="badge bg-primary">Chapter ${chapterNum}</span>`
                                    ).join('')}
                                </div>
                            </div>

                            <div id="bulkQualityCheckProgress" class="d-none">
                                <div class="d-flex align-items-center mb-3">
                                    <div class="spinner-border spinner-border-sm me-2" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                    <span id="bulkProgressText">Starting quality checks...</span>
                                </div>
                                <div class="progress mb-3">
                                    <div id="bulkProgressBar" class="progress-bar progress-bar-striped progress-bar-animated"
                                         role="progressbar" style="width: 0%"></div>
                                </div>
                            </div>

                            <div id="bulkQualityCheckResults" class="d-none">
                                <!-- Results will be populated here -->
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                            <button type="button" class="btn btn-primary" id="startBulkQualityCheck"
                                    onclick="novelDetails.performBulkQualityCheck()">
                                <i class="fas fa-play me-1"></i>
                                Start Bulk Quality Check
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHtml);
        const modal = new bootstrap.Modal(document.getElementById(modalId));
        modal.show();
    }

    /**
     * Get available AI providers for quality check
     */
    async getAvailableAIProviders() {
        try {
            const response = await utils.makeApiRequest('api/quality-check.php?action=providers', {
                method: 'GET'
            });

            if (response.success) {
                return response.providers;
            } else {
                console.error('Failed to get AI providers:', response.error);
                return [];
            }
        } catch (error) {
            console.error('Error fetching AI providers:', error);
            return [];
        }
    }

    /**
     * Perform quality check for a single chapter
     */
    async performQualityCheck(chapterNumber) {
        const progressDiv = document.getElementById('qualityCheckProgress');
        const resultsDiv = document.getElementById('qualityCheckResults');
        const startButton = document.getElementById('startQualityCheck');
        const providerSelect = document.getElementById('qualityCheckProvider');

        // Show progress and hide results
        progressDiv.classList.remove('d-none');
        resultsDiv.classList.add('d-none');
        startButton.disabled = true;

        try {
            const requestBody = {
                novel_id: this.novelId,
                chapter_number: chapterNumber
            };

            // Add AI provider if selected
            if (providerSelect.value) {
                requestBody.ai_provider = providerSelect.value;
            }

            const response = await utils.makeApiRequest('api/quality-check.php', {
                method: 'POST',
                body: JSON.stringify(requestBody)
            });

            if (response.success) {
                this.displayQualityCheckResults(response.quality_report, resultsDiv);
                utils.showToast('Quality check completed successfully', 'success');
            } else {
                throw new Error(response.error || 'Quality check failed');
            }

        } catch (error) {
            console.error('Quality check error:', error);
            resultsDiv.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    Quality check failed: ${error.message}
                </div>
            `;
            resultsDiv.classList.remove('d-none');
            utils.showToast('Quality check failed: ' + error.message, 'error');
        } finally {
            progressDiv.classList.add('d-none');
            startButton.disabled = false;
        }
    }

    /**
     * Perform bulk quality check
     */
    async performBulkQualityCheck() {
        const selectedChapters = Array.from(this.selectedChapters);
        const translatedChapters = selectedChapters.filter(chapterNum => {
            const chapter = this.chapters.find(ch => ch.chapter_number === chapterNum);
            return chapter && chapter.translation_status === 'completed';
        });

        const progressDiv = document.getElementById('bulkQualityCheckProgress');
        const resultsDiv = document.getElementById('bulkQualityCheckResults');
        const startButton = document.getElementById('startBulkQualityCheck');
        const providerSelect = document.getElementById('bulkQualityCheckProvider');
        const progressBar = document.getElementById('bulkProgressBar');
        const progressText = document.getElementById('bulkProgressText');

        // Show progress and hide results
        progressDiv.classList.remove('d-none');
        resultsDiv.classList.add('d-none');
        startButton.disabled = true;

        try {
            const results = [];
            let completed = 0;

            for (const chapterNumber of translatedChapters) {
                progressText.textContent = `Checking chapter ${chapterNumber}... (${completed + 1}/${translatedChapters.length})`;
                progressBar.style.width = `${(completed / translatedChapters.length) * 100}%`;

                try {
                    const requestBody = {
                        novel_id: this.novelId,
                        chapter_number: chapterNumber
                    };

                    // Add AI provider if selected
                    if (providerSelect.value) {
                        requestBody.ai_provider = providerSelect.value;
                    }

                    const response = await utils.makeApiRequest('api/quality-check.php', {
                        method: 'POST',
                        body: JSON.stringify(requestBody)
                    });

                    if (response.success) {
                        results.push({
                            chapter_number: chapterNumber,
                            success: true,
                            quality_report: response.quality_report
                        });
                    } else {
                        results.push({
                            chapter_number: chapterNumber,
                            success: false,
                            error: response.error || 'Quality check failed'
                        });
                    }

                } catch (error) {
                    results.push({
                        chapter_number: chapterNumber,
                        success: false,
                        error: error.message
                    });
                }

                completed++;

                // Small delay to prevent overwhelming the server
                await new Promise(resolve => setTimeout(resolve, 500));
            }

            // Display bulk results
            this.displayBulkQualityCheckResults(results, resultsDiv);

            const successCount = results.filter(r => r.success).length;
            const failCount = results.length - successCount;

            if (failCount === 0) {
                utils.showToast(`All ${successCount} quality checks completed successfully`, 'success');
            } else {
                utils.showToast(`${successCount} successful, ${failCount} failed quality checks`, 'warning');
            }

        } catch (error) {
            console.error('Bulk quality check error:', error);
            resultsDiv.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    Bulk quality check failed: ${error.message}
                </div>
            `;
            resultsDiv.classList.remove('d-none');
            utils.showToast('Bulk quality check failed: ' + error.message, 'error');
        } finally {
            progressDiv.classList.add('d-none');
            startButton.disabled = false;
        }
    }

    /**
     * Display quality check results for a single chapter
     */
    displayQualityCheckResults(qualityReport, resultsDiv) {
        const overallScore = qualityReport.overall_score;
        const scoreClass = this.getScoreClass(overallScore.total_score);

        let html = `
            <div class="quality-check-results">
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="card border-${scoreClass}">
                            <div class="card-header bg-${scoreClass} text-white">
                                <h6 class="mb-0">
                                    <i class="fas fa-chart-line me-2"></i>
                                    Overall Quality Score
                                </h6>
                            </div>
                            <div class="card-body text-center">
                                <h2 class="text-${scoreClass}">${overallScore.total_score}/100</h2>
                                <p class="mb-0">${overallScore.grade}</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="fas fa-info-circle me-2"></i>
                                    Chapter Information
                                </h6>
                            </div>
                            <div class="card-body">
                                <small class="text-muted">
                                    Original: ${qualityReport.chapter_info.original_length} chars<br>
                                    Translated: ${qualityReport.chapter_info.translated_length} chars<br>
                                    ${qualityReport.chapter_info.has_chunks ? 'Split into chunks' : 'Single chapter'}
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
        `;

        // Add detailed check results
        const checks = [
            { key: 'consistency_check', title: 'Content Consistency', icon: 'fas fa-balance-scale' },
            { key: 'name_dictionary_check', title: 'Name Dictionary Application', icon: 'fas fa-book' },
            { key: 'formatting_check', title: 'Formatting Preservation', icon: 'fas fa-align-left' },
            { key: 'punctuation_check', title: 'Punctuation Consistency', icon: 'fas fa-quote-right' },
            { key: 'structure_check', title: 'Structural Integrity', icon: 'fas fa-sitemap' }
        ];

        html += '<div class="row">';

        checks.forEach(check => {
            if (qualityReport[check.key]) {
                const checkResult = qualityReport[check.key];
                const checkClass = checkResult.status === 'good' ? 'success' : 'warning';

                html += `
                    <div class="col-md-6 mb-3">
                        <div class="card border-${checkClass}">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="${check.icon} me-2"></i>
                                    ${check.title}
                                    <span class="badge bg-${checkClass} ms-2">${checkResult.status}</span>
                                </h6>
                            </div>
                            <div class="card-body">
                                ${this.renderCheckDetails(checkResult)}
                            </div>
                        </div>
                    </div>
                `;
            }
        });

        html += '</div>';

        // Add AI analysis if available
        if (qualityReport.ai_analysis && qualityReport.ai_analysis.status === 'success') {
            html += this.renderAIAnalysis(qualityReport.ai_analysis);
        }

        // Add actionable recommendations if available
        if (qualityReport.recommendations && qualityReport.recommendations.length > 0) {
            html += this.renderActionableRecommendations(qualityReport.recommendations, qualityReport.auto_corrections, qualityReport.chapter_info);
        }

        html += '</div>';

        resultsDiv.innerHTML = html;
        resultsDiv.classList.remove('d-none');
    }

    /**
     * Display bulk quality check results
     */
    displayBulkQualityCheckResults(results, resultsDiv) {
        const successResults = results.filter(r => r.success);
        const failedResults = results.filter(r => !r.success);

        let html = `
            <div class="bulk-quality-results">
                <div class="row mb-4">
                    <div class="col-md-4">
                        <div class="card border-success">
                            <div class="card-body text-center">
                                <h3 class="text-success">${successResults.length}</h3>
                                <p class="mb-0">Successful Checks</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card border-danger">
                            <div class="card-body text-center">
                                <h3 class="text-danger">${failedResults.length}</h3>
                                <p class="mb-0">Failed Checks</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card border-info">
                            <div class="card-body text-center">
                                <h3 class="text-info">${this.calculateAverageScore(successResults)}</h3>
                                <p class="mb-0">Average Score</p>
                            </div>
                        </div>
                    </div>
                </div>
        `;

        // Add accordion for detailed results
        html += `
            <div class="accordion" id="bulkQualityAccordion">
        `;

        results.forEach((result, index) => {
            const accordionId = `bulkQuality${index}`;
            const scoreClass = result.success ? this.getScoreClass(result.quality_report?.overall_score?.total_score || 0) : 'danger';

            html += `
                <div class="accordion-item">
                    <h2 class="accordion-header" id="heading${index}">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                                data-bs-target="#${accordionId}" aria-expanded="false" aria-controls="${accordionId}">
                            <span class="badge bg-${scoreClass} me-2">Chapter ${result.chapter_number}</span>
                            ${result.success ?
                                `Score: ${result.quality_report.overall_score.total_score}/100 (${result.quality_report.overall_score.grade})` :
                                `Error: ${result.error}`
                            }
                        </button>
                    </h2>
                    <div id="${accordionId}" class="accordion-collapse collapse" aria-labelledby="heading${index}"
                         data-bs-parent="#bulkQualityAccordion">
                        <div class="accordion-body">
                            ${result.success ?
                                this.renderBulkCheckSummary(result.quality_report) :
                                `<div class="alert alert-danger mb-0">${result.error}</div>`
                            }
                        </div>
                    </div>
                </div>
            `;
        });

        html += '</div></div>';

        resultsDiv.innerHTML = html;
        resultsDiv.classList.remove('d-none');
    }

    /**
     * Get CSS class based on quality score
     */
    getScoreClass(score) {
        if (score >= 90) return 'success';
        if (score >= 80) return 'info';
        if (score >= 70) return 'warning';
        return 'danger';
    }

    /**
     * Render check details
     */
    renderCheckDetails(checkResult) {
        let html = '';

        if (checkResult.issues && checkResult.issues.length > 0) {
            html += '<div class="mb-2"><strong>Issues Requiring Manual Review:</strong><ul class="mb-0">';
            checkResult.issues.forEach(issue => {
                html += `<li class="text-danger"><i class="fas fa-exclamation-triangle me-1"></i>${issue}</li>`;
            });
            html += '</ul></div>';
        }

        if (checkResult.warnings && checkResult.warnings.length > 0) {
            html += '<div class="mb-2"><strong>Warnings & Auto-fixable Issues:</strong><ul class="mb-0">';
            checkResult.warnings.forEach(warning => {
                const isAutoFixable = warning.includes('can be automatically fixed');
                const iconClass = isAutoFixable ? 'fas fa-magic' : 'fas fa-exclamation-circle';
                const textClass = isAutoFixable ? 'text-info' : 'text-warning';
                html += `<li class="${textClass}"><i class="${iconClass} me-1"></i>${warning}</li>`;
            });
            html += '</ul></div>';
        }

        if (checkResult.metrics) {
            html += '<div><strong>Metrics:</strong><br>';
            Object.entries(checkResult.metrics).forEach(([key, value]) => {
                if (key === 'has_auto_fixable_punctuation') {
                    return; // Skip this internal metric
                }
                const displayKey = key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
                html += `<small class="text-muted">${displayKey}: ${value}<br></small>`;
            });
            html += '</div>';
        }

        if (!checkResult.issues?.length && !checkResult.warnings?.length) {
            html = '<span class="text-success"><i class="fas fa-check me-1"></i>No issues found</span>';
        }

        return html;
    }

    /**
     * Render AI analysis results
     */
    renderAIAnalysis(aiAnalysis) {
        return `
            <div class="card border-primary mt-3">
                <div class="card-header bg-primary text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-robot me-2"></i>
                        AI Quality Analysis
                        <span class="badge bg-light text-dark ms-2">Score: ${aiAnalysis.score}/10</span>
                    </h6>
                </div>
                <div class="card-body">
                    ${aiAnalysis.issues && aiAnalysis.issues.length > 0 ? `
                        <div class="mb-3">
                            <strong>AI-Identified Issues:</strong>
                            <ul class="mb-0">
                                ${aiAnalysis.issues.map(issue => `<li class="text-danger">${issue}</li>`).join('')}
                            </ul>
                        </div>
                    ` : ''}

                    ${aiAnalysis.recommendations && aiAnalysis.recommendations.length > 0 ? `
                        <div class="mb-3">
                            <strong>Recommendations:</strong>
                            <ul class="mb-0">
                                ${aiAnalysis.recommendations.map(rec => `<li class="text-info">${rec}</li>`).join('')}
                            </ul>
                        </div>
                    ` : ''}

                    <small class="text-muted">
                        Provider: ${aiAnalysis.provider_used} |
                        Execution time: ${aiAnalysis.execution_time}s
                    </small>
                </div>
            </div>
        `;
    }

    /**
     * Calculate average score from successful results
     */
    calculateAverageScore(successResults) {
        if (successResults.length === 0) return '0/100';

        const totalScore = successResults.reduce((sum, result) => {
            return sum + (result.quality_report?.overall_score?.total_score || 0);
        }, 0);

        return Math.round(totalScore / successResults.length) + '/100';
    }

    /**
     * Render bulk check summary
     */
    renderBulkCheckSummary(qualityReport) {
        const overallScore = qualityReport.overall_score;
        const scoreClass = this.getScoreClass(overallScore.total_score);

        let html = `
            <div class="row mb-3">
                <div class="col-md-6">
                    <div class="text-center">
                        <h4 class="text-${scoreClass}">${overallScore.total_score}/100</h4>
                        <p class="mb-0">${overallScore.grade}</p>
                    </div>
                </div>
                <div class="col-md-6">
                    <small class="text-muted">
                        Original: ${qualityReport.chapter_info.original_length} chars<br>
                        Translated: ${qualityReport.chapter_info.translated_length} chars
                    </small>
                </div>
            </div>
        `;

        // Summary of issues
        const allIssues = [];
        const allWarnings = [];

        ['consistency_check', 'name_dictionary_check', 'formatting_check', 'punctuation_check', 'structure_check'].forEach(checkKey => {
            if (qualityReport[checkKey]) {
                if (qualityReport[checkKey].issues) {
                    allIssues.push(...qualityReport[checkKey].issues);
                }
                if (qualityReport[checkKey].warnings) {
                    allWarnings.push(...qualityReport[checkKey].warnings);
                }
            }
        });

        if (allIssues.length > 0) {
            html += `
                <div class="alert alert-danger alert-sm">
                    <strong>Issues (${allIssues.length}):</strong>
                    <ul class="mb-0 mt-1">
                        ${allIssues.slice(0, 3).map(issue => `<li>${issue}</li>`).join('')}
                        ${allIssues.length > 3 ? `<li><em>... and ${allIssues.length - 3} more</em></li>` : ''}
                    </ul>
                </div>
            `;
        }

        if (allWarnings.length > 0) {
            html += `
                <div class="alert alert-warning alert-sm">
                    <strong>Warnings (${allWarnings.length}):</strong>
                    <ul class="mb-0 mt-1">
                        ${allWarnings.slice(0, 2).map(warning => `<li>${warning}</li>`).join('')}
                        ${allWarnings.length > 2 ? `<li><em>... and ${allWarnings.length - 2} more</em></li>` : ''}
                    </ul>
                </div>
            `;
        }

        if (allIssues.length === 0 && allWarnings.length === 0) {
            html += '<div class="alert alert-success alert-sm mb-0">No issues or warnings found</div>';
        }

        return html;
    }

    /**
     * Render actionable recommendations with fix buttons
     */
    renderActionableRecommendations(recommendations, autoCorrections, chapterInfo) {
        if (!recommendations || recommendations.length === 0) {
            return '';
        }

        let html = `
            <div class="card border-warning mt-4">
                <div class="card-header bg-warning text-dark">
                    <h6 class="mb-0">
                        <i class="fas fa-tools me-2"></i>
                        Actionable Recommendations
                        <span class="badge bg-dark ms-2">${recommendations.length} available</span>
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
        `;

        recommendations.forEach((rec, index) => {
            const priorityClass = rec.priority === 'high' ? 'danger' : rec.priority === 'medium' ? 'warning' : 'info';
            const actionButton = rec.actionable ? this.renderActionButton(rec, autoCorrections, chapterInfo) : '';

            html += `
                <div class="col-md-6 mb-3">
                    <div class="card border-${priorityClass}">
                        <div class="card-header">
                            <div class="d-flex justify-content-between align-items-center">
                                <h6 class="mb-0">${rec.title}</h6>
                                <span class="badge bg-${priorityClass}">${rec.priority}</span>
                            </div>
                        </div>
                        <div class="card-body">
                            <p class="card-text">${rec.description}</p>
                            ${rec.estimated_fixes > 0 ? `<small class="text-muted">Estimated fixes: ${rec.estimated_fixes}</small>` : ''}
                            ${actionButton}
                        </div>
                    </div>
                </div>
            `;
        });

        html += `
                    </div>
                    <div class="mt-3 text-center">
                        <button class="btn btn-success" onclick="novelDetails.applyAllRecommendedFixes(${chapterInfo.chapter_number})">
                            <i class="fas fa-magic me-1"></i>
                            Apply All Recommended Fixes
                        </button>
                        <button class="btn btn-outline-info ms-2" onclick="novelDetails.previewAllFixes(${chapterInfo.chapter_number})">
                            <i class="fas fa-eye me-1"></i>
                            Preview Changes
                        </button>
                    </div>
                </div>
            </div>
        `;

        return html;
    }

    /**
     * Render action button for a recommendation
     */
    renderActionButton(recommendation, autoCorrections, chapterInfo) {
        if (!recommendation.actionable) {
            return '<span class="text-muted"><i class="fas fa-info-circle me-1"></i>Manual review required</span>';
        }

        const buttonClass = recommendation.priority === 'high' ? 'btn-danger' : 'btn-warning';
        const actionId = `${recommendation.action}_${chapterInfo.chapter_number}`;

        switch (recommendation.action) {
            case 'auto_fix_names':
                return `
                    <button class="btn ${buttonClass} btn-sm mt-2"
                            onclick="novelDetails.applyNameFixes(${chapterInfo.chapter_number})"
                            title="Apply name dictionary corrections">
                        <i class="fas fa-book me-1"></i>
                        Fix Names (${recommendation.estimated_fixes})
                    </button>
                `;

            case 'auto_fix_formatting':
                return `
                    <button class="btn ${buttonClass} btn-sm mt-2"
                            onclick="novelDetails.applyFormattingFixes(${chapterInfo.chapter_number})"
                            title="Fix formatting and structure issues">
                        <i class="fas fa-align-left me-1"></i>
                        Fix Formatting (${recommendation.estimated_fixes})
                    </button>
                `;

            case 'auto_fix_punctuation':
                return `
                    <button class="btn ${buttonClass} btn-sm mt-2"
                            onclick="novelDetails.applyPunctuationFixes(${chapterInfo.chapter_number})"
                            title="Fix punctuation issues">
                        <i class="fas fa-quote-right me-1"></i>
                        Fix Punctuation (${recommendation.estimated_fixes})
                    </button>
                `;

            case 'suggest_retranslation':
                return `
                    <button class="btn btn-outline-danger btn-sm mt-2"
                            onclick="novelDetails.retranslateChapter(${chapterInfo.chapter_number})"
                            title="Re-translate this chapter">
                        <i class="fas fa-redo me-1"></i>
                        Re-translate Chapter
                    </button>
                `;

            default:
                return `
                    <button class="btn btn-outline-secondary btn-sm mt-2" disabled>
                        <i class="fas fa-cog me-1"></i>
                        Manual Action Required
                    </button>
                `;
        }
    }

    /**
     * Apply name fixes for a chapter
     */
    async applyNameFixes(chapterNumber) {
        await this.applySpecificFixes(chapterNumber, ['name_dictionary'], 'name dictionary corrections');
    }

    /**
     * Apply formatting fixes for a chapter
     */
    async applyFormattingFixes(chapterNumber) {
        await this.applySpecificFixes(chapterNumber, ['formatting'], 'formatting corrections');
    }

    /**
     * Apply punctuation fixes for a chapter
     */
    async applyPunctuationFixes(chapterNumber) {
        await this.applySpecificFixes(chapterNumber, ['punctuation'], 'punctuation corrections');
    }

    /**
     * Apply specific types of fixes
     */
    async applySpecificFixes(chapterNumber, fixTypes, description) {
        const confirmed = confirm(`Apply ${description} to chapter ${chapterNumber}? This will modify the translated content.`);
        if (!confirmed) return;

        try {
            utils.showLoading(true, `Applying ${description}...`);

            // First get the current quality report to get corrections
            const qualityResponse = await utils.makeApiRequest('api/quality-check.php', {
                method: 'POST',
                body: JSON.stringify({
                    novel_id: this.novelId,
                    chapter_number: chapterNumber
                })
            });

            if (!qualityResponse.success) {
                throw new Error('Failed to get quality report: ' + qualityResponse.error);
            }

            // Apply the fixes
            const fixResponse = await utils.makeApiRequest('api/quality-check.php', {
                method: 'POST',
                body: JSON.stringify({
                    action: 'apply_fixes',
                    novel_id: this.novelId,
                    chapter_number: chapterNumber,
                    fix_types: fixTypes,
                    corrections: qualityResponse.quality_report.auto_corrections
                })
            });

            if (fixResponse.success) {
                utils.showToast(`Applied ${fixResponse.fixes_applied} ${description} successfully`, 'success');

                // Refresh the chapter list to show updated content
                await this.refreshChapters();
            } else {
                throw new Error(fixResponse.error || 'Failed to apply fixes');
            }

        } catch (error) {
            console.error('Apply fixes error:', error);
            utils.showToast('Failed to apply fixes: ' + error.message, 'error');
        } finally {
            utils.showLoading(false);
        }
    }

    /**
     * Apply all recommended fixes for a chapter
     */
    async applyAllRecommendedFixes(chapterNumber) {
        const confirmed = confirm(`Apply all recommended fixes to chapter ${chapterNumber}? This will modify the translated content with name dictionary, formatting, and punctuation corrections.`);
        if (!confirmed) return;

        await this.applySpecificFixes(chapterNumber, ['name_dictionary', 'formatting', 'punctuation'], 'all recommended fixes');
    }

    /**
     * Preview all fixes for a chapter
     */
    async previewAllFixes(chapterNumber) {
        try {
            utils.showLoading(true, 'Generating preview...');

            // Get the current quality report to get corrections
            const qualityResponse = await utils.makeApiRequest('api/quality-check.php', {
                method: 'POST',
                body: JSON.stringify({
                    novel_id: this.novelId,
                    chapter_number: chapterNumber
                })
            });

            if (!qualityResponse.success) {
                throw new Error('Failed to get quality report: ' + qualityResponse.error);
            }

            // Get preview of fixes
            const previewResponse = await utils.makeApiRequest('api/quality-check.php', {
                method: 'POST',
                body: JSON.stringify({
                    action: 'preview_fixes',
                    novel_id: this.novelId,
                    chapter_number: chapterNumber,
                    corrections: qualityResponse.quality_report.auto_corrections
                })
            });

            if (previewResponse.success) {
                this.showFixPreviewModal(chapterNumber, previewResponse.preview, qualityResponse.quality_report.auto_corrections);
            } else {
                throw new Error(previewResponse.error || 'Failed to generate preview');
            }

        } catch (error) {
            console.error('Preview fixes error:', error);
            utils.showToast('Failed to generate preview: ' + error.message, 'error');
        } finally {
            utils.showLoading(false);
        }
    }

    /**
     * Show fix preview modal
     */
    showFixPreviewModal(chapterNumber, preview, corrections) {
        const modalId = 'fixPreviewModal';

        // Remove existing modal if any
        const existingModal = document.getElementById(modalId);
        if (existingModal) {
            existingModal.remove();
        }

        const modalHtml = `
            <div class="modal fade" id="${modalId}" tabindex="-1" aria-labelledby="${modalId}Label" aria-hidden="true">
                <div class="modal-dialog modal-xl">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="${modalId}Label">
                                <i class="fas fa-eye me-2"></i>
                                Fix Preview - Chapter ${chapterNumber}
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                Preview of changes that will be applied. Review carefully before applying.
                            </div>

                            ${this.renderFixPreview(preview)}
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                            <button type="button" class="btn btn-success"
                                    onclick="novelDetails.applyPreviewedFixes(${chapterNumber}, '${btoa(JSON.stringify(corrections))}')">
                                <i class="fas fa-check me-1"></i>
                                Apply All Changes
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHtml);
        const modal = new bootstrap.Modal(document.getElementById(modalId));
        modal.show();
    }

    /**
     * Render fix preview content
     */
    renderFixPreview(preview) {
        if (!preview || preview.length === 0) {
            return '<div class="alert alert-success">No fixes needed - translation quality is good!</div>';
        }

        let html = '<div class="fix-preview-list">';

        preview.forEach((fix, index) => {
            const typeClass = fix.type === 'name_dictionary' ? 'primary' : fix.type === 'formatting' ? 'warning' : 'info';
            const confidenceClass = fix.confidence === 'high' ? 'success' : fix.confidence === 'medium' ? 'warning' : 'secondary';

            html += `
                <div class="card mb-3 border-${typeClass}">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h6 class="mb-0">
                                <span class="badge bg-${typeClass} me-2">${fix.type.replace('_', ' ')}</span>
                                ${fix.description}
                            </h6>
                            <span class="badge bg-${confidenceClass}">${fix.confidence} confidence</span>
                        </div>
                    </div>
                    ${fix.before && fix.after ? `
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6 class="text-danger">Before:</h6>
                                    <code class="text-danger">${utils.escapeHtml(fix.before)}</code>
                                </div>
                                <div class="col-md-6">
                                    <h6 class="text-success">After:</h6>
                                    <code class="text-success">${utils.escapeHtml(fix.after)}</code>
                                </div>
                            </div>
                        </div>
                    ` : ''}
                </div>
            `;
        });

        html += '</div>';
        return html;
    }

    /**
     * Apply previewed fixes
     */
    async applyPreviewedFixes(chapterNumber, encodedCorrections) {
        try {
            const corrections = JSON.parse(atob(encodedCorrections));

            utils.showLoading(true, 'Applying fixes...');

            const fixResponse = await utils.makeApiRequest('api/quality-check.php', {
                method: 'POST',
                body: JSON.stringify({
                    action: 'apply_fixes',
                    novel_id: this.novelId,
                    chapter_number: chapterNumber,
                    fix_types: ['name_dictionary', 'formatting', 'punctuation'],
                    corrections: corrections
                })
            });

            if (fixResponse.success) {
                utils.showToast(`Applied ${fixResponse.fixes_applied} fixes successfully`, 'success');

                // Close the preview modal
                const modal = bootstrap.Modal.getInstance(document.getElementById('fixPreviewModal'));
                if (modal) modal.hide();

                // Refresh the chapter list
                await this.refreshChapters();
            } else {
                throw new Error(fixResponse.error || 'Failed to apply fixes');
            }

        } catch (error) {
            console.error('Apply previewed fixes error:', error);
            utils.showToast('Failed to apply fixes: ' + error.message, 'error');
        } finally {
            utils.showLoading(false);
        }
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    if (typeof window.novelId !== 'undefined') {
        window.novelDetails = new NovelDetails(window.novelId);
    }
});
