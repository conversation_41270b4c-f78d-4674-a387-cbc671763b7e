<?php
/**
 * API Endpoint: Check for New Chapters
 * GET /api/check-new-chapters.php?novel_id=<id>
 */

// Set error handling to prevent HTML output
ini_set('display_errors', 0);
error_reporting(0);

require_once '../config/config.php';

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

/**
 * Clean data for JSON encoding
 */
function cleanDataForJson($data) {
    if (is_array($data)) {
        return array_map('cleanDataForJson', $data);
    } elseif (is_string($data)) {
        // Remove null bytes and fix encoding
        $cleaned = str_replace("\0", '', $data);
        if (!mb_check_encoding($cleaned, 'UTF-8')) {
            $cleaned = mb_convert_encoding($cleaned, 'UTF-8', 'auto');
        }
        return $cleaned;
    }
    return $data;
}

try {
    // Validate input
    if (!isset($_GET['novel_id']) || empty($_GET['novel_id'])) {
        jsonResponse(['error' => 'Novel ID parameter is required'], 400);
    }

    $novelId = (int)$_GET['novel_id'];
    if ($novelId <= 0) {
        jsonResponse(['error' => 'Invalid novel ID'], 400);
    }

    // Get novel information from database
    $db = Database::getInstance();
    $novel = $db->fetchOne(
        "SELECT * FROM novels WHERE id = ?",
        [$novelId]
    );

    if (!$novel) {
        jsonResponse(['error' => 'Novel not found'], 404);
    }

    // Validate platform support
    $supportedPlatforms = ['kakuyomu', 'syosetu', 'shuba69', 'dxmwx'];
    if (!in_array($novel['platform'], $supportedPlatforms)) {
        jsonResponse(['error' => 'Chapter checking is not supported for this platform'], 400);
    }

    // Get current chapters from database
    $currentChapters = $db->fetchAll(
        "SELECT chapter_number, original_title, chapter_url FROM chapters WHERE novel_id = ? ORDER BY chapter_number",
        [$novelId]
    );

    $currentChapterCount = count($currentChapters);
    $lastChapterNumber = $currentChapterCount > 0 ? max(array_column($currentChapters, 'chapter_number')) : 0;

    // Create appropriate crawler for the platform
    $novelManager = new NovelManager();
    $crawler = $novelManager->getCrawler($novel['platform']);
    
    try {
        $latestChapters = $crawler->getChapterList($novel['url']);
        $latestChapterCount = count($latestChapters);
        $latestLastChapterNumber = $latestChapterCount > 0 ? max(array_column($latestChapters, 'chapter_number')) : 0;

        // Compare chapters
        $newChaptersAvailable = $latestChapterCount > $currentChapterCount;
        $newChapterCount = max(0, $latestChapterCount - $currentChapterCount);

        // Find new chapters (chapters that exist in latest but not in current)
        $newChapters = [];
        if ($newChaptersAvailable) {
            $currentChapterNumbers = array_column($currentChapters, 'chapter_number');
            foreach ($latestChapters as $chapter) {
                if (!in_array($chapter['chapter_number'], $currentChapterNumbers)) {
                    $newChapters[] = [
                        'chapter_number' => $chapter['chapter_number'],
                        'original_title' => $chapter['original_title'],
                        'chapter_url' => $chapter['chapter_url']
                    ];
                }
            }
        }

        // Clean data for JSON response
        $responseData = cleanDataForJson([
            'success' => true,
            'novel_id' => $novelId,
            'novel_title' => $novel['original_title'],
            'platform' => $novel['platform'],
            'source_url' => $novel['url'],
            'check_timestamp' => date('Y-m-d H:i:s'),
            'current_chapters' => [
                'count' => $currentChapterCount,
                'last_chapter_number' => $lastChapterNumber
            ],
            'latest_chapters' => [
                'count' => $latestChapterCount,
                'last_chapter_number' => $latestLastChapterNumber
            ],
            'new_chapters_available' => $newChaptersAvailable,
            'new_chapter_count' => $newChapterCount,
            'new_chapters' => array_slice($newChapters, 0, 20), // Limit to first 20 new chapters for display
            'total_new_chapters' => count($newChapters)
        ]);

        jsonResponse($responseData);

    } catch (Exception $e) {
        logError('Check new chapters crawler error: ' . $e->getMessage(), [
            'novel_id' => $novelId,
            'novel_url' => $novel['url'],
            'trace' => $e->getTraceAsString()
        ]);

        jsonResponse([
            'success' => false,
            'error' => 'Failed to check for new chapters: ' . $e->getMessage(),
            'source_available' => false
        ], 500);
    }

} catch (Exception $e) {
    logError('Check new chapters API error: ' . $e->getMessage(), [
        'novel_id' => $_GET['novel_id'] ?? 'not provided',
        'trace' => $e->getTraceAsString()
    ]);

    jsonResponse([
        'success' => false,
        'error' => 'An error occurred while checking for new chapters'
    ], 500);
}
