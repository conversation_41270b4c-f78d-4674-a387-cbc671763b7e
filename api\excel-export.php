<?php
/**
 * API Endpoint: Excel Export for Name Dictionary
 * GET /api/excel-export.php - Export name dictionary data as Excel file
 */

// Set the correct path for includes
$rootPath = dirname(__DIR__);
require_once $rootPath . '/config/config.php';
require_once $rootPath . '/classes/ExcelImportService.php';

// Don't set JSON content type initially - we'll set appropriate headers based on success/failure

try {
    if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
        header('Content-Type: application/json');
        jsonResponse(['success' => false, 'error' => 'Method not allowed'], 405);
        exit;
    }

    // Get novel ID from query parameters
    $novelId = isset($_GET['novel_id']) ? (int)$_GET['novel_id'] : 0;
    
    if (!$novelId) {
        header('Content-Type: application/json');
        jsonResponse(['success' => false, 'error' => 'Novel ID is required'], 400);
        exit;
    }

    // Verify novel exists
    $db = Database::getInstance();
    $novel = $db->fetchOne("SELECT id, original_title FROM novels WHERE id = ?", [$novelId]);
    if (!$novel) {
        header('Content-Type: application/json');
        jsonResponse(['success' => false, 'error' => 'Novel not found'], 404);
        exit;
    }

    $excelService = new ExcelImportService();
    $result = $excelService->exportNameDictionary($novelId);

    if (!$result['success']) {
        header('Content-Type: application/json');
        jsonResponse(['success' => false, 'error' => $result['error']], 400);
        exit;
    }

    $filepath = $result['filepath'];
    $filename = $result['filename'];

    // Verify file exists and has content
    if (!file_exists($filepath) || filesize($filepath) === 0) {
        header('Content-Type: application/json');
        jsonResponse(['success' => false, 'error' => 'Failed to generate export file'], 500);
        exit;
    }

    // Log successful generation
    error_log("Excel Export Success: Novel ID {$novelId}, File: {$filename}, Count: {$result['count']}");

    // Set headers for Excel file download
    header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    header('Content-Length: ' . filesize($filepath));
    header('Cache-Control: must-revalidate');
    header('Pragma: public');

    // Clear any output buffers
    if (ob_get_level()) {
        ob_end_clean();
    }

    // Output the file
    readfile($filepath);

    // Clean up the temporary file after download
    unlink($filepath);

    // Clean up old temporary files (reuse logic from other services)
    $tempDir = APP_ROOT . '/temp/';
    if (is_dir($tempDir)) {
        $files = glob($tempDir . 'name_dictionary_export_*.xlsx');
        $cutoff = time() - (24 * 60 * 60); // 24 hours ago
        
        foreach ($files as $file) {
            if (filemtime($file) < $cutoff) {
                unlink($file);
            }
        }
    }

} catch (Exception $e) {
    error_log("Excel Export Error: " . $e->getMessage());
    
    header('Content-Type: application/json');
    jsonResponse(['success' => false, 'error' => 'Internal server error'], 500);
}
