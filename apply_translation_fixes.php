<?php
require_once 'config/config.php';

echo "=== APPLYING TRANSLATION FIXES TO CHAPTER 163 ===\n";
echo "Date: " . date('Y-m-d H:i:s') . "\n\n";

try {
    $db = Database::getInstance();
    $translationService = new TranslationService();
    $honorificService = new HonorificService();
    
    // Get Chapter 163
    $chapter = $db->fetchOne('SELECT * FROM chapters WHERE novel_id = 1 AND chapter_number = 163');
    
    if (!$chapter) {
        echo "ERROR: Chapter 163 not found for Novel ID 1\n";
        exit(1);
    }
    
    if (empty($chapter['translated_content'])) {
        echo "ERROR: Chapter 163 has no translated content\n";
        exit(1);
    }
    
    $originalContent = $chapter['translated_content'];
    
    echo "Original content length: " . strlen($originalContent) . " characters\n";
    
    // Count issues before fix
    preg_match_all('/-san\d+|-kun\d+|-chan\d+|-sama\d+/', $originalContent, $suffixMatches);
    preg_match_all('/」[^「]*「/', $originalContent, $punctuationMatches);
    
    echo "Issues found before fix:\n";
    echo "- Suffix issues: " . count($suffixMatches[0]) . "\n";
    echo "- Punctuation issues: " . count($punctuationMatches[0]) . "\n\n";
    
    // Apply fixes
    $fixedContent = $originalContent;
    
    // 1. Fix suffix issues using the honorific service approach
    echo "Applying suffix fixes...\n";
    
    // Create mock honorifics for the numbered suffixes found
    $mockHonorifics = [];
    $markerIndex = 0;
    
    foreach ($suffixMatches[0] as $match) {
        if (preg_match('/-(\w+)(\d+)/', $match, $parts)) {
            $honorificType = $parts[1];
            $number = $parts[2];
            $marker = "HONORIFIC_MARKER_{$number}";
            $mockHonorifics[$marker] = [
                'original' => $match,
                'romanized' => '-' . $honorificType
            ];
        }
    }
    
    // Apply honorific fixes
    $fixedContent = $honorificService->postprocessTranslatedText($fixedContent, $mockHonorifics);
    
    // 2. Fix punctuation issues
    echo "Applying punctuation fixes...\n";
    
    // Use reflection to access private method
    $reflection = new ReflectionClass($translationService);
    $fixPunctuationMethod = $reflection->getMethod('fixPunctuationIssues');
    $fixPunctuationMethod->setAccessible(true);
    
    $fixedContent = $fixPunctuationMethod->invoke($translationService, $originalContent, $fixedContent);
    
    // Count issues after fix
    preg_match_all('/-san\d+|-kun\d+|-chan\d+|-sama\d+/', $fixedContent, $suffixMatchesAfter);
    preg_match_all('/」[^「]*「/', $fixedContent, $punctuationMatchesAfter);
    
    echo "\nIssues found after fix:\n";
    echo "- Suffix issues: " . count($suffixMatchesAfter[0]) . "\n";
    echo "- Punctuation issues: " . count($punctuationMatchesAfter[0]) . "\n\n";
    
    // Show improvement
    $suffixImprovement = count($suffixMatches[0]) - count($suffixMatchesAfter[0]);
    $punctuationImprovement = count($punctuationMatches[0]) - count($punctuationMatchesAfter[0]);
    
    echo "Improvements:\n";
    echo "- Suffix issues fixed: {$suffixImprovement}\n";
    echo "- Punctuation issues fixed: {$punctuationImprovement}\n\n";
    
    if ($suffixImprovement > 0 || $punctuationImprovement > 0) {
        echo "✓ Fixes successful! Updating database...\n";
        
        // Update the chapter in the database
        $updateResult = $db->update(
            'chapters',
            ['translated_content' => $fixedContent],
            'id = ?',
            [$chapter['id']]
        );
        
        if ($updateResult) {
            echo "✓ Chapter 163 updated successfully in database!\n";
        } else {
            echo "✗ Failed to update chapter in database\n";
        }
        
        // Show sample of fixed content
        echo "\nSample of fixed content (first 800 characters):\n";
        echo "=" . str_repeat("=", 60) . "\n";
        echo substr($fixedContent, 0, 800) . "\n";
        echo "=" . str_repeat("=", 60) . "\n";
        
    } else {
        echo "⚠ No improvements detected. Fixes may need adjustment.\n";
    }
    
} catch (Exception $e) {
    echo "ERROR: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

echo "\n=== PROCESS COMPLETE ===\n";
?>
