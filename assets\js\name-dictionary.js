/**
 * Name Dictionary Management JavaScript
 * Handles bulk operations and advanced name dictionary management
 */

class NameDictionaryManager {
    constructor() {
        this.novelId = window.novelId;
        this.names = [];
        this.filteredNames = [];
        this.selectedNames = new Set();
        this.currentPage = 1;
        this.itemsPerPage = 50;
        this.totalPages = 1;
        this.searchTerm = '';
        this.typeFilter = '';
        this.statusFilter = '';
        this.searchTimeout = null;

        // Validate novel ID
        if (!this.novelId) {
            console.error('NameDictionaryManager: No novel ID provided');
            utils.showToast('Error: No novel ID provided', 'error');
            return;
        }

        this.init();
    }

    init() {
        try {
            this.setupEventListeners();
            this.loadNames();
        } catch (error) {
            console.error('NameDictionaryManager: Initialization error:', error);
            utils.showToast('Failed to initialize name dictionary', 'error');
        }
    }

    setupEventListeners() {
        // Add name form
        const addNameForm = document.getElementById('add-name-form-element');
        if (addNameForm) {
            addNameForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleAddName();
            });
        }

        // Search input
        const searchInput = document.getElementById('search-names');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                clearTimeout(this.searchTimeout);
                this.searchTimeout = setTimeout(() => {
                    this.searchTerm = e.target.value.trim();
                    this.currentPage = 1;
                    this.applyFilters();
                }, 300);
            });
        }

        // Filter selects
        const filterType = document.getElementById('filter-type');
        if (filterType) {
            filterType.addEventListener('change', (e) => {
                this.typeFilter = e.target.value;
                this.currentPage = 1;
                this.applyFilters();
            });
        }

        const filterStatus = document.getElementById('filter-status');
        if (filterStatus) {
            filterStatus.addEventListener('change', (e) => {
                this.statusFilter = e.target.value;
                this.currentPage = 1;
                this.applyFilters();
            });
        }

        const itemsPerPage = document.getElementById('items-per-page');
        if (itemsPerPage) {
            itemsPerPage.addEventListener('change', (e) => {
                this.itemsPerPage = e.target.value === 'all' ? 'all' : parseInt(e.target.value);
                this.currentPage = 1;
                this.applyFilters();
            });
        }

        // Select all checkbox
        const selectAllCheckbox = document.getElementById('select-all-checkbox');
        if (selectAllCheckbox) {
            selectAllCheckbox.addEventListener('change', (e) => {
                this.toggleSelectAll();
            });
        }
    }

    async loadNames() {
        try {
            // Ensure utils is available
            if (typeof utils === 'undefined') {
                throw new Error('Utils object not available');
            }

            utils.showLoading(true);

            const result = await utils.makeApiRequest(`api/name-dictionary.php?novel_id=${this.novelId}`);

            if (result.success) {
                this.names = result.data.names || [];
                this.updateTotalBadge();
                this.applyFilters();
            } else {
                utils.showToast(result.error || 'Failed to load names', 'error');
            }
        } catch (error) {
            console.error('Load names error:', error);
            if (typeof utils !== 'undefined') {
                utils.showToast('Network error occurred', 'error');
            } else {
                alert('Error: Failed to load names. Utils not available.');
            }
        } finally {
            if (typeof utils !== 'undefined') {
                utils.showLoading(false);
            }
        }
    }

    applyFilters() {
        let filtered = [...this.names];

        // Apply search filter
        if (this.searchTerm) {
            const searchLower = this.searchTerm.toLowerCase();
            filtered = filtered.filter(name => 
                name.original_name.toLowerCase().includes(searchLower) ||
                (name.romanization && name.romanization.toLowerCase().includes(searchLower)) ||
                (name.translation && name.translation.toLowerCase().includes(searchLower))
            );
        }

        // Apply type filter
        if (this.typeFilter) {
            filtered = filtered.filter(name => name.name_type === this.typeFilter);
        }

        // Apply status filter
        if (this.statusFilter) {
            switch (this.statusFilter) {
                case 'translated':
                    filtered = filtered.filter(name => name.translation && name.translation.trim());
                    break;
                case 'romanized':
                    filtered = filtered.filter(name => name.romanization && name.romanization.trim());
                    break;
                case 'untranslated':
                    filtered = filtered.filter(name => !name.translation || !name.translation.trim());
                    break;
                case 'unromanized':
                    filtered = filtered.filter(name => !name.romanization || !name.romanization.trim());
                    break;
            }
        }

        this.filteredNames = filtered;
        this.calculatePagination();
        this.renderTable();
        this.renderPagination();
    }

    calculatePagination() {
        if (this.itemsPerPage === 'all') {
            this.totalPages = 1;
            this.currentPage = 1;
        } else {
            this.totalPages = Math.ceil(this.filteredNames.length / this.itemsPerPage);
            if (this.currentPage > this.totalPages) {
                this.currentPage = Math.max(1, this.totalPages);
            }
        }
    }

    renderTable() {
        const tbody = document.getElementById('names-table-body');
        
        if (this.filteredNames.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="8" class="text-center py-4">
                        <i class="fas fa-search text-muted mb-2" style="font-size: 2rem;"></i>
                        <p class="mb-0">No names found matching your criteria</p>
                    </td>
                </tr>
            `;
            return;
        }

        let startIndex = 0;
        let endIndex = this.filteredNames.length;

        if (this.itemsPerPage !== 'all') {
            startIndex = (this.currentPage - 1) * this.itemsPerPage;
            endIndex = Math.min(startIndex + this.itemsPerPage, this.filteredNames.length);
        }

        const pageNames = this.filteredNames.slice(startIndex, endIndex);
        
        tbody.innerHTML = pageNames.map(name => this.renderNameRow(name)).join('');
        this.updateSelectedBadge();
    }

    renderNameRow(name) {
        const isSelected = this.selectedNames.has(name.id);
        const hasTranslation = name.translation && name.translation.trim();
        const hasRomanization = name.romanization && name.romanization.trim();

        return `
            <tr class="${isSelected ? 'table-active' : ''}" data-name-id="${name.id}">
                <td>
                    <input type="checkbox" class="form-check-input name-checkbox" 
                           value="${name.id}" ${isSelected ? 'checked' : ''}
                           onchange="nameDictionary.toggleNameSelection(${name.id})">
                </td>
                <td>
                    <strong>${utils.escapeHtml(name.original_name)}</strong>
                </td>
                <td>
                    <select class="form-select form-select-sm" 
                            onchange="nameDictionary.updateNameType(${name.id}, this.value)">
                        <option value="character" ${name.name_type === 'character' ? 'selected' : ''}>Character</option>
                        <option value="location" ${name.name_type === 'location' ? 'selected' : ''}>Location</option>
                        <option value="organization" ${name.name_type === 'organization' ? 'selected' : ''}>Organization</option>
                        <option value="country" ${name.name_type === 'country' ? 'selected' : ''}>Country</option>
                        <option value="skill" ${name.name_type === 'skill' ? 'selected' : ''}>Skill</option>
                        <option value="monster" ${name.name_type === 'monster' ? 'selected' : ''}>Monster</option>
                        <option value="item" ${name.name_type === 'item' ? 'selected' : ''}>Item</option>
                        <option value="class/profession" ${name.name_type === 'class/profession' ? 'selected' : ''}>Class/Profession</option>
                        <option value="other" ${name.name_type === 'other' ? 'selected' : ''}>Other</option>
                    </select>
                </td>
                <td>
                    <div class="input-group input-group-sm">
                        <input type="text" class="form-control" 
                               value="${utils.escapeHtml(name.romanization || '')}"
                               onchange="nameDictionary.updateRomanization(${name.id}, this.value)"
                               placeholder="Enter romanization">
                        <button class="btn btn-outline-secondary" type="button"
                                onclick="nameDictionary.generateRomanization(${name.id})"
                                title="Generate romanization">
                            <i class="fas fa-magic"></i>
                        </button>
                    </div>
                </td>
                <td>
                    <input type="text" class="form-control form-control-sm" 
                           value="${utils.escapeHtml(name.translation || '')}"
                           onchange="nameDictionary.updateTranslation(${name.id}, this.value)"
                           placeholder="Enter translation">
                </td>
                <td class="text-center">
                    <span class="badge bg-secondary">${name.frequency}</span>
                </td>
                <td class="text-center">
                    <small class="text-muted">${name.first_appearance_chapter || 'N/A'}</small>
                </td>
                <td>
                    <button class="btn btn-outline-danger btn-sm" 
                            onclick="nameDictionary.deleteName(${name.id})"
                            title="Delete name">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            </tr>
        `;
    }

    renderPagination() {
        const container = document.getElementById('pagination-container');
        
        if (this.totalPages <= 1) {
            container.innerHTML = '';
            return;
        }

        const startItem = ((this.currentPage - 1) * this.itemsPerPage) + 1;
        const endItem = Math.min(startItem + this.itemsPerPage - 1, this.filteredNames.length);

        let paginationHtml = `
            <div class="d-flex justify-content-between align-items-center">
                <small class="text-muted">
                    Showing ${startItem}-${endItem} of ${this.filteredNames.length} entries
                </small>
                <nav aria-label="Name dictionary pagination">
                    <ul class="pagination pagination-sm mb-0">
        `;

        // Previous button
        paginationHtml += `
            <li class="page-item ${this.currentPage === 1 ? 'disabled' : ''}">
                <button class="page-link" onclick="nameDictionary.goToPage(${this.currentPage - 1})"
                        ${this.currentPage === 1 ? 'disabled' : ''}>
                    Previous
                </button>
            </li>
        `;

        // Page numbers
        const startPage = Math.max(1, this.currentPage - 2);
        const endPage = Math.min(this.totalPages, this.currentPage + 2);

        for (let i = startPage; i <= endPage; i++) {
            paginationHtml += `
                <li class="page-item ${i === this.currentPage ? 'active' : ''}">
                    <button class="page-link" onclick="nameDictionary.goToPage(${i})">${i}</button>
                </li>
            `;
        }

        // Next button
        paginationHtml += `
            <li class="page-item ${this.currentPage === this.totalPages ? 'disabled' : ''}">
                <button class="page-link" onclick="nameDictionary.goToPage(${this.currentPage + 1})"
                        ${this.currentPage === this.totalPages ? 'disabled' : ''}>
                    Next
                </button>
            </li>
        `;

        paginationHtml += `
                    </ul>
                </nav>
            </div>
        `;

        container.innerHTML = paginationHtml;
    }

    goToPage(page) {
        if (page >= 1 && page <= this.totalPages && page !== this.currentPage) {
            this.currentPage = page;
            this.renderTable();
            this.renderPagination();
        }
    }

    updateTotalBadge() {
        document.getElementById('total-names-badge').textContent = `${this.names.length} names`;
    }

    updateSelectedBadge() {
        const badge = document.getElementById('selected-names-badge');
        const bulkActions = document.getElementById('bulk-actions');
        
        if (this.selectedNames.size > 0) {
            badge.textContent = `${this.selectedNames.size} selected`;
            badge.style.display = 'inline-block';
            bulkActions.style.display = 'block';
        } else {
            badge.style.display = 'none';
            bulkActions.style.display = 'none';
        }
    }

    toggleNameSelection(nameId) {
        if (this.selectedNames.has(nameId)) {
            this.selectedNames.delete(nameId);
        } else {
            this.selectedNames.add(nameId);
        }
        this.updateSelectedBadge();
        this.updateSelectAllCheckbox();
    }

    toggleSelectAll() {
        const checkbox = document.getElementById('select-all-checkbox');
        const currentPageNames = this.getCurrentPageNames();
        
        if (checkbox.checked) {
            // Select all names on current page
            currentPageNames.forEach(name => this.selectedNames.add(name.id));
        } else {
            // Deselect all names on current page
            currentPageNames.forEach(name => this.selectedNames.delete(name.id));
        }
        
        this.renderTable();
        this.updateSelectedBadge();
    }

    getCurrentPageNames() {
        if (this.itemsPerPage === 'all') {
            return this.filteredNames;
        }
        
        const startIndex = (this.currentPage - 1) * this.itemsPerPage;
        const endIndex = Math.min(startIndex + this.itemsPerPage, this.filteredNames.length);
        return this.filteredNames.slice(startIndex, endIndex);
    }

    updateSelectAllCheckbox() {
        const checkbox = document.getElementById('select-all-checkbox');
        const currentPageNames = this.getCurrentPageNames();
        const selectedOnPage = currentPageNames.filter(name => this.selectedNames.has(name.id));
        
        if (selectedOnPage.length === 0) {
            checkbox.checked = false;
            checkbox.indeterminate = false;
        } else if (selectedOnPage.length === currentPageNames.length) {
            checkbox.checked = true;
            checkbox.indeterminate = false;
        } else {
            checkbox.checked = false;
            checkbox.indeterminate = true;
        }
    }

    clearSearch() {
        document.getElementById('search-names').value = '';
        this.searchTerm = '';
        this.currentPage = 1;
        this.applyFilters();
    }

    async handleAddName() {
        const originalName = document.getElementById('new-original-name').value.trim();
        const romanization = document.getElementById('new-romanization').value.trim();
        const translation = document.getElementById('new-translation').value.trim();
        const nameType = document.getElementById('new-name-type').value;

        // Validate required fields
        if (!originalName) {
            utils.showToast('Original name is required', 'error');
            document.getElementById('new-original-name').focus();
            return;
        }

        if (!nameType) {
            utils.showToast('Name type is required', 'error');
            document.getElementById('new-name-type').focus();
            return;
        }

        // Check for duplicate names in local data
        const existingName = this.names.find(name =>
            name.original_name.toLowerCase() === originalName.toLowerCase()
        );

        if (existingName) {
            utils.showToast('A name with this original text already exists in the dictionary', 'error');
            document.getElementById('new-original-name').focus();
            return;
        }

        try {
            // Show loading state
            const submitButton = document.querySelector('#add-name-form-element button[type="submit"]');
            const originalButtonText = submitButton.innerHTML;
            submitButton.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Adding...';
            submitButton.disabled = true;

            const result = await utils.makeApiRequest('api/name-dictionary.php', {
                method: 'POST',
                body: JSON.stringify({
                    novel_id: this.novelId,
                    original_name: originalName,
                    romanization: romanization || null,
                    translation: translation || null,
                    name_type: nameType
                })
            });

            if (result.success) {
                // Clear form
                document.getElementById('add-name-form-element').reset();

                // Reload names to get the new entry
                await this.loadNames();

                utils.showToast('Name added successfully', 'success');
            } else {
                utils.showToast(result.error || 'Failed to add name', 'error');
            }
        } catch (error) {
            console.error('Add name error:', error);
            utils.showToast('Network error occurred', 'error');
        } finally {
            // Restore button state
            const submitButton = document.querySelector('#add-name-form-element button[type="submit"]');
            submitButton.innerHTML = '<i class="fas fa-plus me-1"></i>Add';
            submitButton.disabled = false;
        }
    }

    async downloadTemplate() {
        try {
            // Show loading state
            const button = document.querySelector('button[onclick="nameDictionary.downloadTemplate()"]');
            const originalText = button.innerHTML;
            button.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Downloading...';
            button.disabled = true;

            // Use fetch to get the file
            const response = await fetch('api/excel-template.php', {
                method: 'GET',
                headers: {
                    'Accept': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            // Get the filename from the Content-Disposition header
            const contentDisposition = response.headers.get('Content-Disposition');
            let filename = 'name_dictionary_template.xlsx';
            if (contentDisposition) {
                const filenameMatch = contentDisposition.match(/filename="(.+)"/);
                if (filenameMatch) {
                    filename = filenameMatch[1];
                }
            }

            // Get the blob data
            const blob = await response.blob();

            // Create download link
            const url = window.URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = filename;
            link.style.display = 'none';

            // Trigger download
            document.body.appendChild(link);
            link.click();

            // Clean up
            document.body.removeChild(link);
            window.URL.revokeObjectURL(url);

            utils.showToast('Excel template downloaded successfully', 'success');

        } catch (error) {
            console.error('Download template error:', error);
            utils.showToast('Failed to download template: ' + error.message, 'error');
        } finally {
            // Restore button state
            const button = document.querySelector('button[onclick="nameDictionary.downloadTemplate()"]');
            if (button) {
                button.innerHTML = '<i class="fas fa-download me-1"></i>Download Template';
                button.disabled = false;
            }
        }
    }

    async importExcel() {
        const fileInput = document.getElementById('excel-file');
        const file = fileInput.files[0];

        if (!file) {
            utils.showToast('Please select an Excel file to import', 'error');
            fileInput.focus();
            return;
        }

        // Validate file type
        const allowedTypes = [
            'application/vnd.ms-excel',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        ];
        const allowedExtensions = ['xls', 'xlsx'];
        const fileExtension = file.name.split('.').pop().toLowerCase();

        if (!allowedTypes.includes(file.type) && !allowedExtensions.includes(fileExtension)) {
            utils.showToast('Invalid file type. Please select .xls or .xlsx files only.', 'error');
            fileInput.focus();
            return;
        }

        // Validate file size (10MB)
        if (file.size > 10 * 1024 * 1024) {
            utils.showToast('File is too large. Maximum size is 10MB.', 'error');
            fileInput.focus();
            return;
        }

        try {
            this.showImportProgress(true, 'Uploading file...');

            const formData = new FormData();
            formData.append('excel_file', file);
            formData.append('novel_id', this.novelId);

            // Debug: Log what we're sending
            console.log('Excel Import Debug - Novel ID:', this.novelId);
            console.log('Excel Import Debug - File:', file);
            console.log('Excel Import Debug - FormData entries:');
            for (let [key, value] of formData.entries()) {
                console.log(`  ${key}:`, value);
            }

            const result = await utils.makeApiRequest('api/excel-import.php', {
                method: 'POST',
                body: formData
            });

            if (result.success) {
                this.showImportProgress(false);

                if (result.data.auto_imported) {
                    // All entries were imported automatically
                    this.showImportSummary(result.data);
                    await this.loadNames(); // Refresh the list
                    fileInput.value = ''; // Clear file input
                } else {
                    // Show duplicate resolution interface
                    this.showDuplicateResolution(result.data);
                }
            } else {
                this.showImportProgress(false);
                utils.showToast(result.error || 'Failed to import Excel file', 'error');
            }
        } catch (error) {
            console.error('Import Excel error:', error);
            this.showImportProgress(false);
            utils.showToast('Network error occurred during import', 'error');
        }
    }

    showImportProgress(show, message = 'Processing...') {
        const progressDiv = document.getElementById('import-progress');
        const statusText = document.getElementById('import-status');

        if (show) {
            progressDiv.style.display = 'block';
            statusText.textContent = message;

            // Animate progress bar
            const progressBar = progressDiv.querySelector('.progress-bar');
            progressBar.style.width = '100%';
        } else {
            progressDiv.style.display = 'none';

            // Reset progress bar
            const progressBar = progressDiv.querySelector('.progress-bar');
            progressBar.style.width = '0%';
        }
    }

    showImportSummary(data) {
        let message = `Import completed!\n\n`;
        message += `✅ Imported: ${data.imported_count} names\n`;

        if (data.duplicate_count > 0) {
            message += `⚠️ Duplicates skipped: ${data.duplicate_count}\n`;
        }

        if (data.error_count > 0) {
            message += `❌ Errors: ${data.error_count}\n`;
        }

        if (data.errors && data.errors.length > 0) {
            message += `\nErrors:\n`;
            data.errors.slice(0, 5).forEach(error => {
                message += `- Row ${error.row}: ${error.error}\n`;
            });
            if (data.errors.length > 5) {
                message += `... and ${data.errors.length - 5} more errors\n`;
            }
        }

        alert(message);
        utils.showToast(`Successfully imported ${data.imported_count} names`, 'success');
    }

    showDuplicateResolution(data) {
        // Create modal for duplicate resolution
        const modalHtml = `
            <div class="modal fade" id="duplicateResolutionModal" tabindex="-1" aria-hidden="true">
                <div class="modal-dialog modal-xl">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-exclamation-triangle text-warning me-2"></i>
                                Resolve Duplicate Entries
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="alert alert-info">
                                <strong>Import Summary:</strong><br>
                                ✅ Ready to import: ${data.valid_count} names<br>
                                ⚠️ Duplicates found: ${data.duplicate_count} names<br>
                                ❌ Errors: ${data.error_count} names
                            </div>

                            ${data.error_count > 0 ? this.renderErrorEntries(data.errors) : ''}
                            ${data.duplicate_count > 0 ? this.renderDuplicateEntries(data.duplicates) : ''}
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                            <button type="button" class="btn btn-primary" onclick="nameDictionary.processDuplicateResolution(${JSON.stringify(data).replace(/"/g, '&quot;')})">
                                <i class="fas fa-check me-1"></i>
                                Process Import
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Remove existing modal if any
        const existingModal = document.getElementById('duplicateResolutionModal');
        if (existingModal) {
            existingModal.remove();
        }

        // Add modal to page
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // Show modal
        const modal = new bootstrap.Modal(document.getElementById('duplicateResolutionModal'));
        modal.show();
    }

    renderErrorEntries(errors) {
        if (!errors || errors.length === 0) return '';

        let html = `
            <div class="mb-4">
                <h6 class="text-danger"><i class="fas fa-times-circle me-1"></i>Errors (${errors.length})</h6>
                <div class="table-responsive">
                    <table class="table table-sm table-bordered">
                        <thead class="table-light">
                            <tr>
                                <th>Row</th>
                                <th>Error</th>
                                <th>Data</th>
                            </tr>
                        </thead>
                        <tbody>
        `;

        errors.slice(0, 10).forEach(error => {
            html += `
                <tr>
                    <td>${error.row}</td>
                    <td class="text-danger">${utils.escapeHtml(error.error)}</td>
                    <td><small>${utils.escapeHtml(JSON.stringify(error.data))}</small></td>
                </tr>
            `;
        });

        if (errors.length > 10) {
            html += `
                <tr>
                    <td colspan="3" class="text-center text-muted">
                        ... and ${errors.length - 10} more errors
                    </td>
                </tr>
            `;
        }

        html += `
                        </tbody>
                    </table>
                </div>
            </div>
        `;

        return html;
    }

    renderDuplicateEntries(duplicates) {
        if (!duplicates || duplicates.length === 0) return '';

        let html = `
            <div class="mb-4">
                <h6 class="text-warning"><i class="fas fa-exclamation-triangle me-1"></i>Duplicate Entries (${duplicates.length})</h6>
                <div class="mb-2">
                    <button type="button" class="btn btn-sm btn-outline-secondary me-2" onclick="nameDictionary.selectAllDuplicates('skip')">
                        <i class="fas fa-times me-1"></i>Skip All
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-warning me-2" onclick="nameDictionary.selectAllDuplicates('update')">
                        <i class="fas fa-edit me-1"></i>Update All
                    </button>
                </div>
                <div class="table-responsive">
                    <table class="table table-sm table-bordered">
                        <thead class="table-light">
                            <tr>
                                <th>Row</th>
                                <th>Original Name</th>
                                <th>Existing Entry</th>
                                <th>New Entry</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody>
        `;

        duplicates.forEach((duplicate, index) => {
            const existing = duplicate.existing;
            const newEntry = duplicate.new;

            html += `
                <tr>
                    <td>${duplicate.row}</td>
                    <td><strong>${utils.escapeHtml(existing.original_name)}</strong></td>
                    <td>
                        <small>
                            <strong>Type:</strong> ${existing.name_type}<br>
                            <strong>Romanization:</strong> ${existing.romanization || 'None'}<br>
                            <strong>Translation:</strong> ${existing.translation || 'None'}<br>
                            <strong>Frequency:</strong> ${existing.frequency}
                        </small>
                    </td>
                    <td>
                        <small>
                            <strong>Type:</strong> ${newEntry.name_type}<br>
                            <strong>Romanization:</strong> ${newEntry.romanization || 'None'}<br>
                            <strong>Translation:</strong> ${newEntry.translation || 'None'}
                        </small>
                    </td>
                    <td>
                        <select class="form-select form-select-sm duplicate-action" data-index="${index}">
                            <option value="skip" selected>Skip</option>
                            <option value="update">Update Existing</option>
                        </select>
                    </td>
                </tr>
            `;
        });

        html += `
                        </tbody>
                    </table>
                </div>
            </div>
        `;

        return html;
    }

    selectAllDuplicates(action) {
        const selects = document.querySelectorAll('.duplicate-action');
        selects.forEach(select => {
            select.value = action;
        });
    }

    async processDuplicateResolution(data) {
        try {
            // Collect duplicate actions
            const duplicateActions = [];
            const selects = document.querySelectorAll('.duplicate-action');

            selects.forEach((select, index) => {
                const action = select.value;
                const duplicate = data.duplicates[index];

                if (action === 'update') {
                    duplicateActions.push({
                        action: 'update',
                        data: {
                            existing_id: duplicate.existing.id,
                            original_name: duplicate.new.original_name,
                            romanization: duplicate.new.romanization,
                            translation: duplicate.new.translation,
                            name_type: duplicate.new.name_type
                        }
                    });
                } else {
                    duplicateActions.push({
                        action: 'skip',
                        data: duplicate.new
                    });
                }
            });

            // Show loading
            utils.showLoading(true);

            // Send resolution to server
            const result = await utils.makeApiRequest('api/excel-import.php', {
                method: 'PUT',
                body: JSON.stringify({
                    novel_id: this.novelId,
                    valid_entries: data.valid_entries,
                    duplicate_actions: duplicateActions
                })
            });

            if (result.success) {
                // Close modal
                const modal = bootstrap.Modal.getInstance(document.getElementById('duplicateResolutionModal'));
                modal.hide();

                // Show summary
                this.showFinalImportSummary(result.data);

                // Refresh the names list
                await this.loadNames();

                // Clear file input
                document.getElementById('excel-file').value = '';
            } else {
                utils.showToast(result.error || 'Failed to process import', 'error');
            }
        } catch (error) {
            console.error('Process duplicate resolution error:', error);
            utils.showToast('Network error occurred', 'error');
        } finally {
            utils.showLoading(false);
        }
    }

    showFinalImportSummary(data) {
        let message = `Import completed!\n\n`;
        message += `✅ Imported: ${data.imported_count} names\n`;

        if (data.updated_count > 0) {
            message += `🔄 Updated: ${data.updated_count} names\n`;
        }

        if (data.skipped_count > 0) {
            message += `⏭️ Skipped: ${data.skipped_count} names\n`;
        }

        if (data.errors && data.errors.length > 0) {
            message += `❌ Errors: ${data.errors.length}\n`;
            message += `\nErrors:\n`;
            data.errors.slice(0, 5).forEach(error => {
                message += `- ${error.name}: ${error.error}\n`;
            });
            if (data.errors.length > 5) {
                message += `... and ${data.errors.length - 5} more errors\n`;
            }
        }

        alert(message);

        const totalProcessed = data.imported_count + data.updated_count;
        if (totalProcessed > 0) {
            utils.showToast(`Successfully processed ${totalProcessed} names`, 'success');
        }
    }

    // Individual update methods
    async updateNameType(nameId, nameType) {
        try {
            const result = await utils.makeApiRequest('api/names.php', {
                method: 'PUT',
                body: JSON.stringify({
                    novel_id: this.novelId,
                    name_id: nameId,
                    name_type: nameType
                })
            });

            if (result.success) {
                // Update local data
                const name = this.names.find(n => n.id === nameId);
                if (name) {
                    name.name_type = nameType;
                }
                utils.showToast('Name type updated', 'success');
            } else {
                utils.showToast(result.error || 'Failed to update name type', 'error');
            }
        } catch (error) {
            console.error('Update name type error:', error);
            utils.showToast('Network error occurred', 'error');
        }
    }

    async updateRomanization(nameId, romanization) {
        try {
            const result = await utils.makeApiRequest('api/names.php', {
                method: 'PUT',
                body: JSON.stringify({
                    novel_id: this.novelId,
                    name_id: nameId,
                    romanization: romanization
                })
            });

            if (result.success) {
                // Update local data
                const name = this.names.find(n => n.id === nameId);
                if (name) {
                    name.romanization = romanization;
                }
                utils.showToast('Romanization updated', 'success');
            } else {
                utils.showToast(result.error || 'Failed to update romanization', 'error');
            }
        } catch (error) {
            console.error('Update romanization error:', error);
            utils.showToast('Network error occurred', 'error');
        }
    }

    async updateTranslation(nameId, translation) {
        try {
            const result = await utils.makeApiRequest('api/names.php', {
                method: 'PUT',
                body: JSON.stringify({
                    novel_id: this.novelId,
                    name_id: nameId,
                    translation: translation
                })
            });

            if (result.success) {
                // Update local data
                const name = this.names.find(n => n.id === nameId);
                if (name) {
                    name.translation = translation;
                }
                utils.showToast('Translation updated', 'success');
            } else {
                utils.showToast(result.error || 'Failed to update translation', 'error');
            }
        } catch (error) {
            console.error('Update translation error:', error);
            utils.showToast('Network error occurred', 'error');
        }
    }

    async generateRomanization(nameId) {
        const name = this.names.find(n => n.id === nameId);
        if (!name) return;

        const button = document.querySelector(`[data-name-id="${nameId}"] .btn-outline-secondary`);
        const originalIcon = button.innerHTML;

        // Show loading state
        button.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
        button.disabled = true;

        try {
            const result = await utils.makeApiRequest('api/names.php', {
                method: 'POST',
                body: JSON.stringify({
                    novel_id: this.novelId,
                    name_id: nameId,
                    action: 'romanize'
                })
            });

            if (result.success) {
                // Update local data and UI
                name.romanization = result.data.romanization;
                const input = document.querySelector(`[data-name-id="${nameId}"] input[type="text"]`);
                if (input) {
                    input.value = result.data.romanization;
                }
                utils.showToast('Romanization generated', 'success');
            } else {
                utils.showToast(result.error || 'Failed to generate romanization', 'error');
            }
        } catch (error) {
            console.error('Generate romanization error:', error);
            utils.showToast('Network error occurred', 'error');
        } finally {
            // Restore button state
            button.innerHTML = originalIcon;
            button.disabled = false;
        }
    }

    async deleteName(nameId) {
        const name = this.names.find(n => n.id === nameId);
        if (!name) return;

        const confirmed = confirm(`Delete name "${name.original_name}"?`);
        if (!confirmed) return;

        try {
            const result = await utils.makeApiRequest('api/names.php', {
                method: 'DELETE',
                body: JSON.stringify({
                    novel_id: this.novelId,
                    name_id: nameId
                })
            });

            if (result.success) {
                // Remove from local data
                this.names = this.names.filter(n => n.id !== nameId);
                this.selectedNames.delete(nameId);
                this.updateTotalBadge();
                this.applyFilters();
                utils.showToast('Name deleted successfully', 'success');
            } else {
                utils.showToast(result.error || 'Failed to delete name', 'error');
            }
        } catch (error) {
            console.error('Delete name error:', error);
            utils.showToast('Network error occurred', 'error');
        }
    }

    // Bulk operation methods
    async bulkUpdateType() {
        const selectedType = document.getElementById('bulk-type-select').value;
        if (!selectedType) {
            utils.showToast('Please select a type', 'warning');
            return;
        }

        if (this.selectedNames.size === 0) {
            utils.showToast('Please select names to update', 'warning');
            return;
        }

        const confirmed = confirm(`Set type to "${selectedType}" for ${this.selectedNames.size} selected names?`);
        if (!confirmed) return;

        try {
            utils.showLoading(true);

            const result = await utils.makeApiRequest('api/name-dictionary.php', {
                method: 'PUT',
                body: JSON.stringify({
                    novel_id: this.novelId,
                    action: 'bulk_update_type',
                    name_ids: Array.from(this.selectedNames),
                    name_type: selectedType
                })
            });

            if (result.success) {
                // Update local data
                this.names.forEach(name => {
                    if (this.selectedNames.has(name.id)) {
                        name.name_type = selectedType;
                    }
                });
                this.applyFilters();
                utils.showToast(`Updated ${result.data.updated_count} names`, 'success');
            } else {
                utils.showToast(result.error || 'Failed to update names', 'error');
            }
        } catch (error) {
            console.error('Bulk update type error:', error);
            utils.showToast('Network error occurred', 'error');
        } finally {
            utils.showLoading(false);
        }
    }

    async bulkGenerateRomanization() {
        if (this.selectedNames.size === 0) {
            utils.showToast('Please select names to generate romanization for', 'warning');
            return;
        }

        const confirmed = confirm(`Generate romanization for ${this.selectedNames.size} selected names?`);
        if (!confirmed) return;

        try {
            utils.showLoading(true);

            const result = await utils.makeApiRequest('api/name-dictionary.php', {
                method: 'PUT',
                body: JSON.stringify({
                    novel_id: this.novelId,
                    action: 'bulk_romanize',
                    name_ids: Array.from(this.selectedNames)
                })
            });

            if (result.success) {
                // Update local data
                if (result.data.romanizations) {
                    result.data.romanizations.forEach(item => {
                        const name = this.names.find(n => n.id === item.id);
                        if (name) {
                            name.romanization = item.romanization;
                        }
                    });
                }
                this.applyFilters();
                utils.showToast(`Generated romanization for ${result.data.processed_count} names`, 'success');
            } else {
                utils.showToast(result.error || 'Failed to generate romanizations', 'error');
            }
        } catch (error) {
            console.error('Bulk romanization error:', error);
            utils.showToast('Network error occurred', 'error');
        } finally {
            utils.showLoading(false);
        }
    }

    async bulkDelete() {
        if (this.selectedNames.size === 0) {
            utils.showToast('Please select names to delete', 'warning');
            return;
        }

        const confirmed = confirm(`Delete ${this.selectedNames.size} selected names? This action cannot be undone.`);
        if (!confirmed) return;

        try {
            utils.showLoading(true);

            const result = await utils.makeApiRequest('api/name-dictionary.php', {
                method: 'DELETE',
                body: JSON.stringify({
                    novel_id: this.novelId,
                    name_ids: Array.from(this.selectedNames)
                })
            });

            if (result.success) {
                // Remove from local data
                this.names = this.names.filter(name => !this.selectedNames.has(name.id));
                this.selectedNames.clear();
                this.updateTotalBadge();
                this.applyFilters();
                utils.showToast(`Deleted ${result.data.deleted_count} names`, 'success');
            } else {
                utils.showToast(result.error || 'Failed to delete names', 'error');
            }
        } catch (error) {
            console.error('Bulk delete error:', error);
            utils.showToast('Network error occurred', 'error');
        } finally {
            utils.showLoading(false);
        }
    }

    async exportData() {
        if (this.names.length === 0) {
            utils.showToast('No data to export', 'warning');
            return;
        }

        try {
            utils.showLoading(true, 'Generating Excel export...');

            // Create a temporary link to trigger the download
            const link = document.createElement('a');
            link.href = `api/excel-export.php?novel_id=${this.novelId}`;
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            utils.showToast('Excel export started. Download should begin shortly.', 'success');

        } catch (error) {
            console.error('Export error:', error);
            utils.showToast('Failed to export data', 'error');
        } finally {
            utils.showLoading(false);
        }
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Add a small delay to ensure all scripts are loaded
    setTimeout(() => {
        try {
            // Check if required dependencies are available
            if (typeof utils === 'undefined') {
                console.error('Utils object not available');
                alert('Error: Required JavaScript utilities not loaded. Please refresh the page.');
                return;
            }

            if (!window.novelId) {
                console.error('Novel ID not available');
                alert('Error: Novel ID not provided. Please check the URL.');
                return;
            }

            window.nameDictionary = new NameDictionaryManager();
        } catch (error) {
            console.error('Failed to initialize NameDictionaryManager:', error);
            alert('Error: Failed to initialize name dictionary. Please refresh the page.');
        }
    }, 100);
});
