<?php
/**
 * Investigation script for novel ID 3, chapter 24 translation issues
 */

require_once 'config/config.php';

echo "=== INVESTIGATING NOVEL ID 3, CHAPTER 24 TRANSLATION ISSUES ===\n";
echo "Date: " . date('Y-m-d H:i:s') . "\n\n";

try {
    $db = Database::getInstance();
    
    // 1. Get basic chapter information
    echo "1. CHAPTER BASIC INFORMATION:\n";
    echo "=" . str_repeat("=", 50) . "\n";
    
    $chapter = $db->fetchOne(
        "SELECT * FROM chapters WHERE novel_id = 3 AND chapter_number = 24",
        []
    );
    
    if (!$chapter) {
        echo "ERROR: Chapter 24 not found for novel ID 3\n";
        exit(1);
    }
    
    echo "Chapter ID: {$chapter['id']}\n";
    echo "Original Title: {$chapter['original_title']}\n";
    echo "Translated Title: {$chapter['translated_title']}\n";
    echo "Translation Status: {$chapter['translation_status']}\n";
    echo "Word Count: {$chapter['word_count']}\n";
    echo "Has Original Content: " . (!empty($chapter['original_content']) ? "Yes" : "No") . "\n";
    echo "Has Translated Content: " . (!empty($chapter['translated_content']) ? "Yes" : "No") . "\n";
    
    if (!empty($chapter['original_content'])) {
        echo "Original Content Length: " . mb_strlen($chapter['original_content']) . " characters\n";
    }
    if (!empty($chapter['translated_content'])) {
        echo "Translated Content Length: " . mb_strlen($chapter['translated_content']) . " characters\n";
    }
    
    // 2. Check for chunks
    echo "\n2. CHUNK INFORMATION:\n";
    echo "=" . str_repeat("=", 50) . "\n";
    
    $chunks = $db->fetchAll(
        "SELECT * FROM chapter_chunks WHERE chapter_id = ? ORDER BY chunk_number",
        [$chapter['id']]
    );
    
    echo "Number of chunks: " . count($chunks) . "\n";
    
    if (!empty($chunks)) {
        foreach ($chunks as $chunk) {
            echo "Chunk {$chunk['chunk_number']}: ";
            echo "Status={$chunk['translation_status']}, ";
            echo "Original=" . mb_strlen($chunk['original_content']) . " chars, ";
            echo "Translated=" . mb_strlen($chunk['translated_content'] ?? '') . " chars\n";
        }
    }
    
    // 3. Get name dictionary for novel 3
    echo "\n3. NAME DICTIONARY FOR NOVEL 3:\n";
    echo "=" . str_repeat("=", 50) . "\n";
    
    $nameDictionary = $db->fetchAll(
        "SELECT * FROM name_dictionary WHERE novel_id = 3 ORDER BY frequency DESC LIMIT 20",
        []
    );
    
    echo "Total names in dictionary: " . count($nameDictionary) . "\n";
    echo "Top 20 most frequent names:\n";
    
    foreach ($nameDictionary as $name) {
        $translation = $name['translation'] ?? $name['romanization'] ?? 'N/A';
        echo "  - {$name['original_name']} → {$translation} (freq: {$name['frequency']}, type: {$name['name_type']})\n";
    }
    
    // 4. Analyze original content for character names with honorifics
    echo "\n4. ORIGINAL CONTENT ANALYSIS:\n";
    echo "=" . str_repeat("=", 50) . "\n";
    
    if (!empty($chapter['original_content'])) {
        // Look for names with -kun suffix
        $kunPattern = '/([ぁ-んァ-ヶ一-龯]{2,6})くん/u';
        preg_match_all($kunPattern, $chapter['original_content'], $kunMatches);
        
        echo "Names with 'くん' (kun) found in original:\n";
        if (!empty($kunMatches[1])) {
            $uniqueKunNames = array_unique($kunMatches[1]);
            foreach ($uniqueKunNames as $name) {
                $count = substr_count($chapter['original_content'], $name . 'くん');
                echo "  - {$name}くん (appears {$count} times)\n";
            }
        } else {
            echo "  No names with くん found\n";
        }
        
        // Look for other honorifics
        $honorifics = ['さん', 'ちゃん', '様', '先生'];
        foreach ($honorifics as $honorific) {
            $pattern = '/([ぁ-んァ-ヶ一-龯]{2,6})' . preg_quote($honorific, '/') . '/u';
            preg_match_all($pattern, $chapter['original_content'], $matches);
            if (!empty($matches[1])) {
                $uniqueNames = array_unique($matches[1]);
                echo "Names with '{$honorific}':\n";
                foreach ($uniqueNames as $name) {
                    $count = substr_count($chapter['original_content'], $name . $honorific);
                    echo "  - {$name}{$honorific} (appears {$count} times)\n";
                }
            }
        }
    }
    
    // 5. Analyze translated content for issues
    echo "\n5. TRANSLATED CONTENT ANALYSIS:\n";
    echo "=" . str_repeat("=", 50) . "\n";
    
    if (!empty($chapter['translated_content'])) {
        // Look for standalone -kun without names
        $standaloneKunPattern = '/(?<![a-zA-Z])-kun(?![a-zA-Z])/';
        preg_match_all($standaloneKunPattern, $chapter['translated_content'], $standaloneMatches);
        
        echo "Standalone '-kun' occurrences: " . count($standaloneMatches[0]) . "\n";
        
        if (count($standaloneMatches[0]) > 0) {
            echo "This indicates character names were lost during translation!\n";
            
            // Show context around each standalone -kun
            $positions = [];
            $offset = 0;
            while (($pos = strpos($chapter['translated_content'], '-kun', $offset)) !== false) {
                $positions[] = $pos;
                $offset = $pos + 1;
            }
            
            echo "\nContext around standalone '-kun' occurrences:\n";
            foreach (array_slice($positions, 0, 5) as $i => $pos) { // Show first 5 occurrences
                $start = max(0, $pos - 50);
                $length = 100;
                $context = substr($chapter['translated_content'], $start, $length);
                echo "  " . ($i + 1) . ". ...{$context}...\n";
            }
        }
        
        // Look for proper name+honorific combinations
        $properNameKunPattern = '/([A-Za-z]+)-kun/';
        preg_match_all($properNameKunPattern, $chapter['translated_content'], $properMatches);
        
        echo "Proper 'Name-kun' combinations: " . count($properMatches[0]) . "\n";
        if (!empty($properMatches[1])) {
            $uniqueProperNames = array_unique($properMatches[1]);
            foreach ($uniqueProperNames as $name) {
                $count = substr_count($chapter['translated_content'], $name . '-kun');
                echo "  - {$name}-kun (appears {$count} times)\n";
            }
        }
    }
    
    // 6. Check translation logs for this chapter
    echo "\n6. TRANSLATION LOGS:\n";
    echo "=" . str_repeat("=", 50) . "\n";
    
    $logs = $db->fetchAll(
        "SELECT * FROM translation_logs WHERE chapter_id = ? ORDER BY created_at DESC LIMIT 5",
        [$chapter['id']]
    );
    
    echo "Recent translation logs: " . count($logs) . "\n";
    foreach ($logs as $log) {
        echo "  - {$log['created_at']}: {$log['status']} ({$log['translation_type']})\n";
        if (!empty($log['error_message'])) {
            echo "    Error: {$log['error_message']}\n";
        }
    }
    
} catch (Exception $e) {
    echo "ERROR: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

echo "\n=== INVESTIGATION COMPLETE ===\n";
?>
