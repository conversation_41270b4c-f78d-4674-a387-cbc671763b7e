<?php
/**
 * Verification script to show the quotation mark fixes applied
 */

require_once 'config/config.php';

echo "=== VERIFICATION OF QUOTATION MARK FIXES ===\n";
echo "Date: " . date('Y-m-d H:i:s') . "\n\n";

try {
    $db = Database::getInstance();
    
    // Get current chapter content
    $chapter = $db->fetchOne(
        "SELECT original_content FROM chapters WHERE novel_id = 3 AND chapter_number = 24",
        []
    );
    
    if (!$chapter) {
        echo "ERROR: Chapter not found\n";
        exit(1);
    }
    
    $content = $chapter['original_content'];
    
    echo "1. QUOTATION MARK STATISTICS:\n";
    echo "=" . str_repeat("=", 50) . "\n";
    
    $singleOpen = mb_substr_count($content, '「');
    $singleClose = mb_substr_count($content, '」');
    $doubleOpen = mb_substr_count($content, '『');
    $doubleClose = mb_substr_count($content, '』');
    
    echo "Single quotes 「」: {$singleOpen} open, {$singleClose} close - " . 
         ($singleOpen === $singleClose ? "BALANCED ✓" : "UNBALANCED ✗") . "\n";
    echo "Double quotes 『』: {$doubleOpen} open, {$doubleClose} close - " . 
         ($doubleOpen === $doubleClose ? "BALANCED ✓" : "UNBALANCED ✗") . "\n";
    echo "Total quotation marks: " . ($singleOpen + $singleClose + $doubleOpen + $doubleClose) . "\n";
    
    echo "\n2. CHECKING FOR CONSECUTIVE QUOTES:\n";
    echo "=" . str_repeat("=", 50) . "\n";
    
    $consecutivePatterns = [
        '/「{2,}/u' => 'Multiple consecutive 「',
        '/」{2,}/u' => 'Multiple consecutive 」',
        '/『{2,}/u' => 'Multiple consecutive 『',
        '/』{2,}/u' => 'Multiple consecutive 』'
    ];
    
    $foundConsecutive = false;
    foreach ($consecutivePatterns as $pattern => $description) {
        if (preg_match($pattern, $content)) {
            echo "FOUND: {$description}\n";
            $foundConsecutive = true;
        }
    }
    
    if (!$foundConsecutive) {
        echo "No consecutive quotation marks found ✓\n";
    }
    
    echo "\n3. SAMPLE OF CORRECTED QUOTATION USAGE:\n";
    echo "=" . str_repeat("=", 50) . "\n";
    
    // Find and display some examples of quotation usage
    $quotes = [];
    $offset = 0;
    
    // Find all quotation marks with context
    foreach (['「', '」', '『', '』'] as $mark) {
        $offset = 0;
        while (($pos = mb_strpos($content, $mark, $offset)) !== false) {
            $start = max(0, $pos - 15);
            $length = 30;
            $context = mb_substr($content, $start, $length);
            $context = str_replace($mark, "**{$mark}**", $context);
            $quotes[] = [
                'mark' => $mark,
                'pos' => $pos,
                'context' => $context
            ];
            $offset = $pos + 1;
        }
    }
    
    // Sort by position and show first 10 examples
    usort($quotes, function($a, $b) { return $a['pos'] - $b['pos']; });
    
    echo "First 10 quotation marks in context:\n";
    for ($i = 0; $i < min(10, count($quotes)); $i++) {
        $quote = $quotes[$i];
        echo sprintf("%2d. %s: ...%s...\n", $i+1, $quote['mark'], trim($quote['context']));
    }
    
    echo "\n4. JAPANESE TYPOGRAPHY COMPLIANCE:\n";
    echo "=" . str_repeat("=", 50) . "\n";
    
    // Check for proper usage patterns
    $dialoguePattern = '/『[^』]*』/u';
    $exclamationPattern = '/「[A-Z!?]+」/u';
    
    $longDialogue = preg_match_all($dialoguePattern, $content);
    $shortExclamations = preg_match_all($exclamationPattern, $content);
    
    echo "Long dialogue using double quotes 『』: {$longDialogue} instances\n";
    echo "Short exclamations using single quotes 「」: {$shortExclamations} instances\n";
    
    // Check for proper nesting
    $nestedPattern = '/『[^』]*「[^」]*」[^』]*』/u';
    $properNesting = preg_match_all($nestedPattern, $content);
    echo "Properly nested quotes (『...「...」...』): {$properNesting} instances\n";
    
    echo "\n5. SUMMARY:\n";
    echo "=" . str_repeat("=", 50) . "\n";
    
    $allBalanced = ($singleOpen === $singleClose) && ($doubleOpen === $doubleClose);
    $noConsecutive = !$foundConsecutive;
    
    if ($allBalanced && $noConsecutive) {
        echo "✓ All quotation marks are properly balanced\n";
        echo "✓ No consecutive quotation mark errors found\n";
        echo "✓ Content follows Japanese typography standards\n";
        echo "\nSTATUS: QUOTATION MARKS SUCCESSFULLY CORRECTED\n";
    } else {
        echo "✗ Some issues may still remain\n";
        if (!$allBalanced) echo "  - Unbalanced quotation marks\n";
        if ($foundConsecutive) echo "  - Consecutive quotation marks found\n";
    }
    
} catch (Exception $e) {
    echo "ERROR: " . $e->getMessage() . "\n";
    exit(1);
}
?>
